using BBB.Core.AssetBundles.Handlers;

namespace BBB.Core.AssetBundles.Loaders
{
    public class StreamingAssetsLoader : BaseLoader
    {
        public StreamingAssetsLoader(BundleLoadingSettings settings) : base(settings)
        {
        }

        public override bool CanExecute(BundleInfo bundleInfo)
        {
            var bundleNameWithHash = Settings.GetFilenameFromBundleInfo(bundleInfo);
            var ret = bundleInfo.BundleType == BundleInfo.BundleTypeLocal;
            // BDebug.Log(LogCat.AssetBundle, $"[StreamingAssetsLoader] CanExecute: {bundleInfo.Bundlename} filename:{bundleNameWithHash} type:{bundleInfo.BundleType} contains:{ret}");
            return ret;
        }

        public override ILoadHandler BuildHandler(BundleInfo bundleInfo, LoadPriority priority)
        {
            return new LocalLoadHandler(bundleInfo, Settings, priority);
        }
    }
}