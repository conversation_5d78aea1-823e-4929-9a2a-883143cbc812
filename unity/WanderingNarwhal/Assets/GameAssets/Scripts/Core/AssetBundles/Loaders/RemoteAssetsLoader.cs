using BBB.Core.AssetBundles.Handlers;
using Core.RPC;

namespace BBB.Core.AssetBundles.Loaders
{
    public class RemoteAssetsLoader : BaseLoader
    {
        public RemoteAssetsLoader(BundleLoadingSettings settings) : base(settings)
        {
            
        }

        public override bool CanExecute(BundleInfo bundleInfo)
        {
            return true;
        }

        public override ILoadHandler BuildHandler(BundleInfo bundleInfo, LoadPriority priority)
        {
            return new RemoteLoadHandler(bundleInfo, Settings, priority);
        }
    }
}