using BBB.Core.AssetBundles.Handlers;

namespace BBB.Core.AssetBundles.Loaders
{
    public abstract class BaseLoader : IBundleLoader
    {
#if BBB_DEBUG
        public string triggeredByAsset { get; set; }
        public string triggeredByBundleDep { get; set; }
#endif

        protected readonly BundleLoadingSettings Settings;

        protected BaseLoader(BundleLoadingSettings settings)
        {
            Settings = settings;
        }

        public abstract bool CanExecute(BundleInfo bundleInfo);

        public abstract ILoadHandler BuildHandler(BundleInfo bundleInfo, LoadPriority priority);
    }
}