using BBB.Core.AssetBundles.Handlers;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace BBB.Core.AssetBundles.Loaders
{
    public class SimulatedLoader : BaseLoader
    {
        private static int _simulateAssetBundleInEditor = -1;
        private readonly string[] _allAssetBundles;
        private const string KSimulateAssetBundles = "SimulateAssetBundles";

        public SimulatedLoader(BundleLoadingSettings settings) : base(settings)
        {
#if UNITY_EDITOR
            _allAssetBundles = AssetDatabase.GetAllAssetBundleNames();
#else
            _allAssetBundles = new string[0];
#endif
        }

        public static bool SimulateAssetBundleInEditor
        {
            get
            {
#if UNITY_EDITOR
                if (_simulateAssetBundleInEditor == -1)
                    _simulateAssetBundleInEditor = EditorPrefs.GetBool(KSimulateAssetBundles, true) ? 1 : 0;

                return _simulateAssetBundleInEditor != 0;
#else
                return false;
#endif
            }
            set
            {
#if UNITY_EDITOR
                var newValue = value ? 1 : 0;
                if (newValue == _simulateAssetBundleInEditor) return;
                
                _simulateAssetBundleInEditor = newValue;
                EditorPrefs.SetBool(KSimulateAssetBundles, value);
#endif
            }
        }

        public override bool CanExecute(BundleInfo bundleInfo)
        {
            if (!SimulateAssetBundleInEditor)
            {
                return false;
            }

            foreach (var bundle in _allAssetBundles)
            {
                if (bundle == bundleInfo.Bundlename)
                {
                    return true;
                }
            }

            return false;
        }

        public override ILoadHandler BuildHandler(BundleInfo bundleInfo, LoadPriority priority)
        {
            return new SimulateEditorHandler(bundleInfo, Settings, priority);
        }
    }
}