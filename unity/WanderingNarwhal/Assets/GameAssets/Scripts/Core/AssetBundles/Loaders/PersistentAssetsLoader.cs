using System.Collections.Generic;
using System.Diagnostics;
using BBB.Core.AssetBundles.Handlers;

namespace BBB.Core.AssetBundles.Loaders
{
    public class PersistentAssetsLoader : BaseLoader
    {
        private readonly HashSet<string> _bundlesInPersistentDataPath;

        public PersistentAssetsLoader(BundleLoadingSettings settings, PersistentBundleManifestProvider persistentManifest) : base(settings)
        {
            _bundlesInPersistentDataPath = persistentManifest.GetAllBundleFilenames();
        }

        public override bool CanExecute(BundleInfo bundleInfo)
        {
#if BBB_DEBUG
            var watch = new Stopwatch();
            watch.Start();
#endif
            var bundleNameWithHash = Settings.GetFilenameFromBundleInfo(bundleInfo);
            var ret = _bundlesInPersistentDataPath.Contains(bundleNameWithHash);

#if BBB_DEBUG
            //AssetBundleManager.RecordAssetBundleLoadTime(bundleInfo.Bundlename, watch.ElapsedMilliseconds, triggeredByAsset, triggeredByBundleDep, "persistent assets loader");
            // BDebug.Log(LogCat.AssetBundle, $"[PersistentAssetsLoader] CanExecute: {bundleInfo.Bundlename} type:{bundleInfo.BundleType} filename:{bundleNameWithHash} contains:{ret}");
            watch.Stop();
#endif
            return ret;
        }

        public override ILoadHandler BuildHandler(BundleInfo bundleInfo, LoadPriority priority)
        {
            return new PersistentDataLoadHandler(bundleInfo, Settings, priority);
        }
    }
}