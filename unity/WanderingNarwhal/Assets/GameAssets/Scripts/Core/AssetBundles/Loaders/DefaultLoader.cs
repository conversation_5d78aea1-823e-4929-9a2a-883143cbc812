using BBB.Core.AssetBundles.Handlers;

namespace BBB.Core.AssetBundles.Loaders
{
    public class DefaultLoader : BaseLoader
    {
        public DefaultLoader(BundleLoadingSettings settings) : base(settings)
        {
        }

        public override bool CanExecute(BundleInfo bundleInfo)
        {
            return true;
        }

        public override ILoadHandler BuildHandler(BundleInfo bundleInfo, LoadPriority priority)
        {
            return new LocalLoadHandler(bundleInfo, Settings, priority);
        }
    }
}