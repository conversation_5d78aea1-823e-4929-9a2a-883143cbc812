using Cysharp.Threading.Tasks;

namespace BBB.Core.AssetBundles.Handlers
{
    public class SimulateEditorHandler : BaseLoadHandler
    {
        private bool _isRunning;

        public SimulateEditorHandler(BundleInfo bundleInfo, BundleLoadingSettings settings,
            LoadPriority priority = LoadPriority.High) : base(bundleInfo, settings, priority)
        {
            _isRunning = false;
        }

        public override bool IsRunning => IsRunning;
        public override float Progress => IsDone ? 1f : 0f;

        public override async void Execute()
        {
#if UNITY_EDITOR
            if (_isRunning) return;
            _isRunning = true;

            //Simulate wait
            await UniTask.DelayFrame(2);

            {
#if BBB_DEBUG
                AssetBundleManager.RecordAssetBundleLoadTime(BundleInfo.Bundlename, 1, triggeredByAsset, triggeredByBundleDep, "simulated handler", 0);
#endif
                Complete( new EditorSimulatedBundle(BundleInfo));
            }
#endif
        }
    }
}