using System;
using System.Diagnostics;
using System.Net;
using BBB.Core.Analytics.TechAnalytics.Managers;
using BBB.Core.Crash;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Core;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.Profiling;
using Debug = UnityEngine.Debug;

namespace BBB.Core.AssetBundles.Handlers
{
    public class RemoteLoadHandler : BaseLoadHandler
    {
        private bool _isRunning;

        private const int NetworkCheckInterval = 1000; //1s
        private const int RequestTimeout = 60; //timeout after 60s and retry instead
        private UnityWebRequest _webRequest;

        public override float Progress => IsDone ? 1f : _webRequest?.downloadProgress ?? 0f;

        public RemoteLoadHandler(BundleInfo bundleInfo, BundleLoadingSettings settings, LoadPriority priority = LoadPriority.High) : base(bundleInfo, settings, priority)
        {
            
        }

        public override bool IsRunning => _isRunning;

        public override void Execute()
        {
            if (_isRunning) return;

            _isRunning = true;

            ExecuteAsync().Forget();
        }

        private async UniTask ExecuteAsync()
        {
#if BBB_DEBUG
            var watch = new Stopwatch();
            watch.Start();
#endif
            var startTs = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
            var url = Settings.GetRemoteUrl(BundleInfo);

            using var uwr = UnityWebRequestAssetBundle.GetAssetBundle(url, BundleInfo.Hash);
            _webRequest = uwr;
            uwr.timeout = RequestTimeout;

            try
            {
                if (!Caching.IsVersionCached(url, BundleInfo.Hash) && !ConnectivityStatusManager.ConnectivityReachable)
                {
                    await UniTask.WhenAny(WaitForConnectivityAsync(NetworkCheckInterval), UniTask.Delay(RequestTimeout * 1000));

                    if (!ConnectivityStatusManager.ConnectivityReachable)
                    {
                        throw new TimeoutException($"Network not reachable for {RequestTimeout}s");  
                    }
                }

                await uwr.SendWebRequest();
            }
            catch (Exception ex)
            {
                LogDownloadError(url, ex, startTs);
                Fail(ex);
                _webRequest = null;
                _isRunning   = false;
                return;
            }

            if (uwr.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError($"[RemoteLoadHandler] Request Bundle: {url}, {uwr.error}");
            }
            else
            {
                LoadedBundle resultBundle = null;
                try
                {
                    Profiler.BeginSample($"RemoteLoadHandler LoadFromMemory [{BundleInfo.Bundlename}]");
                    var assetBundle = DownloadHandlerAssetBundle.GetContent(uwr);
                    resultBundle = new LoadedBundle(BundleInfo, assetBundle);
                }
                catch (Exception e)
                {
                    CrashLoggerService.Log($"AssetBundle Error: {BundleInfo.Bundlename}");
                    Debug.LogException(e);
                }
                finally
                {
                    Profiler.EndSample();
                }

                if (resultBundle != null)
                {
                    NetworkMetricsManager.ReportAssetBundleDownloadRequest(url, startTs, HttpStatusCode.OK.ToInt(), uwr.downloadedBytes);
#if BBB_DEBUG
                    watch.Stop();
                    AssetBundleManager.RecordAssetBundleLoadTime(
                        BundleInfo.Bundlename,
                        watch.ElapsedMilliseconds,
                        triggeredByAsset,
                        triggeredByBundleDep,
                        "remote load handler",
                        0
                    );
                    BDebug.Log(LogCat.AssetBundle, $"[RemoteLoadHandler] remote bundle finish: {BundleInfo.Bundlename}, triggeredByAsset={triggeredByAsset}, triggeredByBundleDep={triggeredByBundleDep}");
#endif
                    Complete(resultBundle);
                }
                else if (!IsDone)
                {
                    var err = new Exception($"Error LoadFromMemory {BundleInfo.Bundlename} from: {url}");
                    Fail(err);
                }
            }

            if (!IsDone)
            {
                Fail(new Exception($"Error downloading {BundleInfo.Bundlename} from: {url}"));
            }

            _webRequest = null;
            _isRunning   = false;
        }
        
        private static async UniTask WaitForConnectivityAsync(int intervalMs)
        {
            while (!ConnectivityStatusManager.ConnectivityReachable)
            {
                await UniTask.Delay(intervalMs);
            }
        }
        
        private void LogDownloadError(string url, Exception ex, DateTimeOffset startTs)
        {
            var msg = $"[RemoteLoadHandler] Error downloading {BundleInfo.Bundlename} from: {url}, {ex.Message}";
            if (ex is TimeoutException or WebException { Status: WebExceptionStatus.Timeout })
            {
                BDebug.LogWarning(LogCat.AssetBundle, msg);
            }
            else
            {
                BDebug.LogError(LogCat.AssetBundle, msg);
            }

            NetworkMetricsManager.ReportAssetBundleDownloadRequest(
                url,
                startTs,
                _webRequest?.responseCode.ToInt() ?? 0,
                _webRequest?.downloadedBytes ?? 0
            );
        }
    }
}