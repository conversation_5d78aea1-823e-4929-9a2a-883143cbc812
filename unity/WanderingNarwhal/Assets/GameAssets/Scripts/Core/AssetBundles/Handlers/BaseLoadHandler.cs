using System;
using Cysharp.Threading.Tasks;

namespace BBB.Core.AssetBundles.Handlers
{
    public abstract class BaseLoadHandler : ILoadHandler
    {
        public string triggeredByAsset { get; set; }
        public string triggeredByBundleDep { get; set; }

        protected readonly BundleInfo BundleInfo;
        protected readonly BundleLoadingSettings Settings;
        private readonly UniTaskCompletionSource<ILoadedBundle> _loadTcs;

        protected ILoadedBundle LoadedBundle;

        public LoadPriority Priority { get; private set; }
        public abstract bool IsRunning { get; }
        public bool IsDone { get; protected set; }
        public abstract float Progress { get; }

        protected BaseLoadHandler(BundleInfo bundleInfo, BundleLoadingSettings settings, LoadPriority priority = LoadPriority.High)
        {
            Priority = priority;
            BundleInfo = bundleInfo;
            Settings = settings;
            _loadTcs = new UniTaskCompletionSource<ILoadedBundle>();
        }

        public abstract void Execute();

        public UniTask<ILoadedBundle> GetHandleAsync() => _loadTcs.Task;
        
        protected void Complete(ILoadedBundle result)
        {
            if (IsDone)
                return;
            
            LoadedBundle = result;
            IsDone = true;
            _loadTcs.TrySetResult(result);
        }
        
        protected void Fail(Exception ex)
        {
            if (IsDone)
                return;
            
            IsDone = true;
            _loadTcs.TrySetException(ex);
        }

        public void UpdatePriority(LoadPriority priority)
        {
            Priority = priority;
        }

        public override int GetHashCode() => BundleInfo.Bundlename.GetHashCode();

        public override string ToString() => BundleInfo.Bundlename;
    }
}