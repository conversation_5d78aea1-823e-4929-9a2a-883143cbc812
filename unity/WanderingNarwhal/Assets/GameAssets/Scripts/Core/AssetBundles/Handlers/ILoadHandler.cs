using Cysharp.Threading.Tasks;

namespace BBB.Core.AssetBundles.Handlers
{
    public interface ILoadHandler
    {
        string triggeredByAsset { get; set; }
        string triggeredByBundleDep { get; set; }

        LoadPriority Priority { get; }
        bool IsRunning { get; }
        bool IsDone { get; }
        public float Progress { get; }
        void Execute();
        UniTask<ILoadedBundle> GetHandleAsync();
        void UpdatePriority(LoadPriority priority);
    }

    public enum LoadPriority
    {
        Immediate = 0,
        High = 1,
        Medium = 2,
        Low = 3,
        End = 4
    }
}