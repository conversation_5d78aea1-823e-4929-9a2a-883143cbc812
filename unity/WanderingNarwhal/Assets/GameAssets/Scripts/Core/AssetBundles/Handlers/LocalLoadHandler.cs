using System;
using System.Diagnostics;
using UnityEngine;
using System.IO;
using System.Net;
using BBB.Core.Analytics.TechAnalytics.Managers;
using BBB.Core.Crash;
using Core.Debug;
using Cysharp.Threading.Tasks;
using UnityEngine.Profiling;
using Debug = UnityEngine.Debug;

namespace BBB.Core.AssetBundles.Handlers
{
    internal abstract class States
    {
        public string BundleNameWithHash;
        public byte[] Content;
    }

    public class LocalLoadHandler : BaseLoadHandler
    {
        private bool _isRunning;

        public LocalLoadHandler(BundleInfo bundleInfo, BundleLoadingSettings settings,
            LoadPriority priority = LoadPriority.High)
            : base(bundleInfo, settings, priority)
        {
        }

        public override bool IsRunning => _isRunning;
        public override float Progress => IsDone ? 1f : _assetBundleCreateRequest?.progress ?? 0f;

        private bool _isWriting = false;
        private AssetBundleCreateRequest _assetBundleCreateRequest;

        public override void Execute()
        {
            if (_isRunning) return;

            _isRunning = true;

            LocalHandlerLoadFromStreamingAssetPathDirectlyAsync().Forget();
        }

        private async UniTask LocalHandlerLoadFromStreamingAssetPathDirectlyAsync()
        {
#if BBB_DEBUG
            Stopwatch watch = new Stopwatch();
            watch.Start();
#endif
            var downloadStartTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
            var url = Settings.GetStreamingAssetUrl(BundleInfo);
            if (AssetBundleManager.Restarting)
            {
                Debug.LogError($"Trying to load AssetBundle when game is restarting: {BundleInfo.Bundlename}");
                CrashLoggerService.Log($"Trying to load AssetBundle when game is restarting: {BundleInfo.Bundlename}");
            }

            _assetBundleCreateRequest = AssetBundle.LoadFromFileAsync(url);
            var assetBundle = await _assetBundleCreateRequest;
            if (assetBundle == null)
            {
                BDebug.LogError(LogCat.AssetBundle, "[LoadFromStreamingAssetPathDirectly] load bundle failed: " + BundleInfo.Bundlename);
                NetworkMetricsManager.ReportAssetBundleDownloadRequest(url, downloadStartTime, HttpStatusCode.BadRequest.ToInt(),0);
            }
            
            long size = 0;
            try
            {
                if (File.Exists(url))
                {
                    size = new FileInfo(url).Length;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError("Bundle size determining exception: " + ex.Message);
            }
#if BBB_DEBUG
            watch.Stop();
            AssetBundleManager.RecordAssetBundleLoadTime(BundleInfo.Bundlename, watch.ElapsedMilliseconds, 
                triggeredByAsset, triggeredByBundleDep, "local - streaming asset", size);
#endif
            
            NetworkMetricsManager.ReportAssetBundleDownloadRequest(url, downloadStartTime, HttpStatusCode.OK.ToInt(), size);
            Complete(new LoadedBundle(BundleInfo, assetBundle));
            _assetBundleCreateRequest = null;
        }
    }
}