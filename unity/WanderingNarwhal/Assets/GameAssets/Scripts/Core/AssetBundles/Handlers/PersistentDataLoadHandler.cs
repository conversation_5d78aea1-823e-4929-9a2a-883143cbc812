using System;
using System.Diagnostics;
using UnityEngine;
using System.IO;
using System.Net;
using BBB.Core.Analytics.TechAnalytics.Managers;
using BBB.Core.Crash;
using Cysharp.Threading.Tasks;
using UnityEngine.Profiling;
using Debug = UnityEngine.Debug;

namespace BBB.Core.AssetBundles.Handlers
{
    
    public class PersistentDataLoadHandler : BaseLoadHandler
    {
        private bool _isRunning;
        private AssetBundleCreateRequest _createRequest;

        public PersistentDataLoadHandler(BundleInfo bundleInfo, BundleLoadingSettings settings,
            LoadPriority priority = LoadPriority.High)
            : base(bundleInfo, settings, priority)
        {
        }

        public override bool IsRunning => _isRunning;
        public override float Progress => IsDone ? 1f : _createRequest?.progress ?? 0f;

        public override void Execute()
        {
            if (_isRunning) return;

            _isRunning = true;

            LoadFromPersistentDataPathAsync().Forget();
        }

        private async UniTask LoadFromPersistentDataPathAsync()
        {
#if BBB_DEBUG
            Stopwatch watch = new Stopwatch();
            watch.Start();
#endif
            var downloadStartTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
            var url = Settings.GetPersistentDataUrl(BundleInfo);
            if (AssetBundleManager.Restarting)
            {
                Debug.LogError($"Trying to load AssetBundle when game is restarting: {BundleInfo.Bundlename}");
                CrashLoggerService.Log($"Trying to load AssetBundle when game is restarting: {BundleInfo.Bundlename}");
            }
            
            try
            {
                _createRequest = AssetBundle.LoadFromFileAsync(url);
                var assetBundle = await _createRequest;
                if (assetBundle == null)
                {
                    NetworkMetricsManager.ReportAssetBundleDownloadRequest(url, downloadStartTime, HttpStatusCode.BadRequest.ToInt(),0);
                }

                long size = 0;
                try
                {
                    if (File.Exists(url))
                    {
                        size = new FileInfo(url).Length;
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError("Bundle size determining exception: " + ex.Message);
                }
#if BBB_DEBUG
                watch.Stop();
                AssetBundleManager.RecordAssetBundleLoadTime(BundleInfo.Bundlename, watch.ElapsedMilliseconds, 
                    triggeredByAsset, triggeredByBundleDep, "persistent load handler", size);
                BDebug.Log(LogCat.AssetBundle,
                    "[PersistentDataLoadHandler] load bundle finish: '" + BundleInfo.Bundlename + "', triggered by asset = '" + triggeredByAsset + "', dependency of bundle = '" +
                    triggeredByBundleDep + "'");
#endif
                NetworkMetricsManager.ReportAssetBundleDownloadRequest(url,downloadStartTime, HttpStatusCode.OK.ToInt(), size);
                Profiler.BeginSample($"LoadFromFile Resolve [{BundleInfo.Bundlename}]");
                Complete(new LoadedBundle(BundleInfo, assetBundle));
                Profiler.EndSample();
            }
            catch (Exception e)
            {
                BDebug.LogError(LogCat.AssetBundle, "[PersistentDataLoadHandler] load bundle failed: " + BundleInfo.Bundlename);
            }
            
            _createRequest = null;
        }
    }
}