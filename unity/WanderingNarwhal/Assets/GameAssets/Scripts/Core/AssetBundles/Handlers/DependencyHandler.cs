using System;
using System.Collections.Generic;
using System.Text;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace BBB.Core.AssetBundles.Handlers
{
    public class DependencyHandler : BaseLoadHandler
    {
        private bool _isRunning;
        private readonly ILoadHandler _handler;
        private readonly List<UniTask<ILoadedBundle>> _tasks;
        private readonly List<string> _bundleNames;
        private readonly IBundleManager _bundleManager;

        public override bool IsRunning => _isRunning;

        public override float Progress
        {
            get
            {
                var total = 0f;
                foreach (var name in _bundleNames)
                {
                    total += _bundleManager.GetBundleLoadingProgress(name);
                }
                
                total += _handler.Progress;
                return total / (_tasks.Count + 1);
            }
        }

        public DependencyHandler(ILoadHandler handler,
            BundleInfo bundleInfo,
            List<UniTask<ILoadedBundle>> tasks,
            List<string> bundleNames,
            BundleLoadingSettings settings,
            IBundleManager bundleManager,
            LoadPriority priority = LoadPriority.High) : base(bundleInfo, settings, priority)
        {
            _handler = handler;
            _tasks = tasks;
            _bundleManager = bundleManager;
            _bundleNames = bundleNames;
        }

        public override void Execute()
        {
            _isRunning = true;
            ExecuteAsync().Forget();
        }
        
        private async UniTask ExecuteAsync()
        {
            try
            {
                var loadedDeps = await UniTask.WhenAll(_tasks);
                
                _handler.Execute();
                
                var bundle = await _handler.GetHandleAsync();

                var dependencies = new List<ILoadedBundle>(loadedDeps);
#if BBB_LOG
                var sb = new StringBuilder();
                foreach (var dep in dependencies)
                {
                    sb.Append(dep.BundleInfo.Bundlename).Append(',');
                }

                if (sb.Length > 0)
                {
                    sb.Length--;
                }
                
                BDebug.LogFormat(LogCat.AssetBundle, $"Dependencies for bundle={BundleInfo.Bundlename} dependencies={sb}");
#endif
                bundle.SetDependencies(dependencies);
                Complete(bundle);
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
                Fail(ex);
            }
            finally
            {
                _isRunning = false;
            }
        }
    }
}