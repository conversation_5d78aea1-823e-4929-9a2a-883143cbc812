using System.IO;
using BBB.Core.AssetBundles.Loaders;
using BebopBee;
using UnityEngine;

namespace BBB.Core.AssetBundles
{
    public static class BundleConstants
    {
        public static string GetPlatformName(RuntimePlatform platform)
        {
            if (AppDefinesConverter.UnityEditor && !SimulatedLoader.SimulateAssetBundleInEditor)
            {
                if (AppDefinesConverter.UnityAndroid)
                {
                    platform = RuntimePlatform.Android;
                }
                else if (AppDefinesConverter.UnityIos)
                {
                    platform = RuntimePlatform.IPhonePlayer;
                }
                else
                {
                    platform = RuntimePlatform.WindowsPlayer;
                }
            }

            switch (platform)
            {
                case RuntimePlatform.Android:
                    return "Android";
                case RuntimePlatform.IPhonePlayer:
                    return "iOS";
                case RuntimePlatform.OSXPlayer:
                    return "OSX";
                case RuntimePlatform.WindowsPlayer:
                    return "Windows";
                case RuntimePlatform.WebGLPlayer:
                    return "WebGL";
                default:
                    break;
            }

            return "";
        }

        public static string GetPlatformName()
        {
            return GetPlatformName(Application.platform);
        }
    }

    public class BundleLoadingSettings
    {
        private readonly string _remoteUrl;
        private readonly string _buildPathUrl;
        private readonly string _buildPath;
        private readonly string _persistentDataPath = Application.persistentDataPath;

        public BundleLoadingSettings(string remoteUrl, string buildPath)
        {
            _remoteUrl = remoteUrl.EndsWith("/") ? remoteUrl.Substring(0, remoteUrl.Length - 1) : remoteUrl;
            // Android uses jar:file:/// as base path for build assets
            if (buildPath.StartsWith("jar"))
            {
                _buildPathUrl = buildPath;
            }
            else
            {
                _buildPathUrl = "file:///" + buildPath;
            }

            _buildPath = buildPath;
        }

        /// <summary>
        /// Returns Bundle URL path using bundle info and platform
        /// Ex: http://bundles.redlion.bebopbee.com/v1/iOS/downloadable/paris/maps/ab_f_paris_[hash].unity3d
        /// </summary>
        /// <param name="bundleInfo"></param>
        /// <returns></returns>
        public string GetRemoteUrl(BundleInfo bundleInfo)
        {
            var platform = BundleConstants.GetPlatformName();
            var fullPath = $"{_remoteUrl}/v{bundleInfo.Version}/{platform}/{bundleInfo.Bundlename}_{bundleInfo.Hash}.unity3d";
            BDebug.Log(LogCat.AssetBundle, $"~~GetRemoteUrl + {bundleInfo.Bundlename} + {fullPath}");
            return fullPath;
        }

        public string GetCachedUrl(BundleInfo bundleInfo)
        {
            var platform = BundleConstants.GetPlatformName();
            var paths = new[] {_buildPathUrl, platform, $"{bundleInfo.Bundlename}_{bundleInfo.Hash}.unity3d"};
            return Path.Combine(paths);
        }

        public string GetStreamingAssetUrl(BundleInfo bundleInfo)
        {
            var platform = BundleConstants.GetPlatformName();
            var paths = new[] {Application.streamingAssetsPath, "Bundles", platform, $"{bundleInfo.Bundlename}_{bundleInfo.Hash}.unity3d"};
            return Path.Combine(paths);
        }

        public string GetFilenameFromBundleInfo(BundleInfo bundleInfo)
        {
            return $"{bundleInfo.Bundlename}_{bundleInfo.Hash}.unity3d";
        }

        public string GetPersistentDataUrl(BundleInfo bundleInfo)
        {
            return GetPersistentPath(GetFilenameFromBundleInfo(bundleInfo));
        }

        public string GetPersistentDataUrl(string bundleNameWithHash)
        {
            return GetPersistentPath(bundleNameWithHash);
        }

        private string GetPersistentPath(string filename)
        {
            var platform = BundleConstants.GetPlatformName();
            var finalPath = Path.Combine(_persistentDataPath, "Bundles", platform, filename);

            return finalPath;
        }
    }
}