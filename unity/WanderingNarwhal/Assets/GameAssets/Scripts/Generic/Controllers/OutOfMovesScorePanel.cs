using BBB;
using UnityEngine;

public class OutOfMovesScorePanel : BbbMonoBehaviour
{
    [SerializeField] private LocalizedTextPro _localizedScoreText;
    [SerializeField] private GameObject _scoreAchieved;

    /// <summary>
    /// Sets up the score panel
    /// </summary>
    public void Setup(int scoreLeft, int scoreToAchieve)
    {
        var scoreAchieved = scoreLeft <= 0;
        _scoreAchieved.SetActive(scoreAchieved);
        _localizedScoreText.gameObject.SetActive(!scoreAchieved);
        _localizedScoreText.FormatSetArgs(scoreLeft, scoreToAchieve);
    }
}
