using System;
using System.Collections;
using BBB.Audio;
using BBB.Core;
using BBB.Core.ResourcesManager;
using UnityEngine;
using TMPro;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using BBB.Map;
using BBB.Match3.Systems;
using BBB.UI;
using BebopBee.Core.Audio;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.Utils;
using UnityEngine.UI;

namespace BBB
{
    public class LevelExitMenuController : BbbMonoBehaviour
    {
        private const string ExitMenuFadeLayerName = "Fade";

        [Header("Exit Menu References")]
        [SerializeField] private GameObject _exitMenuHolder;
        [SerializeField] private Animator _exitMenuAnimator;
        [SerializeField] private AnimationEventCatcher _exitMenuAec;
        [SerializeField] private Button _endGameButton;
        [SerializeField] private SettingsBar _settingsBar;
        [SerializeField] private Button[] _exitMenuCloseButtons;

        [Space] [Header("Level Quit Panel References")]
        [SerializeField] private GameObject _loseLifeMenuHolder;
        [SerializeField] private ModalsSlideDownAnimation _livesLosingAnimator;
        [SerializeField] private Animator _livesLosingMainAnimator;
        [SerializeField] private TextMeshProUGUI _levelHeaderText;
        [SerializeField] private Button _okayButton;
        [SerializeField] private Button[] _loseLifeCloseButtons;
        [SerializeField] private RectTransform _loseLifePanelRectTransform;
        [SerializeField] private PopupStagePaletteApplier _popupStagePaletteApplier;
        [SerializeField] private PaletteApplier _popupPaletteApplier;

        [Space] [Header("Default LoseLife References")]
        [SerializeField] private TextMeshProUGUI _livesCount;
        [SerializeField] private GameObject _losePanelDefaultState;

        [Space] [Header("Infinite LoseLife References")]
        [SerializeField] private UITimerComponent _infLivesTimer;
        [SerializeField] private GameObject _losePanelInfiniteState;

        [Space] [Header("Game Event References")]
        [SerializeField] private GameEventIcon[] _loseGameEventIcons;
        [SerializeField] private TextMeshProUGUI[] _loseGameEventScoreTexts;
        [SerializeField] private GameObject _eventItem;
        [SerializeField] private GameObject _raceEventStreakIcon;
        [SerializeField] private GameObject _discoRushLostIcon;
        [SerializeField] private TextMeshProUGUI _discoRushLostText;
        [SerializeField] private GameObject _eventLossPanel;
        [SerializeField] private GameObject _doubleScoreStreakItem;
        [SerializeField] private TextMeshProUGUI _doubleScoreStreakItemText;
        [SerializeField] private GameObject _butlerStreakItem;
        [SerializeField] private Image _butlerStreakImage;


        [Space] [Header("Settings")]
        [SerializeField] private bool _showLifeCount = false;
        [SerializeField] private float _delayBeforeLifesTextUpdate = 0.5f;
        [SerializeField] private float _delayBeforeModalBeingHiddenOnHeartLose = 1.5f;
        [SerializeField] private Vector2 _losePanelNoEventScale = new Vector2(0, 643f);
        [SerializeField] private Vector2 _losePanelEventScale = new Vector2(0, 840f);

        [HideInInspector] public bool isExitPanelVisible;
        private GameEventMatch3ManagersCollection _gameEventMatch3ManagersCollection;
        private ILivesManager _livesManager;
        private Action _onLeave;
        private bool _shouldShowLifeLose;
        private bool _isInfLives;
        private bool _isRaceEventStreakBroken;
        private bool _isDoubleScoreStreakBroken;
        private int _highestMultiplierStreakBroken;
        private bool _isButlerStreakBroken;
        private int _discoRushLostScore;
        private GameController _gameController;
        private Match3SimulationPlayer _match3SimulationPlayer;
        private TimeManager _timeManager;
        private IEventDispatcher _eventDispatcher;
        private IGameEventResourceManager _gameEventResourceManager;
        private IButlerGiftManager _butlerGiftManager;
        private IAssetsManager _assetsManager;

        private ILevel _level;
        private static readonly int Hide = Animator.StringToHash("Hide");
        private static readonly int Lose = Animator.StringToHash("Lose");

        public void Init(IContext context, bool shouldShowLifeLose, bool isInfiniteLives,
            bool isRaceEventStreakBroken, bool isDoubleScoreStreakBroken, int discoRushLostScore, bool isButlerStreakBroken,
            int highestMultiplierScoreBroken, ILevel level, Action onLeave = null)
        {
            _livesManager = context.Resolve<ILivesManager>();
            _gameController = context.Resolve<GameController>();
            _match3SimulationPlayer = context.Resolve<Match3SimulationPlayer>();
            _gameEventMatch3ManagersCollection = context.Resolve<GameEventMatch3ManagersCollection>();
            _timeManager = context.Resolve<TimeManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _butlerGiftManager = context.Resolve<IButlerGiftManager>();
            _assetsManager = context.Resolve<IAssetsManager>();

            _settingsBar.Init(context);
            _isInfLives = isInfiniteLives;
            _onLeave = onLeave;
            _shouldShowLifeLose = shouldShowLifeLose;
            _isRaceEventStreakBroken = isRaceEventStreakBroken;
            _isDoubleScoreStreakBroken = isDoubleScoreStreakBroken;
            _highestMultiplierStreakBroken = highestMultiplierScoreBroken;
            _discoRushLostScore = discoRushLostScore;
            _isButlerStreakBroken = isButlerStreakBroken;
            _level = level;
            _livesLosingMainAnimator.ResetAllParameters();
            _livesLosingMainAnimator.Rebind();
            _exitMenuAnimator.Rebind();
            _exitMenuHolder.gameObject.SetActive(false);
            _okayButton.interactable = false;
            foreach (var button in _loseLifeCloseButtons)
            {
                button.interactable = true;
            }
        }

        protected override void OnDisable()
        {
            _exitMenuHolder.SetActive(false);
            _loseLifeMenuHolder.SetActive(false);
            isExitPanelVisible = false;
        }

        public void ResetOnRetry(ILevel level)
        {
            _exitMenuAnimator.Rebind();
            _exitMenuHolder.SetActive(false);
            _loseLifeMenuHolder.SetActive(false);
        }

        private void Subscribe()
        {
            Unsubscribe();

            _exitMenuCloseButtons.ReplaceOnClick(() => HideExitMenu());
            _eventDispatcher.AddListener<InfiniteLivesPeriodEndEvent>(InfiniteLivesPeriodEnded);

            if (_shouldShowLifeLose)
            {
                _endGameButton.ReplaceOnClick(ShowLoseLifePanel);
            }
            else
            {
                _endGameButton.ReplaceOnClick(ExitWithoutLose);
            }

            _loseLifeCloseButtons.ReplaceOnClick(HideLoseLife);
            _okayButton.ReplaceOnClick(OnGameLeave);
            _exitMenuAec.AnimationEvent += DisableExitMenu;
        }

        private void Unsubscribe()
        {
            _exitMenuCloseButtons?.RemoveOnClickListeners();
            _endGameButton?.RemoveOnClickListeners();
            _loseLifeCloseButtons?.RemoveOnClickListeners();
            _okayButton?.RemoveOnClickListeners();

            if (_exitMenuAec != null)
            {
                _exitMenuAec.AnimationEvent -= DisableExitMenu;
            }
            _eventDispatcher?.RemoveListener<InfiniteLivesPeriodEndEvent>(InfiniteLivesPeriodEnded);
        }

        private void OnGameLeave()
        {
            if (_isInfLives)
            {
                Unsubscribe();
                _gameController.GameEnded = true;
                _onLeave();
            }
            else
            {
                StartCoroutine(ShowLoseAnimationCoroutine());
            }
        }

        public void ShowExitMenu()
        {
            isExitPanelVisible = true;
            Subscribe();
            _exitMenuHolder.SetActive(true);
            HedgehogTeam.EasyTouch.EasyTouch.SetUICompatibily(true);
        }

        private void HideExitMenu(bool withScrimFade = false)
        {
            _butlerStreakImage.sprite = null;
            _exitMenuAnimator.SetTrigger(Hide);
            _exitMenuAnimator.SetLayerWeight(_exitMenuAnimator.GetLayerIndex(ExitMenuFadeLayerName), withScrimFade ? 1 : 0);
        }

        private void DisableExitMenu()
        {
            _exitMenuHolder.SetActive(false);
            StartCoroutine(DelayAction(3f));
            HedgehogTeam.EasyTouch.EasyTouch.SetUICompatibily(_loseLifeMenuHolder.activeSelf ||
                                                              _exitMenuHolder.activeSelf);
        }

        private IEnumerator DelayAction(float time)
        {
            yield return WaitCache.Seconds(time);
            isExitPanelVisible = false;
        }

        private void ExitWithoutLose()
        {
            _match3SimulationPlayer.Complete();
            HideExitMenu();
            Unsubscribe();
            _onLeave();
        }

        private void InfiniteLivesPeriodEnded(InfiniteLivesPeriodEndEvent ev)
        {
            _losePanelInfiniteState.SetActive(false);
            _isInfLives = false;
            SetupLoseLife();
        }

        private void ShowLoseLifePanel()
        {
            HideExitMenu(true);
            _okayButton.interactable = true;
            _eventLossPanel.SetActive(false);
            _losePanelInfiniteState.SetActive(false);
            _losePanelDefaultState.SetActive(false);

            var match3Manager = _gameEventMatch3ManagersCollection.GetFirstManager(manager => manager is SideMapEventMatch3Manager);
            
            if (match3Manager != null)
            {
                var currentEventUid = match3Manager.ActiveGameEventUid;
                var eventPalette = _gameEventResourceManager.GetSettings(currentEventUid).Palette;
                if (eventPalette != null)
                    _popupPaletteApplier.Apply(eventPalette);
                else
                    _popupStagePaletteApplier.Apply(Stage.Good);
            }
            else
            {
                _popupStagePaletteApplier.Apply(_level.Config.GetPaletteStage());
            }

            _loseLifePanelRectTransform.sizeDelta = _losePanelNoEventScale;
            if (_isInfLives)
            {
                _losePanelInfiniteState.SetActive(true);
                _infLivesTimer.SetupTime(_timeManager, _livesManager.TimeTillInfiniteLivesEnd);
            }
            else
            {
                SetupLoseLife();
            }

            if (CheckUpdateEventScore())
            {
                _eventLossPanel.SetActive(true);
                _loseLifePanelRectTransform.sizeDelta = _losePanelEventScale;
            }

            _loseLifeMenuHolder.SetActive(true);
        }

        private bool CheckUpdateEventScore()
        {
            var match3Manager = _gameEventMatch3ManagersCollection.GetFirstManager(manager => manager is SideMapEventMatch3Manager);
            var scoreWouldBeLost = match3Manager?.ScoresThatWouldBeLost ?? 0;
            var triggerValue = match3Manager?.ActiveGameEvent?.GetGoalToNextMilestone() ?? 0;
            var shouldShowEventIcon = scoreWouldBeLost > triggerValue;
            if (!shouldShowEventIcon && !_isRaceEventStreakBroken &&
                _discoRushLostScore <= 0 && !_isDoubleScoreStreakBroken && !_isButlerStreakBroken) return false;
            _eventItem.SetActive(shouldShowEventIcon);
            foreach (var icon in _loseGameEventIcons)
            {
                if (icon != null)
                {
                    icon.Refresh();
                }
            }

            foreach (var scoreText in _loseGameEventScoreTexts)
            {
                if (scoreText != null)
                {
                    scoreText.text = scoreWouldBeLost.ToString();
                }
            }
            
            _raceEventStreakIcon.SetActive(_isRaceEventStreakBroken);
            _discoRushLostText.text = _discoRushLostScore.ToString();
            _discoRushLostIcon.SetActive(_discoRushLostScore > 0);
            _doubleScoreStreakItemText.text = _highestMultiplierStreakBroken + "x";
            _doubleScoreStreakItem.SetActive(_isDoubleScoreStreakBroken);
            _butlerStreakItem.SetActive(false);
            if (_isButlerStreakBroken)
            {
                SetButlerGiftSprite();
            }

            return true;
        }

        private void SetupLoseLife()
        {
            HedgehogTeam.EasyTouch.EasyTouch.SetUICompatibily(true);
            AudioProxy.PlaySound(GenericSoundIds.LevelFailPopupAppearing);

            // We need to show NumLives+1 because 1 life is subtracted from player right after first move.
            // If player will not quit and successfully complete the level, then life will be added back silently.
            _livesCount.text = _showLifeCount ? (_livesManager.NumberOfLives + 1).ToString() : "-1";
            _losePanelDefaultState.SetActive(true);
        }

        private void HideLoseLife()
        {
            _livesLosingAnimator.Hide(DisableLoseLife);
        }

        private void DisableLoseLife()
        {
            _loseLifeMenuHolder.SetActive(false);
            HedgehogTeam.EasyTouch.EasyTouch.SetUICompatibily(_loseLifeMenuHolder.activeSelf ||
                                                              _exitMenuHolder.activeSelf);
        }

        private IEnumerator ShowLoseAnimationCoroutine()
        {
            foreach (var button in _loseLifeCloseButtons)
            {
                button.interactable = false;
            }
            _okayButton.interactable = false;
            _livesLosingMainAnimator.SetTrigger(Lose);
            AudioProxy.PlaySound(Match3SoundIds.LifeLost);
            // We don't spend life here, because it is already spent after first move.

            yield return WaitCache.Seconds(_delayBeforeLifesTextUpdate);

            _livesCount.text = _livesManager.NumberOfLives.ToString();

            yield return WaitCache.Seconds(_delayBeforeModalBeingHiddenOnHeartLose);
            _livesLosingMainAnimator.ResetTrigger(Lose);
            _livesLosingAnimator.Hide(() =>
            {
                _gameController.GameEnded = true;
                DisableLoseLife();
                Unsubscribe();
                _onLeave();
            });
        }

        private async void SetButlerGiftSprite()
        {
            var butlerStreakIcon = $"FennecPerks_{_butlerGiftManager.CurrentStreak}_OOM_Icon";

            var assetLoaded = await _assetsManager.LoadAsync<Sprite>(butlerStreakIcon);
            
            if (assetLoaded != null && assetLoaded.Get() != null)
            {
                _butlerStreakImage.sprite = assetLoaded.Get();
                _butlerStreakItem.SetActive(true);
            }
            else
            {
                BDebug.LogError(LogCat.Match3, $"Sprite for {butlerStreakIcon} not found");
            }
        }
        
        protected override void OnDestroy()
        {
            base.OnDestroy();
            Unsubscribe();
            _butlerStreakImage.sprite = null;
        }
    }
}