using System;
using BBB.Core;
using BBB.UI.Level.Views;

namespace BBB.UI.Level.Controllers
{
    public class GameEventLoseLevelWarningController : BaseModalsController<IGameEventLoseLevelWarningViewPresenter>
    {
        private Action _onBack;
        private Action _onExit;
        private int _scoreWillBeLost;
        private bool _isOutOfMoves;
        private bool _competitionProgressLost;

        public void Setup(int scoreWillBeLost, bool competitionProgressLost, bool isOutOfMoves, Action onExitClick, Action onBackClick)
        {
            _onBack = onBackClick;
            _onExit = onExitClick;
            _scoreWillBeLost = scoreWillBeLost;
            _competitionProgressLost = competitionProgressLost;
            _isOutOfMoves = isOutOfMoves;
        }

        protected override void OnShow()
        {
            base.OnShow();
            View.Setup(_scoreWillBeLost, _competitionProgressLost, _isOutOfMoves, OnExitClicked);
            View.OnCloseClicked -= OnModalClosed;
            View.OnCloseClicked += OnModalClosed;
        }

        private void OnModalClosed()
        {
            _onBack?.Invoke();
            _onBack = null;
            _onExit = null;
        }

        private void OnExitClicked()
        {
            _onExit?.Invoke();
            _onBack = null;
            _onExit = null;
        }
    }
}