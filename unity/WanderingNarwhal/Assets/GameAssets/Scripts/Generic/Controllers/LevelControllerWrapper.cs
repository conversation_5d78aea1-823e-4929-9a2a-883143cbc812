using BBB.Core;
using BBB.UI.Level.Views;

namespace BBB.UI.Level
{
    public class LevelControllerWrapper : BaseScreensController<ILevelViewPresenter>
    {
        protected override void OnShow()
        {
            base.OnShow();
        }

        protected override void OnHide()
        {
            base.OnHide();
        }

        public override bool IsReady()
        {
            return base.IsReady() && View.IsLevelLoaded();
        }

        public override bool IsInSameGroup(ScreenType screenType)
        {
            return (screenType & ScreenType.Levels) > 0;
        }
    }
}