using System;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Core;
using BBB.DI;
using BBB.EndGameEvents;
using BBB.Social;
using BebopBee.Core.Audio;
using Core.Configs;
using FBConfig;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Generic.Carrots;
using GameAssets.Scripts.GlobeModal;
using UnityEngine;

namespace BBB.UI.Level.Controllers
{
    public class LevelSuccessController : BaseModalsController<ILevelSuccessViewPresenter>
    {
        private static readonly Type[] RequiredConfigs =
        {
            typeof(SystemConfig)
        };

        private readonly List<Match3CharacterAnim> _fennecAnimations = new();

        private ChallengeTriviaManager _challengeTriviaManager;
        private GameEventMatch3ManagersCollection _managersCollection;

        private Action<GameObject> _onShow;
        private Action<GameObject> _onHide;

        private bool _showDidiOnHide;
        private bool _goButtonClicked;

        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);
            _challengeTriviaManager = context.Resolve<ChallengeTriviaManager>();

            SetupSystemConfig(context.Resolve<IConfig>());
            Config.OnConfigUpdated -= SetupSystemConfig;
            Config.OnConfigUpdated += SetupSystemConfig;
        }

        private void SetupSystemConfig(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            var systemConfig = config.TryGetDefaultFromDictionary<SystemConfig>();

            _fennecAnimations.Clear();

            for (var i = 0; i < systemConfig.LevelSuccessFennecAnimationsLength; i++)
            {
                if (Enum.TryParse(systemConfig.LevelSuccessFennecAnimations(i), out Match3CharacterAnim anim))
                {
                    _fennecAnimations.Add(anim);
                }
            }
        }

        public void Setup(GameEventMatch3ManagersCollection managersCollection, IRaceEventMatch3Manager raceEventMatch3Manager,
            Match3ResourceProvider resourceProvider, CurrencyIconsLoader iconsLoader, ILevel level, int score,
            int initialStage, bool canSendChallenge, CarrotsData carrotsData, LevelSuccessAdData levelSuccessAdData,
            Action<GameObject> onShow, Action<GameObject> onHide)
        {
            _onShow = onShow;
            _onHide = onHide;

            _managersCollection = managersCollection;

            if (canSendChallenge)
            {
                // preloading to avoid frame glitch
                ModalsBuilder.CreateModalView<GlobeModalController>(ModalsType.GlobeModal);
            }

            if (IsReady())
            {
                SetupAction();
            }
            else
            {
                DoWhenReady(SetupAction);
            }

            return;

            void SetupAction()
            {
                View.Setup(_managersCollection, raceEventMatch3Manager, resourceProvider, iconsLoader, level, score,
                    initialStage, canSendChallenge, levelSuccessAdData, _fennecAnimations, carrotsData);
            }
        }

        protected override void OnShow()
        {
            _onShow((View as MonoBehaviour)?.gameObject);
            _goButtonClicked = false;
            _showDidiOnHide = false;
            Subscribe();
            base.OnShow();
        }

        private void Subscribe()
        {
            Unsubscribe();
            View.OnExitButtonClicked += OnGoButtonClicked;
            View.OnCloseClicked += OnGoButtonClicked;

            View.OnShowDidiEvent += OnShowDidiHandler;
            View.OnHidden += OnHidden;
        }

        private void Unsubscribe()
        {
            View.OnExitButtonClicked -= OnGoButtonClicked;
            View.OnCloseClicked -= OnGoButtonClicked;

            View.OnShowDidiEvent -= OnShowDidiHandler;
            View.OnHidden -= OnHidden;
        }

        private void OnHidden()
        {
            Unsubscribe();
        }

        private void OnShowDidiHandler()
        {
            _showDidiOnHide = true;
        }

        protected override void OnHide()
        {
            _onHide.SafeInvoke((View as MonoBehaviour)?.gameObject);
            base.OnHide();
            _onShow = null;
            _onHide = null;
        }

        protected override void PlayOpenningSound()
        {
            AudioProxy.PlaySound(GenericSoundIds.LevelSuccessPopupAppearing);
        }

        private void OnGoButtonClicked()
        {
            if (_goButtonClicked)
                return;

            _goButtonClicked = true;
            HideModal();

            if (_showDidiOnHide && _challengeTriviaManager.TryShowGlobeModal())
            {
                ModalsView.ImmediateHide();
            }
            else
            {
                ShowMapScreen();
            }

            _showDidiOnHide = false;
        }

        private void ShowMapScreen()
        {
            var sideMapEventActive =
                _managersCollection.GetFirstManager(manager => manager.ActiveGameEvent is SideMapGameEvent
                {
                    Status: GameEventStatus.Active
                }) != null;

            switch (ScreensBuilder.CurrentScreenType)
            {
                case ScreenType.SideMapLevelScreen when sideMapEventActive:
                    LoadingProcessTracker.LogShowScreen(ScreenType.SideMapScreen.ToString(), ScreensManager.GetTrackingPreviousScreenType(), "LevelSuccessPlay");
                    ScreensBuilder.ShowScreen(ScreenType.SideMapScreen);
                    break;
                default:
                    LoadingProcessTracker.LogShowScreen(ScreenType.EpisodeScreen.ToString(), ScreensManager.GetTrackingPreviousScreenType(), "LevelSuccessPlay");
                    ScreensBuilder.ShowScreen(ScreenType.EpisodeScreen);
                    break;
            }
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            Config.OnConfigUpdated -= SetupSystemConfig;
        }
    }
}