#if UNITY_EDITOR
using BBB.Core;
using System;
using System.Collections.Generic;
using System.Text;
using BBB.Audio;
using BBB.ContentManagement;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using BBB.Leanplum;
using BBB.M3Editor;
using BBB.Match3;
using BBB.Match3.Renderer;
using BBB.Match3.Systems;
using BBB.MMVibrations;
using BBB.Modals;
using BBB.Navigation;
using BBB.RaceEvents;
using BBB.TeamEvents;
using BBB.Testers.Mocks;
using BBB.UI.Core;
using BBB.UI.Level;
using BBB.UI.Level.Match3BoostProgressProvider;
using BBB.UI.Level.Match3ConfigProvider;
using Bebopbee.Core.Extensions.Unity;
using BebopBee;
using BebopBee.Core.Audio;
using Core.Configs;
using Core.Debug;
using GameAssets.Scripts.Generic;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Theme;
using UI;
using UnityEngine;
using Bebopbee.Core.Systems.RpcCommandManager;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.Messages;
using GameAssets.Scripts.Tutorial.Core;
using GameAssets.Scripts.UI.OverlayDialog;
using GameAssets.Scripts.Utils;

namespace BBB.UI
{
    public interface IConfigRefresher
    {
        void RefreshConfig(ILevel level, Action callback);
    }

    public class Match3EditorContext : UnityContext
    {
        public Match3EditorContext(bool copyResolvers) : base(copyResolvers)
        {

        }
    }

    public partial class EditorLevelControllerLauncher : BbbMonoBehaviour, ICoroutineExecutor, IConfigRefresher, IUpdateDispatcher
    {
        [Serializable]
        public struct FakeBooster
        {
            public string id;
            public int count;
            public bool active;
        }

        public string DefaultPath { get; private set; }

        public M3Editor.M3Editor M3Editor => _m3Editor;

        public event Action OnUpdate;
        public event Action OnFixedUpdate;
        public event Action OnLateUpdate;

        [SerializeField] private Transform _levelHolder;
        [SerializeField] private M3EditorExceptionPopup _exceptionPopup;
        [SerializeField] private M3EditorHistogramPopup _histogramPopup;
        [SerializeField] private M3EditorProgressPopup _progressPopup;
        [SerializeField] private M3EditorLevelSelectionPopup _levelSelectionPopup;
        [SerializeField] private M3EditorAiEditPopup _aiEditPopup;
        [SerializeField] private M3EditorTestLevelsPopup _testLevelsPopup;
        [SerializeField] private M3EditorSpawnerSettingsPopup _spawnerSettingsPopup;
        [SerializeField] private M3EditorIntegrationTestsPopup _integrationTestsPopup;
        [SerializeField] private M3EditorReplayPopup _replayPopupPopup;
        [SerializeField] private M3EditorLevelGoalsOverridePopup _goalsOverridePopup;
        [SerializeField] private List<FakeBooster> _fakeBoosters;
        [SerializeField] private GameObject _levelCanvasPrefab;
        [SerializeField] private Camera _camera;
        [SerializeField] private List<GameObject> _editorPrefabs;
        [SerializeField] private ImageContentLoadSystem _imageContentLoadSystem;
        [SerializeField] private M3Editor.M3Editor _m3Editor;
        [SerializeField] private SpawnerSettingsContainer _spawnerSettingsContainer;

        private Match3EditorResourceProvider _resourceProvider;
        private IRPCService _rpcService;
        private M3EditorConfigLoader _editorConfigProvider;
        private ILocalizationManager _localizationManager;
        private IConfig _config;
        private M3EditorLevelFileToConfigMap _fileToConfigMap;
        private IEventDispatcher _dispatcher;
        private UnityContext _awakeContext;
        private SpawnerSettingsManager _spawnerSettingsManager;
        private ProtobufSerializer _protoSerializer;
        private LevelsOrderingManager _levelOrderingManager;

        public Transform LevelHolder { get { return _levelHolder; } }

        public M3EditorSpawnerSettingsPopup SpawnerSettingsPopup => _spawnerSettingsPopup;

        private async void Awake()
        {
            _protoSerializer = new ProtobufSerializer();
            LevelBinarySerializer.SetWrapper(_protoSerializer);
            UniRx.MainThreadDispatcher.Initialize();
            BDebug.Initialize();
            RandomSystem.Reset((uint)(Time.time * 1000f));
            _dispatcher = new MessageDispatcher();
            DefaultPath = Application.dataPath + "/Levels/main_1/level_01_01" + LevelHelper.LEVELS_DOT_EXT;
            var firstLevelPath = DefaultPath;

            if (!M3SaveLoadUtility.LastEditedLevelPath.IsNullOrEmpty())
            {
                firstLevelPath = M3SaveLoadUtility.LastEditedLevelPath;
            }

            GameAssets.Scripts.Player.Level level = null;
            try
            {
                M3SaveLoadUtility.LoadLevelFromFile(firstLevelPath, out level);
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }

            if (level == null && firstLevelPath != DefaultPath)
            {
                M3SaveLoadUtility.LoadLevelFromFile(DefaultPath, out level);
            }

            if (level == null)
            {
                M3SaveLoadUtility.LastEditedLevelPath = "";
            }

            _resourceProvider = new Match3EditorResourceProvider();
            foreach (var prefab in _editorPrefabs)
                _resourceProvider.AddEditorPrefab(prefab);

            RPCService.SetServerUrl(GameConstants.RPCServerURL);
            _rpcService = gameObject.AddComponent<RPCService>();
            _editorConfigProvider = new M3EditorConfigLoader();
            _localizationManager = new LocalizationManager(this);

            var logStopWatch = new LogStopwatch();
            logStopWatch.Start();

            _awakeContext = new Match3EditorContext(true);

            _awakeContext.AddServiceToRegister<IAssetsManager>(_resourceProvider.AssetsManager);
            _awakeContext.AddServiceToRegister<ImageContentCacheSystem>(new ImageContentCacheSystem());
            _awakeContext.AddServiceToRegister<ImageContentLoadSystem>(_imageContentLoadSystem);
            _awakeContext.AddServiceToRegister<ICoroutineExecutor>(this);
            _awakeContext.AddServiceToRegister<IUpdateDispatcher>(this);
            _awakeContext.AddServiceToRegister<IGameEventManager>(new GameEventManagerStub());
            var resourceCacheHandler = new ResourceCache(_resourceProvider.AssetsManager);
            _awakeContext.AddServiceToRegister<IGameEventResourceManager>(new GameEventResourceManagerStub());
            _awakeContext.AddServiceToRegister<IResourceCacheHandle>(resourceCacheHandler);
            _awakeContext.AddServiceToRegister<GenericResourceProvider>(new GenericResourceProvider());
            _awakeContext.AddServiceToRegister<ITimeController>(new TimeManager());
            _awakeContext.AddServiceToRegister<WeeklyLeaderboardManager>(new WeeklyLeaderboardManager());

            resourceCacheHandler.BeginCachingPersistentResources();
            var priorityResourceProvider = new PriorityResourceProvider();
            priorityResourceProvider.CacheResources(resourceCacheHandler);
            await resourceCacheHandler.ReloadAllAsync();
            _awakeContext.AddServiceToRegister<PriorityResourceProvider>(priorityResourceProvider);
            _awakeContext.RegisterContext(null);

            ContextedUiBehaviour.SetupRootContext(_awakeContext);

            _spawnerSettingsManager = new SpawnerSettingsManager();
            _spawnerSettingsManager.SetGlobalSpawners(_spawnerSettingsContainer);

            await _resourceProvider.LoadAsync();
            var mixers = _resourceProvider.Mixers;

            var audioPlayer = StandardAudioPlayer.Create(mixers, new StandardLogger());
            AudioProxy.SetInstances(audioPlayer, audioPlayer, audioPlayer);

            logStopWatch.StopLogRestart("M3 Editor resources loaded");

            AudioProxy.AddContext(_resourceProvider.GetAudioContext(GenericResKeys.GenericSoundsContext));
            AudioProxy.AddContext(_resourceProvider.GetAudioContext(GenericResKeys.GenericVoiceoverContext));

            _editorConfigProvider.LoadAsync(_rpcService, conf =>
            {
                try
                {
                    logStopWatch.StopLogRestart("M3 Editor remote config loaded");

                    _levelOrderingManager = new LevelsOrderingManager();
                    _levelOrderingManager.SetupConfig(conf);
                    _localizationManager.SetConfig(conf);

                    _config = conf;
                    _fileToConfigMap = new M3EditorLevelFileToConfigMap(conf.Get<FBConfig.ProgressionLevelConfig>());
                    if (!_fileToConfigMap.HasRemoteDataForFile(M3SaveLoadUtility.LastEditedLevelPath))
                    {
                        M3SaveLoadUtility.LastEditedLevelPath = Application.dataPath + "/Levels/" +
                                                                BBB.GameAssets.Scripts.Player.Level.GetLevelFullName(
                                                                    _fileToConfigMap.FirstLevelConfig, 0);
                    }

                    level?.SetupRemoteData(_fileToConfigMap.GetRemoteDataForFile(M3SaveLoadUtility.LastEditedLevelPath),
                        _spawnerSettingsManager.SpawnerSettings);
                    logStopWatch.StopLog("M3 Editor remote config set");
                    Run(level);
                }
                catch (Exception e)
                {
                    Debug.LogException(e);
                    throw;
                }
            }).Forget();
        }


        void IConfigRefresher.RefreshConfig(ILevel level, Action callback)
        {
            _editorConfigProvider.LoadAsync(_rpcService, conf =>
            {
                try
                {
                    _localizationManager.SetConfig(conf);
                    _fileToConfigMap = new M3EditorLevelFileToConfigMap(conf.Get<FBConfig.ProgressionLevelConfig>());
                    callback.SafeInvoke();
                    level.SetupRemoteData(_fileToConfigMap.GetRemoteDataForFile(M3SaveLoadUtility.LastEditedLevelPath), _spawnerSettingsManager.SpawnerSettings);
                }
                catch (Exception e)
                {
                    Debug.LogException(e);
                    throw;
                }
            }).Forget();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            _dispatcher.Dispose();
        }

        public void HardRestart(ILevel level)
        {
            Run(level);
        }

        private void Run(ILevel level)
        {
            var debugContext = new Match3EditorContext(true);

            debugContext.AddServiceToRegister<Match3ResourceHelper>(new Match3ResourceHelper(true));
            debugContext.AddServiceToRegister<M3EditorLevelFileToConfigMap>(_fileToConfigMap);
            debugContext.AddServiceToRegister<IConfig>(_config);
            debugContext.AddServiceToRegister<LevelHolder>(new LevelHolder());
            var playerManager = new PlayerManagerStub(_fakeBoosters);
            playerManager.SetCurrentLevel(level);
            debugContext.AddServiceToRegister<IPlayerManager>(playerManager);
            debugContext.AddServiceToRegister<ILevelsOrderingManager>(_levelOrderingManager);
            debugContext.AddServiceToRegister<IMatch3BoostProgressProvider>(new M3EditorBoostProgressProvider());
            debugContext.AddServiceToRegister<ILocalizationManager>(_localizationManager);
            debugContext.AddServiceToRegister<IMatch3SharedResourceProvider>(_resourceProvider);
            debugContext.AddServiceToRegister<Match3EditorResourceProvider>(_resourceProvider);

            debugContext.AddServiceToRegister<M3EditorExceptionPopup>(_exceptionPopup);
            debugContext.AddServiceToRegister<M3EditorHistogramPopup>(_histogramPopup);
            _histogramPopup.M3Editor = _m3Editor;
            
            debugContext.AddServiceToRegister<M3EditorProgressPopup>(_progressPopup);
            debugContext.AddServiceToRegister<M3EditorLevelSelectionPopup>(_levelSelectionPopup);
            debugContext.AddServiceToRegister<M3EditorAiEditPopup>(_aiEditPopup);
            debugContext.AddServiceToRegister<M3EditorTestLevelsPopup>(_testLevelsPopup);

            debugContext.AddServiceToRegister<M3EditorIntegrationTestsPopup>(_integrationTestsPopup);
            _integrationTestsPopup.M3Editor = _m3Editor;

            debugContext.AddServiceToRegister<M3EditorReplayPopup>(_replayPopupPopup);
            _replayPopupPopup.M3Editor = _m3Editor;
            
            debugContext.AddServiceToRegister<M3EditorLevelGoalsOverridePopup>(_goalsOverridePopup);

            var screenController = new StubScreensManager(_levelHolder);
            debugContext.AddServiceToRegister<IScreensManager>(screenController);

            debugContext.AddServiceToRegister<Camera>(_camera, Match3Constants.LevelCameraTag);
            debugContext.AddServiceToRegister<ILevelRevealer>(new LevelRevealerStub());
            debugContext.AddServiceToRegister<ILevelSkipper>(new LevelSkipperStub());
            debugContext.AddServiceToRegister<IAccountManager>(new AccountManagerStub());
            debugContext.AddServiceToRegister<ILivesManager>(new LivesManagerStub());
            debugContext.AddServiceToRegister<IEventDispatcher>(_dispatcher);
            debugContext.AddServiceToRegister<ILockManager>(new LockManagerStub());
            debugContext.AddServiceToRegister<IModalsBuilder>(new ModalsBuilderStub());
            debugContext.AddServiceToRegister<ILevelScoreAcceptor>(new StubLevelScoreAcceptor());
            debugContext.AddServiceToRegister<ITutorialPlaybackController>(new TutorialPlaybackControllerStub());
            var screensBuilder = new ScreensBuilder();
            debugContext.AddServiceToRegister<IScreensBuilder>(screensBuilder);
            debugContext.AddServiceToRegister<IScreenRegisterer>(screensBuilder);
            debugContext.AddServiceToRegister<EditorLevelControllerLauncher>(this);
            debugContext.AddServiceToRegister<IConfigRefresher>(this);
            debugContext.AddServiceToRegister<ICityAssetsProvider>(GetComponent<EditorCityAssetsProvider>());
            debugContext.AddServiceToRegister<ScoreMultiplierProvider>(new ScoreMultiplierProvider());
            debugContext.AddServiceToRegister<SpawnerSettingsManager>(_spawnerSettingsManager);
            debugContext.AddServiceToRegister<IRoyaleEventMatch3Manager>(new RoyaleEventMatch3ManagerStub());
            debugContext.AddServiceToRegister<ITeamCoopEventMatch3Manager>(new TeamCoopEventEventMatch3ManagerStub());
            debugContext.AddServiceToRegister<IRaceEventMatch3Manager>(new RaceEventMatch3ManagerStub());
            var gameEventManagerCollection = new GameEventMatch3ManagersCollection();
            gameEventManagerCollection.AddManager(new GameEventMatch3ManagerStub());
            debugContext.AddServiceToRegister<GameEventMatch3ManagersCollection>(gameEventManagerCollection);
            debugContext.AddServiceToRegister<IOrientationTracker>(new OrientationTrackerStub());
            debugContext.AddServiceToRegister<IModalsManager>(null);
            debugContext.AddServiceToRegister<IThemeManager>(new ThemeManagerStub());
            debugContext.AddServiceToRegister<IBoosterManager>(new BoosterManagerStub());
            debugContext.AddServiceToRegister<IRaceEventManager>(new RaceEventManagerStub());
            debugContext.AddServiceToRegister<ITeamEventManager>(new TeamEventManagerStub());
            debugContext.AddServiceToRegister<IRoyaleEventManager>(new RoyaleEventManagerStub());
            var vibrationWrapper = new VibrationsWrapperStub();
            debugContext.AddServiceToRegister<IVibrationsWrapper>(vibrationWrapper);
            debugContext.AddServiceToRegister<UserSettings>(new UserSettings(vibrationWrapper));
            debugContext.AddServiceToRegister<IButlerGiftManager>(new ButlerGiftManagerEditorStub());
            debugContext.AddServiceToRegister<ILocationManager>(new MockLocationManager());
            debugContext.AddServiceToRegister<IOverlayDialogManager>(new OverlayDialogManagerStub());
            
            debugContext.RegisterContext(_awakeContext);
            ContextedUiBehaviour.SetupRootContext(debugContext);
            screenController.ShowScreen(_levelCanvasPrefab,
                go =>
                {
                    var ctrl = go.GetOrAddComponent<DebugLevelController>();
                    ctrl.Init(debugContext);
                    ctrl.OnHide();
                    ctrl.OnShow();
                });
        }

        [ContextMenu("Print goal types")]
        private void DebugPrintGoalTypes()
        {
            var names = Enum.GetNames(typeof(GoalType));
            var sb = new StringBuilder();
            foreach (var n in names)
            {
                var v = (GoalType)Enum.Parse(typeof(GoalType), n);
                sb.Append(v.ToString());
                sb.Append(":");
                sb.Append(((ulong)v).ToString());
                sb.AppendLine();
            }

            Debug.Log(sb.ToString());
        }

        private void Update()
        {
            OnUpdate?.Invoke();
        }

        private void FixedUpdate()
        {
            OnFixedUpdate?.Invoke();
        }

        private void LateUpdate()
        {
            OnLateUpdate?.Invoke();
        }
    }
}
#endif
