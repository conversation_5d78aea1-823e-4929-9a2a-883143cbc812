using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Object = UnityEngine.Object;

namespace BBB.UI
{
    public class EditorCityAssetsProvider : BbbMonoBehaviour, ICityAssetsProvider
    {
        [Serializable]
        public class StringSpritePair
        {
            public string String;
            public Sprite Sprite;
        }

        [Serializable]
        public class StringGameObjectPair
        {
            public string String;
            public Object GameObject;
        }

        [SerializeField] private List<StringSpritePair> _namedSpritesList;
        [SerializeField] private List<StringGameObjectPair> _namedObjectsList;
        [SerializeField] private List<StringGameObjectPair> _cityBackgrounds;

        private Dictionary<string, Sprite> _namedSpritesDict = new Dictionary<string, Sprite>();
        private Dictionary<string, Object> _namesObjectsDict = new Dictionary<string, Object>();
        private bool _inited;

        private void LazyInit()
        {
            if (_inited)
                return;

            foreach(var pair in _namedSpritesList)
                _namedSpritesDict.Add(pair.String, pair.Sprite);

            foreach(var pair in _namedObjectsList)
                _namesObjectsDict.Add(pair.String, pair.GameObject);

            foreach(var pair in _cityBackgrounds)
                _namesObjectsDict.Add(pair.String, pair.GameObject);

            _inited = true;
        }

        public T GetGenericAsset<T>(string key) where T : Object
        {
            LazyInit();

            return _namesObjectsDict.GetSafe(key) as T;
        }

        public string GetStateDetails()
        {
            return string.Empty;
        }

        public Sprite GetSprite(string key, bool logError = true)
        {
            LazyInit();

            return _namedSpritesDict.GetSafe(key);
        }

        public UniTask<Sprite> GetSceneSnapshot(string key)
        {
            //shouldn't be used in editor
            throw new NotImplementedException();
        }

        public UniTask<Sprite> GetSceneIcon(string key)
        {
            //shouldn't be used in editor
            throw new NotImplementedException();
        }
    }
}
