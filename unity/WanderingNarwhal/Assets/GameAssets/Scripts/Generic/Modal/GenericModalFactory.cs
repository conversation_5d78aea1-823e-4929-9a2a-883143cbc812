using System;
using BBB.DI;
using BebopBee.Core.UI;

namespace BBB.Generic.Modal
{
    public class GenericModalFactory : IContextInitializable
    {
        private const string MONETIZR_NO_CONNECTION_DESCRIPTION1 = "MONETIZR_NO_CONNECTION_DESCRIPTION1";
        private const string MONETIZR_NO_CONNECTION_DESCRIPTION2 = "MONETIZR_NO_CONNECTION_DESCRIPTION2";

        private IModalsBuilder _modalsBuilder;
        private ILocalizationManager _localizationManager;

        public void InitializeByContext(IContext context)
        {
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _localizationManager = context.Resolve<ILocalizationManager>();
        }

        public void ShowWithCloseButton(string title, string description, Action<int> callback = null)
        {
            var ctrl = _modalsBuilder.CreateModalView<GenericModalController>(ModalsType.GenericModal);
            ctrl.SetupWithClose(title, description, callback);
            ctrl.ShowModal(ShowMode.Immediate);
        }

        public void ShowExitAppPrompt(ILocalizationManager localization)
        {
            ShowWithOkCloseButton(
                localization.getLocalizedText("APP_QUIT_TITLE"),
                localization.getLocalizedText("MM_EXIT_TITLE"),
                (result) =>
                {
                    if (result == 1)
                    {
                        if (UnityEngine.Application.isEditor)
                        {
                            UnityEngine.Debug.Log("Quit");
                        }
                        else
                        {
                            UnityEngine.Application.Quit();
                        }
                    }
                }
            );
        }

        public void ShowWithOkCloseButton(string title, string description, Action<int> callback = null, bool autocloseOnOk = true)
        {
            var ctrl = _modalsBuilder.CreateModalView<GenericModalController>(ModalsType.GenericModal);
            ctrl.SetupWithOkAndClose(title, description, callback, autocloseOnOk);
            ctrl.ShowModal(ShowMode.Immediate);
        }

        public void ShowWithOkButton(string title, string description, Action<int> callback = null, bool autocloseOnOk = true)
        {
            var ctrl = _modalsBuilder.CreateModalView<GenericModalController>(ModalsType.GenericModal);
            ctrl.SetupWithOkButton(title, description, callback, autocloseOnOk);
            ctrl.ShowModal(ShowMode.Immediate);
        }
        
        public void ShowWithOkButtonOnly(string title, string description, Action<int> callback = null)
        {
            var ctrl = _modalsBuilder.CreateModalView<GenericModalController>(ModalsType.GenericModal);
            ctrl.SetupWithOkButtonOnly(title, description, callback);
            ctrl.ShowModal(ShowMode.Immediate);
        }

        public void ShowNoConnectionModal(Action<int> callback = null, string title = null, string description = null)
        {
            var ctrl = _modalsBuilder.CreateModalView<NoConnectionModalController>(ModalsType.NoConnectionModal);
            ctrl.SetupWithOkButton(title, description, callback);
            ctrl.ShowModal(ShowMode.Immediate);
        }

        public void ShowVipProductsNoConnectionModal(Action<int> callback = null)
        {
            var ctrl = _modalsBuilder.CreateModalView<NoConnectionModalController>(ModalsType.NoConnectionModal);
            ctrl.SetupWithOkButton(_localizationManager.getLocalizedText(MONETIZR_NO_CONNECTION_DESCRIPTION1), _localizationManager.getLocalizedText(MONETIZR_NO_CONNECTION_DESCRIPTION2), callback);
            ctrl.ShowModal(ShowMode.Immediate);
        }

#if BBB_DEBUG
        public void ShowDebug(string title, string description, Action<int> callback = null)
        {
            var ctrl = _modalsBuilder.CreateModalView<DebugModalController>(ModalsType.DebugModal);
            ctrl.SetupWithClose(title, description, callback);
            ctrl.ShowModal(ShowMode.Immediate);
        }
#endif
    }
}