using System;
using BBB.Audio;
using BBB.Screens;
using BebopBee.Core.Audio;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Generic.Modal
{
    [Flags]
    public enum GenericStatusButtonTypes
    {
        ButtonTypeOk = 1 << 1,
        ButtonTypeClose = 1 << 2,
        ButtonTypeOkOnly = 1 << 3,
    }

    public class GenericModalViewPresenter : ModalsViewPresenter, IGenericModalViewPresenter
    {
        public event Action<int> OnButtonEvent = delegate { };
        
        [SerializeField] private TextMeshProUGUI _title;
        [SerializeField] private TextMeshProUGUI _message;
        [SerializeField] private Button _okButton;
        [SerializeField] private GenericStatusButtonSwitcher _buttonSwitcher;

        protected override void Awake()
        {
            base.Awake();
            _okButton.ReplaceOnClick(OnOkButtonClicked);
        }

        private void OnOkButtonClicked()
        {
            OnButtonEvent(1);
            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
        }

        public void SetupView(string title, string message, GenericStatusButtonTypes buttonTypes)
        {
            // _title.text = Localization.getLocalizedText(title);
            // _message.text = Localization.getLocalizedText(message);
            if (_title != null)
            {
                _title.text = title;
            }
            _message.text = message;
            _buttonSwitcher.CurrentState = buttonTypes;
            CloseButton.gameObject.SetActive(!buttonTypes.HasFlag(GenericStatusButtonTypes.ButtonTypeOkOnly));
        }

        protected override void OnCloseButtonClick()
        {
            OnButtonEvent(0);
            base.OnCloseButtonClick();
        }
    }
}