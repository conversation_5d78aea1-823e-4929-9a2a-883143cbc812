using System;
using BBB.Core;
using BBB.Social;

namespace BBB.Generic.Modal
{
    public class GenericModalController : BaseModalsController<IGenericModalViewPresenter>
    {
        private bool _autocloseOnOk;
        private Action<int> _callback;

        protected override void OnShow()
        {
            base.OnShow();
            View.OnButtonEvent += OnButtonClicked;
        }

        protected override void OnHide()
        {
            base.OnHide();
            View.OnButtonEvent -= OnButtonClicked;
        }

        public void SetupWithOkAndClose(string title, string description, Action<int> callback = null, bool autocloseOnOk = true)
        {
            _callback = callback;
            _autocloseOnOk = autocloseOnOk;
            if (IsReady())
            {
                View.SetupView(title, description, GenericStatusButtonTypes.ButtonTypeClose | GenericStatusButtonTypes.ButtonTypeOk);
            }
            else
            {
                DoWhenReady(() => { View.SetupView(title, description, GenericStatusButtonTypes.ButtonTypeClose | GenericStatusButtonTypes.ButtonTypeOk); });
            }
        }

        public void SetupWithClose(string title, string description, Action<int> callback)
        {
            _callback = callback;
            if (IsReady())
            {
                View.SetupView(title, description, GenericStatusButtonTypes.ButtonTypeClose);
            }
            else
            {
                DoWhenReady(() => { View.SetupView(title, description, GenericStatusButtonTypes.ButtonTypeClose); });
            }
        }

        public void SetupWithOkButton(string title, string description, Action<int> callback, bool autocloseOnOk = true)
        {
            _callback = callback;
            _autocloseOnOk = autocloseOnOk;
            if (IsReady())
            {
                View.SetupView(title, description, GenericStatusButtonTypes.ButtonTypeOk);
            }
            else
            {
                DoWhenReady(() => { View.SetupView(title, description, GenericStatusButtonTypes.ButtonTypeOk); });
            }
        }
        
        public void SetupWithOkButtonOnly(string title, string description, Action<int> callback)
        {
            _callback = callback;
            _autocloseOnOk = true;
            if (IsReady())
            {
                View.SetupView(title, description, GenericStatusButtonTypes.ButtonTypeOkOnly);
            }
            else
            {
                DoWhenReady(() => { View.SetupView(title, description, GenericStatusButtonTypes.ButtonTypeOkOnly); });
            }
        }

        private void OnButtonClicked(int idx)
        {
            if (idx == 1)
            {
                if (_callback != null)
                {
                    _callback.SafeInvoke(1);
                    if (_autocloseOnOk)
                    {
                        HideModal();
                    }
		            return;
                }
                else
                {
                    HideModal();
                }
            }
            else if (idx == 0)
            {
                HideModal();
            }

            _callback.SafeInvoke(idx);
        }

        public override bool CanBypassTransition()
        {
            return true;
        }
    }
}