using System;
using BBB.Core;

namespace BBB.Generic.Modal
{
    public class NoConnectionModalController : BaseModalsController<INoConnectionModalViewPresenter>
    {
        private bool _autocloseOnOk;
        private Action<int> _callback;

        protected override void OnShow()
        {
            base.OnShow();
            View.OnButtonEvent += OnButtonClicked;
        }

        protected override void OnHide()
        {
            base.OnHide();
            View.OnButtonEvent -= OnButtonClicked;
        }

        public void SetupWithOkAndClose(string title, string description, Action<int> callback = null, bool autocloseOnOk = true)
        {
            _callback = callback;
            _autocloseOnOk = autocloseOnOk;
            if (IsReady())
            {
                View.SetupView(title, description, GenericStatusButtonTypes.ButtonTypeClose | GenericStatusButtonTypes.ButtonTypeOk);
            }
            else
            {
                DoWhenReady(() => { View.SetupView(title, description, GenericStatusButtonTypes.ButtonTypeClose | GenericStatusButtonTypes.ButtonTypeOk); });
            }
        }

        public void SetupWithClose(string title, string description, Action<int> callback)
        {
            _callback = callback;
            if (IsReady())
            {
                View.SetupView(title, description, GenericStatusButtonTypes.ButtonTypeClose);
            }
            else
            {
                DoWhenReady(() => { View.SetupView(title, description, GenericStatusButtonTypes.ButtonTypeClose); });
            }
        }

        public void SetupWithOkButton(string title, string description, Action<int> callback, bool autocloseOnOk = true)
        {
            _callback = callback;
            if (IsReady())
            {
                View.SetupView(title, description, GenericStatusButtonTypes.ButtonTypeOk);
            }
            else
            {
                DoWhenReady(() => { View.SetupView(title, description, GenericStatusButtonTypes.ButtonTypeOk); });
            }
        }

        private void OnButtonClicked(int idx)
        {
            if (idx == 1)
            {
                if (_callback != null)
                {
                    _callback.SafeInvoke(1);
                    if (_autocloseOnOk)
                    {
                        HideModal();
                    }
                }
                else
                {
                    HideModal();
                }
            }

            _callback.SafeInvoke(idx);
        }

        public override bool CanBypassTransition()
        {
            return true;
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            _callback = null;
            View.OnButtonEvent -= OnButtonClicked;
        }
    }
}