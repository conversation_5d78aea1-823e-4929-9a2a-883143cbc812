using System;
using BBB.Audio;
using BBB.Screens;
using BBB.UI.Core;
using BebopBee.Core.Audio;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Generic.Modal
{
    public interface INoConnectionModalViewPresenter : IViewPresenter
    {
        event Action<int> OnButtonEvent; 
        void SetupView(string title, string message,  GenericStatusButtonTypes buttonTypes);
    }

    public class NoConnectionModalViewPresenter : ModalsViewPresenter, INoConnectionModalViewPresenter
    {
        private const string DEFAULT_NO_CONNECTION_TITLE = "OFFLINE_POPUP_NETWORK_OFF_TITLE";
        private const string DEFAULT_NO_CONNECTION_DESC = "OFFLINE_POPUP_NETWORK_OFF_DESC";
        public event Action<int> OnButtonEvent = delegate { };
        
        [SerializeField] private TextMeshProUGUI _title;
        [SerializeField] private TextMeshProUGUI _message;
        [SerializeField] private Button _okButton;
        [SerializeField] private GenericStatusButtonSwitcher _buttonSwitcher;

        protected override void Awake()
        {
            base.Awake();
            _okButton.ReplaceOnClick(OnOkButtonClicked);
        }

        private void OnOkButtonClicked()
        {
            OnButtonEvent(1);
            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
            TriggerOnCloseClicked();
        }

        public void SetupView(string title, string message, GenericStatusButtonTypes buttonTypes)
        {
            _title.text = title.IsNullOrEmpty() ? LocalizationManager.GetLocalizedText(DEFAULT_NO_CONNECTION_TITLE) : title;
            _message.text = message.IsNullOrEmpty() ? LocalizationManager.GetLocalizedText(DEFAULT_NO_CONNECTION_DESC) : message;
            
            _buttonSwitcher.CurrentState = buttonTypes;
        }

        protected override void OnCloseButtonClick()
        {
            OnButtonEvent(0);
            base.OnCloseButtonClick();
        }
    }
}