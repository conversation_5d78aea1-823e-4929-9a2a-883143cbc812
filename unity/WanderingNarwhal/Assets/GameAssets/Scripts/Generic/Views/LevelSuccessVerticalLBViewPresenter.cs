using System.Collections;
using BBB.DI;
using BBB.EndGameEvents;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.UI.OverlayDialog;
using UnityEngine;

namespace BBB.UI.Level
{
    public class LevelSuccessVerticalLBViewPresenter : LevelSuccessBaseViewPresenter
    {
        [SerializeField] private float _delayBeforeLBForceShow = 1f;
        [SerializeField] protected EventLeaderboardScrollView _leaderboardScrollView;

        private ICoroutineExecutor _coroutineExecutor;
        private Coroutine _waitingCoroutine;
        private IOverlayDialogManager _overlayDialogManager;

        protected override void InitOnce(IContext previousContext)
        {
            base.InitOnce(previousContext);
            
            _coroutineExecutor = previousContext.Resolve<ICoroutineExecutor>();
            _overlayDialogManager = previousContext.Resolve<IOverlayDialogManager>();

            _leaderboardScrollView.Init(previousContext);
            _leaderboardScrollView.Configure(true, false);
        }

        protected override void StartMainFlow()
        {
            StopWaitingCoroutine();
            var gameEvent = GameEventManager.GetCurrentSideMapEvent() as SideMapGameEvent;
            if (gameEvent != null)
            {
                var viewModel = EventLeaderboardViewModel.CreateViewModel(gameEvent);
                
                if (viewModel.Items == null && !gameEvent.IsUpdatingDataNow && ConnectivityStatusManager.ConnectivityReachable)
                {
                    RefreshScrollView(viewModel);
                    gameEvent.UpdateRemoteDataIfNeeded();
                }
                _waitingCoroutine = _coroutineExecutor.StartCoroutine(WaitToShowLeaderboard(viewModel, gameEvent));
            }

            OnWaitComplete();
        }
        
        private IEnumerator WaitToShowLeaderboard(EventLeaderboardViewModel viewModel, CompetitionGameEvent gameEvent)
        {
            var startTime = Time.time;
            while ((gameEvent.CachedOpponents == null && gameEvent.IsUpdatingDataNow) || Time.time - startTime < _delayBeforeLBForceShow)
            {
                yield return null;
            }

            viewModel.Items = gameEvent.CachedOpponents;
            RefreshScrollView(viewModel);
        }
        
        private void StopWaitingCoroutine()
        {
            if (_waitingCoroutine != null)
            {
                _coroutineExecutor?.StopCoroutine(_waitingCoroutine);
                _waitingCoroutine = null;
            }
        }

        private void RefreshScrollView(EventLeaderboardViewModel viewModel)
        {
            _leaderboardScrollView.Refresh(viewModel, null, _overlayDialogManager);
        }
        
        protected override void OnHide()
        {
            StopWaitingCoroutine();
            _leaderboardScrollView.Clear();
            base.OnHide();
        }
    }
}
