using System;
using System.Collections.Generic;
using BBB.DI;
using BBB.UI.Core;
using Cysharp.Threading.Tasks;
using FBConfig;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Generic.Views
{
    public class GenericRewardComponent : ContextedUiBehaviour
    {
        [SerializeField] private Image _rewardIcon;
        [SerializeField] private TextMeshProUGUI _rewardAmount;
        [SerializeField] private Sprite _multipleRewardsIcon;
        [SerializeField] private GameObject _textHolder;
        
        private CurrencyIconsLoader _iconsLoader;
        
        protected override void InitWithContextInternal(IContext context)
        {
            _iconsLoader = context.Resolve<CurrencyIconsLoader>();
        }

        public void Setup(List<DictStringIntT> rewards)
        {
            LazyInit();

            if (rewards == null || rewards.Count == 0)
                return;
            
            if (rewards.Count > 1)
            {
                _rewardIcon.sprite = _multipleRewardsIcon;
                _textHolder.SetActive(false);
            }
            else
                Setup(rewards[0].Key, rewards[0].Value);
        }

        private void Setup(string reward, int amount)
        {
            _iconsLoader.LoadAndGetCurrencySpriteAsync(reward).ContinueWith(sprite => _rewardIcon.sprite = sprite);
            _rewardAmount.text = amount.ToString();
            _textHolder.SetActive(true);
        }

        public void Clear()
        {
            _rewardIcon.sprite = null;
            _rewardAmount.text = string.Empty;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Clear();
        }
    }
}