using System;
using System.Collections.Generic;
using UnityEngine;

namespace BBB.UI.Level.ScriptableObjects
{
    public class AwardsSettings : ScriptableObject
    {
        [Serializable]
        public class Instance
        {
            public string AwardUid;
            public string LocalizedTextId;
            public string SoundId;
            public string SecondarySoundId;
        }

        public List<Instance> Settings;

        public Instance FindByUid(string uid)
        {
            foreach (var instance in Settings)
            {
                if (instance.AwardUid.Equals(uid))
                {
                    return instance;
                }
            }
            return null;
        }

    }
}