using UnityEngine;

/// <summary>
/// Settings container for shuffle animation settings
/// If settings are not specified in this config, then default animation settings will be used 
/// </summary>
[CreateAssetMenu(fileName = "ShuffleAnimSettings", menuName = "BBB/M3/ShufflesAnims", order = 1)]
public class ShuffleAnimationsSettings : ScriptableObject
{
    public float unitsPerSound = 4f;
    public float perpOffset = 0.2f;
    public AnimationCurve animationCurve;
    public float angle = 0f;
}