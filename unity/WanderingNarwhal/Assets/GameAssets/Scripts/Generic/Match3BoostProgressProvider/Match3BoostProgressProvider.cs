using BBB.DI;

namespace BBB.UI.Level.Match3BoostProgressProvider
{
    public class Match3BoostProgressProvider : IMatch3BoostProgressProvider, IContextInitializable
    {
        private float _value;
        
        public void InitializeByContext(IContext context)
        {
        }

        public void ResetDefaults()
        {
            _value = default;
        }

        public float GetValue()
        {
            return _value;
        }

        public void SetValue(float value)
        {
            _value = value;
        }

    }
}