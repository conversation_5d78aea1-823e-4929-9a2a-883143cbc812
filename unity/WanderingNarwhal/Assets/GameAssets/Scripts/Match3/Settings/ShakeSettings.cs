using System;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Settings
{
    [Serializable]
    public class ShakeSettings
    {
        public float Amplitude = 20f;
        public int Frequency = 20;
        [Tooltip("Indicates how much the shake will be random (0 to 180 - values higher than 90 tileLayerState of suck, so beware).\nSetting it to 0 will shake along a single direction.")]
        [Range(0f, 180f)]
        public float VectorAngleSpread = 90f;
        public float Duration = 0.5f;
        public float Delay = 0f;
    }
}