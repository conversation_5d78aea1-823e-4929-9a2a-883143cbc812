using System;
using System.Collections.Generic;
using BBB;
using BBB.DI;
using BBB.Map;
using BBB.Match3;
using BBB.Match3.Renderer;
using BBB.UI;
using Core.Configs;
using FBConfig;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Settings
{
    public class M3Settings : BbbMonoBehaviour, IContextInitializable, IContextReleasable
    {
        private const string DiscoBallSkin = "DiscoBall";
        private const string SuperDiscoBallSkin = "SuperDiscoBall";
        
        [Tooltip("Defines chessboard pattern of cells' sprites over different stages")]
        [SerializeField] private StageCellBg[] _boardCellBackground;

        [Tooltip("tiles and boosters settings on level start")]
        [SerializeField] private BoardRevealSettings _boardRevealSettings;

        [Header("Tile destroy")]
        [SerializeField] private TileDestroySettings _destroyByBooster;
        [SerializeField] private TileDestroySettings _manualMatch;
        [SerializeField] private TileDestroySettings _autoMatch;

        [SerializeField] private TileDestroySettings _manualMatchWithBoosterFormation;
        [SerializeField] private TileDestroySettings _autoMatchWithBoosterFormation;
        [SerializeField] private TileFallingSettings _tileFallingSettings;

        [SerializeField] private float _swapSpeed;
        [SerializeField] private float _swapScale;
        [SerializeField] private Material[] _scoreFontMaterials;
        [SerializeField] private List<AutoBoosterSettlePriority> _autoBoosterSettlePriorities;

        [Header("Shake")]
        [Tooltip("If TRUE the tween will smoothly snap all values to integers")]
        [SerializeField] private bool _snapping;
        [Tooltip("If TRUE the shake will automatically fadeOut smoothly within the tween's duration, otherwise it will not")]
        [SerializeField] private bool _fadeOut = true;

        [Tooltip("This shake happens on layered blocker tiles such as frame, crate, watermelon, egg, vase etc.")]
        [SerializeField] private ShakeSettings _blockerPositionShake;
        [Tooltip("Same as above but for rotation in angles")]
        [SerializeField] private ShakeSettings _blockerRotationShake;

        [SerializeField] private ShakeSettings _defaultShake;
        [SerializeField] private ShakeSettings _bombShake;
        [SerializeField] private ShakeSettings _verticalBoosterShake;
        [SerializeField] private ShakeSettings _horizontalBoosterShake;
        [SerializeField] private ShakeSettings _tuktukShake;
        [Space]
        [SerializeField] private ShakeSettings _boltBombShake;
        [SerializeField] private ShakeSettings _boltBoltShake;
        [SerializeField] private ShakeSettings _bombBombShake;
        [SerializeField] private ShakeSettings _dynamiteBoxShake;

        [Header("Tile Tap Highlight")]
        [SerializeField] private float _glowInOutDuration = 0.5f;
        [SerializeField] private float _glowMaxValue = 1.2f;
        [SerializeField] private float _glowMinValue = 1f;
        [SerializeField] private string _glowParam = "_Glow";

        [Header("Bonus Time")]
        [SerializeField] private Color _boardDimColor;
        [SerializeField] private float _dimTime = 0.5f;

        [Header("Level lose tiles fall settings")]
        [SerializeField] private GridFallAnimSettings _gridFallAnimSettings;

        [Header("OOM Booster")]
        [SerializeField] private float _initialBoosterSpawnDelay = 0.3f;

        [Header("Busy time")]
        [SerializeField] private float _bombBusyTime;
        [SerializeField] private float _tntExplosionBusyTime;
        [SerializeField] private float _toadDestroyBusyTime;
        [SerializeField] private float _iceBarDestroyBusyTime;
        [SerializeField] private float _metalBarDestroyBusyTime;
        [SerializeField] private float _shelfDestroyBusyTime;
        [SerializeField] private float _goldenScarabDestroyBusyTime;
        [SerializeField] private float _slotMachineDestroyBusyTime;
        [SerializeField] private float _dynamiteBoxDestroyBusyTime;

        [Header("Rocket")]
        [SerializeField] private float _rocketSpeed;
        [SerializeField] private float _rocketAcceleration;
        [SerializeField] private float _pauseAfterRocketHit = 0.1f;

        [Header("Lightning")]
        [SerializeField] private float _timeForLightning = 0.5f;
        [SerializeField] private float _timeBetweenLightnings = 0.5f;
        [SerializeField] private float _boltBoltPause = 2.1f;
        [SerializeField] private float _formationByBoltIntroTime = 0.9f;
        
        [Header("Disco Ball")]
        [SerializeField] private float _discoBallEffectDefaultValue = 1f;

        [Header("Bomb")]
        [SerializeField] private float _bombBombActivationTime;

        [Header("TukTuk")]
        [SerializeField] private float _tukTukSpeed;
        [SerializeField] private float _tukTukAcceleration;
        [SerializeField] private float _tukTukCollectionDelay = 0.1f;
        [SerializeField] private float _tukTukDestroyDelay = 0.5f;

        [Header("Vertical")]
        [SerializeField] private float _verticalBoosterSpeed;
        [SerializeField] private float _verticalBoosterAcceleration;
        [SerializeField] private float _verticalBoosterAnticipationDelay;
        [SerializeField] private float _verticalBoosterBoardHideDelay;
        [SerializeField] private float _verticalBoosterBoardRevealDelay;

        [Header("Horizontal")]
        [SerializeField] private float _horizontalBoosterSpeed;
        [SerializeField] private float _horizontalBoosterAcceleration;
        [SerializeField] private float _horizontalBoosterOffset;

        [Header("Comet")]
        [SerializeField] private float _timeForComet = 0.34f;
        [SerializeField] private float _timeBetweenComets = 0.17f;

        [Header("Ivy")]
        [SerializeField] private float _ivyDestroyDelay = 0.2f;
        [Header("Tnt")]
        [SerializeField] private float _tntShakeMechanicsDelay = 0.45f;
        [Header("Monkey")]
        [SerializeField] private float _monkeyDestroyBusyTime = 1.1f;

        [Header("Events")]
        [SerializeField] private float _collectEventTileDelay = 0.4f;

        [SerializeField] private int _discoRushSimpleTileMatch = 1;
        [SerializeField] private int _discoRushSpecialTileMatch = 1;
        [SerializeField] private int _discoRushEndGameSpawn = 5;

        [Header("Other")]
        [SerializeField] private float _tileCollisionRadius;
        [SerializeField] private float _chanceToSpawnBombInBonusTime = 0.2f;
        [SerializeField] private float _spawnAnticipationDelay = 0.4f;
        [SerializeField] private float _spawnAnticipationWaitTime = 0.025f;
        [SerializeField] private int _slotMachineMaxSameSpawnsPerLevel = 2;
        [SerializeField] private int _spawnFxReleaseTime = 8;
        
        [Header("Super Disco Ball")]
        [SerializeField] private float _discoBallStageDelayWhileUsingSuperDiscoBall = 1f;
        [SerializeField] private float _regularTilesStageDelayWhileUsingSuperDiscoBall = 0.5f;
        [SerializeField] private float _boostersStageDelayWhileUsingSuperDiscoBall = 0.5f;
        [SerializeField] private Color _discoBallRegularTilesOverlayColor = Color.white;
        [SerializeField] private Color _discoBallBoostersTilesOverlayColor = Color.white;
        [SerializeField] private Color _superDiscoBallRegularTilesOverlayColor = Color.white;
        [SerializeField] private Color _superDiscoBallBoostersTilesOverlayColor = Color.white;

        [Header("Dynamite Blast")] 
        [SerializeField] private float _dynamiteBlastSpeed = 9f;

        [SerializeField] private FireWorksEffectSettings _fireWorksEffectSettings;
        [SerializeField] private BoltBoosterComboAnimSettings _boltBoosterComboAnimSettings;
        [SerializeField] private BoltBoosterComboAnimSettings _boltRegularComboAnimSettings;
        [SerializeField] private PropellerEffectSettings _propellerEffectSettings;
        [SerializeField] private PropellerComboFlightEffectSettings _propellerComboFlightEffectSettings;

        public T GetSettingsForEffect<T>(FxType effectType) where T : class
        {
            object result = effectType switch
            {
                FxType.BoltBoosterCombine => _boltBoosterComboAnimSettings,
                FxType.BoltRegularTileCombine => _boltRegularComboAnimSettings,
                FxType.PropellerFlight => _propellerEffectSettings,
                FxType.PropellerComboFlight => _propellerComboFlightEffectSettings,
                FxType.FireWorksFlight => _fireWorksEffectSettings,
                _ => null
            };

            return result as T;
        }

        public Sprite GetCellBackgroundSprite(Coords cellCoords)
        {
            var xIndex = cellCoords.X % 2;
            var index = cellCoords.Y % 2 == 0 ? xIndex : 1 - xIndex;
            if (_screensBuilder.CurrentScreenType is ScreenType.SideMapLevelScreen)
            {
                var eventUid = _gameEventManager.GetCurrentSideMapEvent()?.Uid;
                var eventSettings = _gameEventResourceManager?.GetSettings(eventUid);
                if (eventSettings == null || eventSettings.CellBgs == null ||
                    eventSettings.CellBgs.Length <= index)
                {
                    UnityEngine.Debug.LogError("No checkmate normal sprites for event");
                }
                else
                {
                    return eventSettings.CellBgs[index];
                }
            }

            if (_boardCellBackground.Length == 0)
            {
                UnityEngine.Debug.LogError("No checkmate normal sprites found");
                return null;
            }

            return _boardCellBackground[(int)_currentLevelHolder.level.GetPaletteStage()].CheckmateNormalSprites[index];
        }

        public BoardRevealSettings BoardRevealSettings => _boardRevealSettings;
        public TileDestroySettings TileDestroyByBooster => _destroyByBooster;

        public TileDestroySettings GetMatchSetting(bool manual, bool boosterFormation)
        {
            if (boosterFormation)
                return manual ? _manualMatchWithBoosterFormation : _autoMatchWithBoosterFormation;
            return manual ? _manualMatch : _autoMatch;
        }

        public float SwapSpeed => _swapSpeed;
        public float SwapScale => _swapScale;

        public TileFallingSettings TileFallingSettings => _tileFallingSettings;

        public bool Snapping => _snapping;
        public bool FadeOut => _fadeOut;
        public Tuple<ShakeSettings, ShakeSettings> TileShakeSettings => new(_blockerPositionShake, _blockerRotationShake);

        public ShakeSettings GetShakeParams(ShakeSettingsType shakeSettingsType)
        {
            return shakeSettingsType switch
            {
                ShakeSettingsType.BoltBolt => _boltBoltShake,
                ShakeSettingsType.BoltBomb => _boltBombShake,
                ShakeSettingsType.BombBomb => _bombBombShake,
                ShakeSettingsType.Bomb => _bombShake,
                ShakeSettingsType.TukTuk => _tuktukShake,
                ShakeSettingsType.VerticalBooster => _verticalBoosterShake,
                ShakeSettingsType.HorizontalBooster => _horizontalBoosterShake,
                ShakeSettingsType.DynamiteBox => _dynamiteBoxShake,
                _ => _defaultShake
            };
        }

        public float BombBombActivationTime => _bombBombActivationTime;

        public int GetAutoBoosterPriorityByType(AutoBoosterSettlePriorityType autoBoosterSettlePriorityType)
        {
            foreach (var booster in _autoBoosterSettlePriorities)
            {
                if (booster.AutoBoosterSettlePriorityType == autoBoosterSettlePriorityType)
                {
                    return booster.Priority;
                }
            }
    
            return 0;
        }
        
        public float GlowInOutDuration => _glowInOutDuration;
        public float GlowMaxValue => _glowMaxValue;
        public float GlowMinValue => _glowMinValue;
        public string GlowParam => _glowParam;


        public Color BoardDimColor => _boardDimColor;
        public float DimTime => _dimTime;

        public Material GetRandomScoreMaterial() => _scoreFontMaterials.GetRandomItem();

        public Material GetScoreMaterial(TileKinds tileKind)
        {
            var index = (int)tileKind - 1;

            if (index < 0 || index >= _scoreFontMaterials.Length)
                return null;

            return _scoreFontMaterials[index];
        }

        public GridFallAnimSettings GridFallAnim => _gridFallAnimSettings;
        public float InitialBoosterSpawnDelay => _initialBoosterSpawnDelay;

        public float PauseAfterRocketHit => _pauseAfterRocketHit;

        public float RocketSpeed => _rocketSpeed;
        public float RocketAcceleration => _rocketAcceleration;
        public float TileCollisionRadius => _tileCollisionRadius;
        public float BombBusyTime => _bombBusyTime;
        public float TntExplosionBusyTime => _tntExplosionBusyTime;
        public float TimeForComet => _timeForComet;
        public float TimeBetweenComets => _timeBetweenComets;
        public float TimeForOneLightning => _timeForLightning;
        public float TimeBetweenLightnings => _timeBetweenLightnings;
        public float ToadDestroyBusyTime => _toadDestroyBusyTime;
        public float IceBarDestroyBusyTime => _iceBarDestroyBusyTime;
        public float MetalBarDestroyBusyTime => _metalBarDestroyBusyTime;
        public float ShelfDestroyBusyTime => _shelfDestroyBusyTime;
        public float GoldenScarabDestroyBusyTime => _goldenScarabDestroyBusyTime;
        public float SlotMachineDestroyBusyTime => _slotMachineDestroyBusyTime;
        public float DynamiteBoxDestroyBusyTime => _dynamiteBoxDestroyBusyTime;
        public float TukTukSpeed => _tukTukSpeed;
        public float TukTukAcceleration => _tukTukAcceleration;
        public float VerticalBoosterSpeed => _verticalBoosterSpeed;
        public float VerticalBoosterAcceleration => _verticalBoosterAcceleration;
        public float VerticalBoosterAnticipationDelay => _verticalBoosterAnticipationDelay;
        public float VerticalBoosterBoardHideDelay => _verticalBoosterBoardHideDelay;
        public float VerticalBoosterBoardRevealDelay => _verticalBoosterBoardRevealDelay;
        public float HorizontalBoosterSpeed => _horizontalBoosterSpeed;
        public float HorizontalBoosterAcceleration => _horizontalBoosterAcceleration;
        public float HorizontalBoosterOffset => _horizontalBoosterOffset;
        public float ChanceToSpawnBombInBonusTime => _chanceToSpawnBombInBonusTime;
        public float BoltBoltPause => _boltBoltPause;
        public float TntShakeMechanicsDelay => _tntShakeMechanicsDelay;
        public float MonkeyDestroyBusyTime => _monkeyDestroyBusyTime;
        public float TukTukCollectionDelay => _tukTukCollectionDelay;
        public float TukTukDestroyDelay => _tukTukDestroyDelay;
        public float SpawnAnticipationDelay => _spawnAnticipationDelay;
        public float SpawnAnticipationWaitTime => _spawnAnticipationWaitTime;
        public float CollectEventTileDelay => _collectEventTileDelay;
        public int DiscoRushSimpleTileMatch => _discoRushSimpleTileMatch;
        public int DiscoRushSpecialTileMatch => _discoRushSpecialTileMatch;
        public int DiscoRushEndGameSpawn => _discoRushEndGameSpawn;
        public float FormationByBoltIntroTime => _formationByBoltIntroTime;
        public float IvyDestroyDelay => _ivyDestroyDelay;
        public int SlotMachineMaxSameSpawnsPerLevel => _slotMachineMaxSameSpawnsPerLevel;
        public float DiscoBallEffectDefaultValue => _discoBallEffectDefaultValue;
        public float DiscoBallStageDelayWhileUsingSuperDiscoBall => _discoBallStageDelayWhileUsingSuperDiscoBall;
        public float RegularTilesStageDelayWhileUsingSuperDiscoBall => _regularTilesStageDelayWhileUsingSuperDiscoBall;
        public float BoostersStageDelayWhileUsingSuperDiscoBall => _boostersStageDelayWhileUsingSuperDiscoBall;
        public Color DiscoBallRegularTilesOverlayColor => _discoBallRegularTilesOverlayColor;
        public Color DiscoBallBoostersTilesOverlayColor => _discoBallBoostersTilesOverlayColor;
        public Color SuperDiscoBallRegularTilesOverlayColor => _superDiscoBallRegularTilesOverlayColor;
        public Color SuperDiscoBallBoostersTilesOverlayColor => _superDiscoBallBoostersTilesOverlayColor;
        
        public int SpawnFxReleaseTime => _spawnFxReleaseTime;
        public float DynamiteBlastSpeed => _dynamiteBlastSpeed;

        private LevelHolder _currentLevelHolder;
        private IScreensBuilder _screensBuilder;
        private IGameEventResourceManager _gameEventResourceManager;
        private IGameEventManager _gameEventManager;
        
        private static readonly Type[] RequiredConfigs = {
            typeof(MechanicTargetingConfigT),
            typeof(SlotMachineOutcomeConfigT),
            typeof(GiantPinataOutcomeConfigT)
        };

        public IDictionary<string, MechanicTargetingConfigT> MechanicTargetingConfigs { get; private set; }
        
        public SlotMachineConfiguration SlotMachineConfiguration { get; private set; }
        public GiantPinataConfiguration GiantPinataConfiguration { get; private set; }

        private float _superDiscoBall = 1;
        public event Action SuperDiscoBallChanged;
        
        public float SuperDiscoBall
        {
            get => _superDiscoBall;
            set
            {
                _superDiscoBall = value;
                SuperDiscoBallChanged?.Invoke();
            }
        }
        
        public bool IsSuperDiscoBallActive => SuperDiscoBall > 1;

        public void InitializeByContext(IContext context)
        {
            _currentLevelHolder = context.Resolve<LevelHolder>();
            _screensBuilder = context.Resolve<IScreensBuilder>();
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _gameEventManager = context.Resolve<IGameEventManager>();

            var config = context.Resolve<IConfig>();
            SetupConfigs(config);
            Config.OnConfigUpdated -= SetupConfigs;
            Config.OnConfigUpdated += SetupConfigs;
        }
        
        private void SetupConfigs(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            MechanicTargetingConfigs = config.Get<MechanicTargetingConfigT>();
            
            var slotMachineConfig = config.Get<SlotMachineOutcomeConfigT>();
            SlotMachineConfiguration = new SlotMachineConfiguration(slotMachineConfig);
            
            var giantPinataOutcomeConfig = config.Get<GiantPinataOutcomeConfigT>();
            GiantPinataConfiguration = new GiantPinataConfiguration(giantPinataOutcomeConfig);
        }
        
        public string GetDiscoBallSkin()
        {
            return IsSuperDiscoBallActive ? SuperDiscoBallSkin : DiscoBallSkin;
        }

        public void ReleaseByContext(IContext context)
        {
            Config.OnConfigUpdated -= SetupConfigs;
        }
    }
}