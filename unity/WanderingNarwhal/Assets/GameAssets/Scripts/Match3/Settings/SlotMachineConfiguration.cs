using System;
using System.Collections.Generic;
using FBConfig;
using GameAssets.Scripts.Match3.Logic.Tiles;

namespace GameAssets.Scripts.Match3.Settings
{
    public class SlotMachineConfiguration
    {
        public readonly Dictionary<SlotMachineRewardType, SlotMachineOutcomeConfigT> RewardConfigurations;

        public SlotMachineConfiguration(IDictionary<string, SlotMachineOutcomeConfigT> outcomeConfigDictionary)
        {
            RewardConfigurations = new Dictionary<SlotMachineRewardType, SlotMachineOutcomeConfigT>();

            foreach (var configKey in outcomeConfigDictionary.Keys)
            {
                // Try to parse the key as an enum value of SlotMachineRewardType
                if (Enum.TryParse(configKey, out SlotMachineRewardType rewardType))
                {
                    // Add the parsed enum value and corresponding config to the dictionary
                    RewardConfigurations.Add(rewardType, outcomeConfigDictionary[configKey]);
                }
            }
        }
    }

}