using System.Collections.Generic;
using BBB.Match3.Systems.CreateSimulationSystems;
using Core.Configs;
using FBConfig;
using GameAssets.Scripts.Match3.Logic;

namespace GameAssets.Scripts.Match3.Settings
{
    public class MechanicTargetingSettings
    {
        public Dictionary<GeneralizedLayer, float> TargetingWeightTable { get; }
        public Dictionary<GoalType, float> GoalTypeRelatedWeights { get; }
        public float WeightUnderDropItem { get; }
        public float DropItemWeightDiminishStep { get; }
        public float TntGoalWeight { get; }
        public float TukTukGoalWeight { get; }
        public float FrameNearAnimalWeight { get; }

        public MechanicTargetingSettings(MechanicTargetingConfigT config)
        {
            var weightTable = config.WeightTable;
            if (weightTable != null)
            {
                TargetingWeightTable = new Dictionary<GeneralizedLayer, float>();
                foreach (var kvp in weightTable)
                {
                    var generalizedLayer = kvp.Key.TryParseToEnum<GeneralizedLayer>();
                    TargetingWeightTable[generalizedLayer] = kvp.Value;
                }
                FrameNearAnimalWeight = TargetingWeightTable[GeneralizedLayer.FrameNearAnimal];
            }

            var goalWeightTable = config.GoalWeightTable;
            if (goalWeightTable != null)
            {
                GoalTypeRelatedWeights = new Dictionary<GoalType, float>();
                foreach (var kvp in goalWeightTable)
                {
                    var goalType = kvp.Key.TryParseToEnum<GoalType>();
                    GoalTypeRelatedWeights[goalType] = kvp.Value;
                }
            }

            WeightUnderDropItem = config.WeightUnderDropItem;
            DropItemWeightDiminishStep = config.DropItemWeightDiminishStep;
            TntGoalWeight = config.TntGoalWeight;
            TukTukGoalWeight = config.TukTukGoalWeight;
        }
    }
}