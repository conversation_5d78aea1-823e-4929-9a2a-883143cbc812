using System;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Settings
{
    [Serializable]
    public struct BoostersRevealSettings
    {
        [Tooltip("Flight time")]
        public float BoosterFlightTime;
        public float PauseBetweenBoosters;
        [Tooltip("Time to go from 0 to 1 alpha")]
        public float AlphaFadeInTime;
        public float FxTrailLifetime;
        public float StartScale;
        public AnimationCurve ScaleCurve;
    }
}