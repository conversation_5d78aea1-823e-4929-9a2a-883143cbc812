using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.Match3.Renderer;
using BBB.UI.Level;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;
using Random = UnityEngine.Random;

namespace GameAssets.Scripts.Match3.Settings
{
    [Serializable]
    public class TntTargetSpriteMap
    {
        public TntTargetType Target;
        public Sprite Sprite;
    }

    [Serializable]
    public class LitterRenderVariant
    {
        public LitterSkin LitterSkin;
        public Sprite FullSprite;
        public List<Sprite> PartsSprites;
    }

    [Serializable]
    public class SpecialBoostRenderVariant
    {
        public TileKinds Kind;
        public Sprite BombSprite;
        public Sprite LineBreakerBodySprite;
        public Sprite LineBreakerFirstArrowSprite;
        public Sprite LineBreakerSecondArrowSprite;
    }

    [Serializable]
    public class StickerRenderVariant
    {
        private static int _currentSelectedSubVariant;

        public StickerSkin Skin;
        public List<Sprite> GoalSpritesSubVariants;
        public List<Sprite> TileSpriteSubVariantsLayer0;
        public List<Sprite> TileSpriteSubVariantsLayer1;
        public List<Sprite> TileSpriteSubVariantsLayer2;

        public Sprite GetTileSpriteAtIndex(int index)
        {
            return index switch
            {
                0 => CurrentTileSpriteLayer0,
                1 => CurrentTileSpriteLayer1,
                2 => CurrentTileSpriteLayer2,
                _ => null
            };
        }

        public int TotalSubVariants => Mathf.Max(GoalSpritesSubVariants.Count, Mathf.Max(TileSpriteSubVariantsLayer0.Count, Mathf.Max(TileSpriteSubVariantsLayer1.Count, TileSpriteSubVariantsLayer2.Count)));

        public Sprite CurrentGoalSprite => GoalSpritesSubVariants.Count == 0 ? null : GoalSpritesSubVariants[Mathf.Clamp(_currentSelectedSubVariant, 0, GoalSpritesSubVariants.Count - 1)];

        public Sprite CurrentTileSpriteLayer0 => TileSpriteSubVariantsLayer0.Count == 0 ? null : TileSpriteSubVariantsLayer0[Mathf.Clamp(_currentSelectedSubVariant, 0, TileSpriteSubVariantsLayer0.Count - 1)];

        public Sprite CurrentTileSpriteLayer1 => TileSpriteSubVariantsLayer1.Count == 0 ? null : TileSpriteSubVariantsLayer1[Mathf.Clamp(_currentSelectedSubVariant, 0, TileSpriteSubVariantsLayer1.Count - 1)];

        public Sprite CurrentTileSpriteLayer2 => TileSpriteSubVariantsLayer2.Count == 0 ? null : TileSpriteSubVariantsLayer2[Mathf.Clamp(_currentSelectedSubVariant, 0, TileSpriteSubVariantsLayer2.Count - 1)];

        /// <summary>
        /// Set current sub variant.
        /// </summary>
        /// <remarks>
        /// In case of Crate and Slate tiles, the sub-variant must be selected randomly each time level is started.
        /// For example, Slate has 5 variants of tile.
        /// </remarks>
        public void SetCurrentSubVariant(int index)
        {
            _currentSelectedSubVariant = index;
        }
    }

    [Serializable]
    public struct EffectSettingsData
    {
        public FxType effectType;
        public ScriptableObject effectSettins;
    }

    public class TileResourceSelector : BbbMonoBehaviour, IContextInitializable
    {
        [SerializeField] private List<LitterRenderVariant> _litterSpriteVariants;
        [SerializeField] private List<StickerRenderVariant> _stickerSpriteVariants;

        [SerializeField] private ShuffleAnimationsSettings _shuffleAnimationsSettings;

        /// <summary>
        /// Effects fly animations settings for each goal type.
        /// </summary>
        [SerializeField] private GoalsAnimationsSettingsList _goalsSettins;
        [SerializeField] private TntTargetSpriteMap[] _tntTargetsGoalSprites;

        private readonly IDictionary<LitterSkin, LitterRenderVariant> _litterSkinDict = new Dictionary<LitterSkin, LitterRenderVariant>();
        private readonly Dictionary<StickerSkin, StickerRenderVariant> _stickerSkinDict = new ();
        private IMatch3SharedResourceProvider _match3ResourceProvider;
        private ICityAssetsProvider _cityAssetsProvider;

        private AnimalSkin _selectedAnimalSkin;

        public GameObject SelectedAnimalPrefab => _match3ResourceProvider.GetPrefab(_selectedAnimalSkin.ToPrefabName());

        public LitterRenderVariant SelectedLitterVariant { get; private set; }

        public StickerRenderVariant SelectedStickerVariant { get; private set; }

        public void InitializeByContext(IContext context)
        {
            if (_match3ResourceProvider == context.Resolve<IMatch3SharedResourceProvider>()) return;

            _match3ResourceProvider = context.Resolve<IMatch3SharedResourceProvider>();
            _cityAssetsProvider = context.Resolve<ICityAssetsProvider>();

            _litterSkinDict.Clear();
            foreach (var variant in _litterSpriteVariants)
            {
                _litterSkinDict.Add(variant.LitterSkin, variant);
            }

            _stickerSkinDict.Clear();
            foreach (var variant in _stickerSpriteVariants)
            {
                _stickerSkinDict[variant.Skin] = variant;
            }
        }

        public void ResetDefaults()
        {
            _selectedAnimalSkin = default;
        }

        public ShuffleAnimationsSettings GetShuffleAnimationSettings()
        {
            return _shuffleAnimationsSettings;
        }
        
        public void GetAppearFlySettings(TileAsset target, out FxType fx, out GoalIconFlySettingsScriptableObject settings)
        {
            _goalsSettins.GetAppearFlySetting(target, out fx, out settings);
        }

        public void GetGoalFlySettinsForGoal(GoalType goal, out FxType fx, out GoalIconFlySettingsScriptableObject settins)
        {
            _goalsSettins.GetSettinsForGoal(goal, out fx, out settins);
        }

        public async UniTask<Sprite> GetTntTargetGoalSpriteAsync(TntTargetType target, TileKinds kind, TilesResources tilesResources)
        {
            switch (target)
            {
                case TntTargetType.Simple:
                    var tileData = await tilesResources.GetAsync(kind);
                    return tileData.Sprite;
                case TntTargetType.DropItem:
                    return GetGoalSprite(GoalType.DropItems);
                case TntTargetType.Litter:
                    return GetGoalSprite(GoalType.Litters);
                case TntTargetType.Sticker:
                    return GetGoalSprite(GoalType.Stickers);
            }

            foreach (var entry in _tntTargetsGoalSprites)
            {
                if (entry.Target == target)
                {
                    return entry.Sprite;
                }
            }

            BDebug.LogError(LogCat.Match3, "Couldn't find tnt target sprite for "+target);
            return null;
        }

        public Sprite GetGoalSprite(GoalType goal)
        {
            switch (goal)
            {
                // DropItem, Litter and Sticker goal have special sub-variants
                // which is not supported by generic GoalSettings asset.
                case GoalType.Litters:
                    return SelectedLitterVariant.FullSprite;
                case GoalType.Stickers:
                    return SelectedStickerVariant.CurrentGoalSprite;
                case GoalType.DropItems:
                    return GetDropItemSprite();
                case GoalType.Animal:
                    return GetFurryPalSprite();
            }

            var result = _goalsSettins.GetGoalIcon(goal);
            if (result == null)
            {
                BDebug.LogError(LogCat.Match3, $"Goal icon not found for goal {goal}");
            }

            return result;
        }

        public void RefreshForLevel(ILevel level)
        {
            RefreshForLevelInternal(level);
            RefreshStickerSubVariant();
        }

        private void RefreshForLevelInternal(ILevel level)
        {
            var litterSkin = level.LitterSkin;
            if (_litterSkinDict.TryGetValue(litterSkin, out var variant))
            {
                SelectedLitterVariant = variant;
            }
            else
            {
                SelectedLitterVariant = _litterSpriteVariants.Count == 0 ? null : _litterSpriteVariants[0];
                UnityEngine.Debug.LogError($"LitterSkin {litterSkin} not found in the dict");
            }

            var stickerSkin = level.StickerSkin;
            if (_stickerSkinDict.TryGetValue(stickerSkin, out var sVariant))
            {
                SelectedStickerVariant = sVariant;
            }
            else
            {
                SelectedStickerVariant = _stickerSpriteVariants[0];
                UnityEngine.Debug.LogError($"StickerSkin {stickerSkin} not found in the dict");
            }

            _selectedAnimalSkin = level.AnimalSkin;
        }

        private void RefreshStickerSubVariant()
        {
            SelectedStickerVariant.SetCurrentSubVariant(Random.Range(0, SelectedStickerVariant.TotalSubVariants));
        }

        private Sprite GetDropItemSprite()
        {
            return _cityAssetsProvider.GetSprite(CityResourceKeys.CityGenericDropItemSprite, false) ??
                   _goalsSettins.GetGoalIcon(GoalType.DropItems) ??
                   _match3ResourceProvider.GetSprite(Match3ResKeys.DropItemOverrideSprite);
        }

        private Sprite GetFurryPalSprite()
        {
            return _match3ResourceProvider.GetSprite(_selectedAnimalSkin.ToSpriteName());
        }
    }
}