using System;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Settings
{
    [Serializable]
    public class BoardRevealSettings
    {
        [Tooltip("Falling acceleration in RectTransform coordinates per second")]
        public float TilesFallingAcceleration = -3750f;
        [Tooltip("Angles per second tile rotates while falling")]
        public float RotationSpeed = -625f;
        [Tooltip("Tiles appear on the board row by row, that's a delay between rows appearing")]
        public float RowsRevealDelay = 0.075f;
        [Toolt<PERSON>("Shockwave starts from the center trigger tiles animation, this delay defines max delay which the furthest from center tile will have")]
        public float ShockwaveMaxDelay = 0.1125f;
        [<PERSON>ltip("Butler globe is triggered at the same time tiles falling is started, because it has long intro animation, use this delay to postpone it")]
        public float ButlerGlobeAppearingDelay = 0.15f;
        [Toolt<PERSON>("Equipped boosters appears after all rows have been revealed and after butler gone is done, but you can extra delay (or opposite) using this parameter")]
        public float EquippedBoostersAppearingDelay = 0.1f;
        
        [SerializeField] private BoostersRevealSettings _butlerGlobeBoosters;
        [SerializeField] private BoostersRevealSettings _equippedBoosters;
        
        public BoostersRevealSettings GetBoostersRevealSettings(bool isButler) => isButler ? _butlerGlobeBoosters : _equippedBoosters;
    }
}