namespace GameAssets.Scripts.Match3.Settings
{
    public static class M3Constants
    {
        public const int DefaultBombRadius = 2;
        public const int ExtendedBombRadius = 4;
        public const int MinMatchLength = 3;
        public const int LoopDetectorValue = 1000;
        public const int EndGameLoopDetectorValue = 200;
        
        public const int BusyTime = 1;
        public const int BusyTimeSimple = 1;
        public const int BusyTimeLineBreaker = 1;
        public const int BusyTimeBomb = 1;
        public const int BusyTimeCross = 1;
        public const int BusyTimeSmallCross = 1;
        public const int BusyTimeWhirlpool = 1;
        public const int BusyTimeRemoveAllColorTiles = 1;
        public const int BusyTimeChangeAllColorTilesInto = 1;
        public const int BusyTimeOnStickerRemoval = 1;
        public const int BusyTimeTripleCross = 1;
        
        public static readonly int[] FlowerPotSpreadPositions =
        {
            0, 1, 2,
            3, 4, 5,
            6, 7, 8
        };

        public static readonly int[] BushSpreadPositions =
        {
            8, 6, 5, 11,
            12, 3, 0, 15,
            14, 1, 2, 13,
            9, 7, 4, 10
        };
    }
}