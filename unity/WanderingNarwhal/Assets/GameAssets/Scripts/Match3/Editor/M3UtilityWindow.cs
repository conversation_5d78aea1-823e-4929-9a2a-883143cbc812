using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using BBB.M3Editor;
using BBB.Map;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using CleanupUtility;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.Match3.Logic;
using UnityEditor;
using UnityEngine;
using Path = System.IO.Path;
using Random = UnityEngine.Random;

namespace BBB.Match3.Editor
{
    public class M3UtilityWindow : EditorWindow
    {
        private static readonly Tuple<float, float>[] CoefRanges =
        {
            new(1f, 1.05f),
            new(1.05f, 1.1f),
            new(1.1f, 1.15f),
            new(1.15f, 1.2f),
            new(1.2f, 1.3f),
            new(1.35f, 1.5f),
            new(1.55f, 1.7f),
        };

        private bool _append;
        private GameController _gameController;
        private Grid _editorDebugGrid;
        private Cell _currentSelectedCell;
        private bool _topPanelHidden;
        private int _currentQueueSnapshotIndex;
        private int _currentQueueSnapshotStep;

        private Vector2 _scrollActions;
        private Vector2 _scrollCellInfo;

        [MenuItem("BebopBee/Debug/M3 Util")]
        private static void Init()
        {
            var window = (M3UtilityWindow)GetWindow(typeof(M3UtilityWindow));
            window.Show();
        }

        private void OnGUI()
        {
            _topPanelHidden = EditorGUILayout.BeginToggleGroup("static utils", _topPanelHidden);
            if (_topPanelHidden)
            {
                _append = GUILayout.Toggle(_append, "Append");

                if (GUILayout.Button("Process Level Stats"))
                    ProcessLevelStats();
            }
            EditorGUILayout.EndToggleGroup();

            GridStateOnGUI();
        }

        private void GridStateOnGUI()
        {
            if (!Application.isPlaying)
            {
                if (_editorDebugGrid == null)
                {
                    const int debugSizeX = 4;
                    const int debugSizeY = 4;
                    _editorDebugGrid = new Grid(debugSizeX, debugSizeY);
                    _currentSelectedCell = null;
                    for (int x = 0; x < debugSizeX; x++)
                    {
                        for (int y = 0; y < debugSizeY - x; y++)
                        {
                            var cell = new Cell(new Coords(x, y));
                            cell.AddTile(TileFactory.CreateTile(_editorDebugGrid.Cells.Count, TileAsset.Simple, new TileOrigin(), TileKinds.Blue));
                            _editorDebugGrid.Cells.Add(cell);
                        }
                    }
                }

                GridStateDraw(_editorDebugGrid);
                return;
            }

            if (_gameController == null)
            {
                _gameController = GameObject.FindObjectOfType<GameController>();

                if (_gameController == null)
                {
                    return;
                }

                _currentSelectedCell = null;
                _editorDebugGrid = null;
            }

            var grid = _gameController.Level.Grid;
            if (grid == null) return;
            if (grid.Cells == null) return;

            GridStateDraw(grid);
        }

        private void GridStateDraw(Grid grid)
        {
            const float rowSize = 15f;
            const float cellW = 60f;
            const float cellH = 60f;
            const float spacing = 5f;
            const float originX = 5;
            const float originY = rowSize * 6 - cellH;
            var panelX = originX + grid.Width * (cellW + spacing) + 5;
            var panelY = rowSize * 6;
            var gridCellsCountY = grid.Height;
            var gridVisualSizeY = gridCellsCountY * cellH;

            for (int i = 0; i < grid.Cells.Count; i++)
            {
                var cell = grid.Cells[i];
                float x = originX + cell.Coords.X * (cellW + spacing);
                float y = originY + cellH * gridCellsCountY + spacing * (gridCellsCountY - 1) - cell.Coords.Y * (cellH + spacing);

                if (GUI.Button(new Rect(x, y, cellW, cellH), ""))
                {
                    _currentSelectedCell = cell;
                }

                GUI.Label(new Rect(x, y, cellW, cellH / 3f), $"X:{cell.Coords.X} Y:{cell.Coords.Y};");
                var tileId = cell.Tile.IsNull() ? "null" : "<" + cell.Tile.Id + ">";
                GUI.Label(new Rect(x, y + cellH / 3f, cellW, cellH / 3f), tileId);
                if (!cell.Tile.IsNull())
                {
                    var cellInfoText = cell.Tile.Asset == TileAsset.Simple ? cell.Tile.Kind.ToString() : cell.Tile.State.ToString();
                    GUI.Label(new Rect(x, y + 2 * cellH / 3f, cellW, cellH / 3f), cellInfoText);
                }
            }

            if (_currentSelectedCell != null)
            {
                GUILayout.BeginArea(new Rect(panelX, panelY, position.width - panelX - 5f, gridVisualSizeY));
                {
                    _scrollCellInfo = GUILayout.BeginScrollView(scrollPosition: _scrollCellInfo);
                    {
                        EditorGUILayout.LabelField($"Pos: X={_currentSelectedCell.Coords.X}, Y={_currentSelectedCell.Coords.Y}");
                        EditorGUILayout.LabelField($"Busy={_currentSelectedCell.IsBusy}");
                        EditorGUILayout.LabelField($"Dir={_currentSelectedCell.CellDir}");
                        EditorGUILayout.LabelField($"CellState={_currentSelectedCell.State}");
                        EditorGUILayout.LabelField($"SizeX={_currentSelectedCell.SizeX}");
                        EditorGUILayout.LabelField($"SizeY={_currentSelectedCell.SizeY}");
                        EditorGUILayout.LabelField($"TNT Count={_currentSelectedCell.TntCount}");
                        if (_currentSelectedCell.Walls != null)
                        {
                            EditorGUILayout.LabelField($"Wall={_currentSelectedCell.Walls.Directions}");
                        }
                        if (_currentSelectedCell.InvisibleWalls != null)
                        {
                            EditorGUILayout.LabelField($"InvisibleWall={_currentSelectedCell.InvisibleWalls.Directions}");
                        }
                        var directions = _gameController == null ? null : _gameController.M3SimPlayerDEBUG?.GravitySystemDEBUG?.CellsAndDirectionsDEBUG;
                        if (directions != null)
                        {
                            if (directions.ContainsKey(_currentSelectedCell.Coords))
                            {
                                var p = directions[_currentSelectedCell.Coords];
                                EditorGUILayout.LabelField($"GravityRoute: {p.Directions}");
                            }
                        }

                        if (_currentSelectedCell.HasMultiSizeCellReference())
                        {
                            foreach (var otherCell in _currentSelectedCell.CachedMultiSizeCellsReferences)
                            {
                                EditorGUILayout.LabelField($"Under multi-size tile at pos: " + otherCell.Coords);
                            }
                        }
                        else
                        {
                            EditorGUILayout.LabelField($"Not under multi-size tile");
                        }

                        if (_currentSelectedCell.Tile.IsNull())
                        {
                            EditorGUILayout.LabelField($"Tile: null");
                        }
                        else
                        {
                            var tile = _currentSelectedCell.Tile;
                            var tileParams = tile.GetParamsDto();
                            EditorGUILayout.LabelField($"Tile: <{tile.Id}>");
                            EditorGUILayout.LabelField($"TileState = {tile.State}");
                            EditorGUILayout.LabelField($"TileSpeciality = {tile.Speciality}");
                            EditorGUILayout.LabelField($"TileKind = {tile.Kind}");

                            if (tileParams != null)
                            {
                                foreach (var p in tileParams)
                                {
                                    EditorGUILayout.LabelField($"> param '{p.Param}' = " + p.Value);
                                }
                            }
                        }

                    }
                    GUILayout.EndScrollView();
                }
                GUILayout.EndArea();
            }
        }

        private void DrawLogicalActions(Rect areaRect, GameSimulation gameSim)
        {
            GUILayout.BeginArea(areaRect, "", "box");
            {
                _scrollActions = EditorGUILayout.BeginScrollView(_scrollActions);
                {
                    EditorGUILayout.LabelField("Logical Actions List");
                    EditorGUILayout.LabelField("Result = " + gameSim.Result);

                    foreach (var step in gameSim.GetAllSimulationSteps())
                    {
                        EditorGUILayout.LabelField("### Step " + step.turn + " ###");

                        foreach (var v in step.actions)
                        {
                            EditorGUILayout.LabelField("Action:" + v.GetType().Name + ":" + v.ToString());
                        }
                    }
                }
                EditorGUILayout.EndScrollView();
            }
            GUILayout.EndArea();
        }

        private void ProcessLevelStats()
        {
            var folderPath = EditorUtility.OpenFolderPanel("Select Folder", Application.dataPath, string.Empty);
            var csvFiles = Directory.GetFiles(folderPath);

            var path = "Assets/GameAssets/Resources/AI/AiTrainingData.asset";
            var trainingDataObject = CreateInstance<AiTrainingDataObject>();

            var colToGoalMap = new Dictionary<int, GoalType>
            {
                { 6, GoalType.Backgrounds },
                { 7, GoalType.Stickers },
                { 8, GoalType.Litters },
                { 9, GoalType.DropItems },
                { 10, GoalType.IceCubes },
                { 11, GoalType.Pinata },
                { 12, GoalType.Animal },
                { 13, GoalType.Animal },
                { 14, GoalType.ColorCrate },
                { 15, GoalType.Watermelon },
                { 16, GoalType.Vase },
                { 17, GoalType.MoneyBag },
                { 18, GoalType.Penguin },
                { 19, GoalType.Egg },
                { 20, GoalType.Bird },
                { 21, GoalType.Banana },
                { 22, GoalType.Sheep },
                { 23, GoalType.Skunk },
                { 24, GoalType.Chicken },
                { 25, GoalType.Bee },
                { 26, GoalType.Mole },
                { 27, GoalType.BowlingPin },
                { 28, GoalType.Bush },
                { 29, GoalType.SodaBottle },
                { 30, GoalType.Safe },
                { 31, GoalType.FlowerPot },
                { 32, GoalType.Petal },
                { 33, GoalType.Chains },
                { 34, GoalType.IceBar },
                { 35, GoalType.DynamiteStick },
                { 36, GoalType.GiantPinata },
                { 37, GoalType.Shelf },
                { 38, GoalType.JellyFish },
                { 39, GoalType.DestructibleWall },
            };

            int minIndex = -1;
            int maxIndex = -1;
            foreach (var kv in colToGoalMap)
            {
                if (minIndex == -1)
                {
                    minIndex = kv.Key;
                    maxIndex = kv.Key;
                }
                else
                {
                    if (kv.Key < minIndex)
                    {
                        minIndex = kv.Key;
                    }

                    if (kv.Key > maxIndex)
                    {
                        maxIndex = kv.Key;
                    }
                }
            }

            foreach (var file in csvFiles)
                if (file.EndsWith(".csv"))
                {
                    var lines = File.ReadAllLines(file);
                    for (int i = 1; i < lines.Length; i++)
                    {
                        var line = lines[i];
                        var cellContents = line.Split(new char[] { ',' }, StringSplitOptions.None);

                        var levelName = cellContents[2];

                        var firstOrDefault =
                            trainingDataObject.GoalData.FirstOrDefault(instance => instance.LevelUid.Equals(levelName));

                        if (firstOrDefault == null)
                        {
                            firstOrDefault = new AiTrainingDataObject.LevelInstance
                            {
                                LevelUid = levelName,
                                Data = new List<AiTrainingDataObject.Pair>()
                            };

                            trainingDataObject.GoalData.Add(firstOrDefault);
                        }

                        if (!string.IsNullOrWhiteSpace(cellContents[4]))
                        {
                            var scores = int.Parse(cellContents[4]);
                            var pair = new AiTrainingDataObject.Pair { Type = (long)GoalType.Score, Count = scores };
                            firstOrDefault.Data.Add(pair);
                        }

                        if (!string.IsNullOrWhiteSpace(cellContents[5]) && !cellContents[5].Contains('?'))
                        {
                            var trimmedContents = cellContents[5].Replace("\"", "").Trim(' ', '{', '}');
                            var commaSplitArray = trimmedContents.Split(new char[] { ',' });

                            foreach (var contentString in commaSplitArray)
                            {
                                var colonSplitArray = contentString.Split(new char[] { ':' });
                                var tileKindGoalType = (GoalType)Enum.Parse(typeof(GoalType), colonSplitArray[0]);
                                var slashSplitArray = colonSplitArray[1].Split(new char[] { '/' });
                                var tileKindCount = int.Parse(slashSplitArray[0]);
                                var pair = new AiTrainingDataObject.Pair { Type = (long)tileKindGoalType, Count = tileKindCount };
                                firstOrDefault.Data.Add(pair);
                            }
                        }

                        for (int j = minIndex; j <= maxIndex; j++)
                        {
                            if (j >= cellContents.Length)
                                break;

                            var cellContent = cellContents[j];
                            if (!string.IsNullOrWhiteSpace(cellContent) && !cellContent.Contains('?'))
                            {
                                var slashSplitArray = cellContent.Split(new char[] { '/' });
                                var count = int.Parse(slashSplitArray[0]);
                                var pair = new AiTrainingDataObject.Pair { Type = (long)colToGoalMap[j], Count = count };
                                firstOrDefault.Data.Add(pair);
                            }
                        }
                    }
                }

            AssetDatabase.CreateAsset(trainingDataObject, path);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }
}
