using System;
using System.Collections.Generic;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3.Logic;
using BBB.Match3.Systems.GoalsService;
using FBConfig;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3
{
    [Serializable]
    public class GoalsSystemDto
    {
        public GoalStateDto goalsProgressLeft;
        public GoalStateDto assistGoalProgressLeft;
        public GoalStateDto originalGoals;
        public GoalStateDto originalAssistGoals;
        public GoalScoreRulesDto rules;

        public GoalsSystemDto()
        {
        }

        public bool Equals(GoalsSystemDto other)
        {
            return IsGoalStateDtoMatch(assistGoalProgressLeft, other.assistGoalProgressLeft) 
                   && IsGoalStateDtoMatch(goalsProgressLeft, other.goalsProgressLeft)
                   && IsGoalStateDtoMatch(originalAssistGoals, other.originalAssistGoals) 
                   && IsGoalStateDtoMatch(originalGoals, other.originalGoals);

            // don't check score rules.
            bool IsGoalStateDtoMatch(GoalStateDto a, GoalStateDto b)
            {
                if (IsGoalStateNullOrEmpty(a) && !IsGoalStateNullOrEmpty(b) || !IsGoalStateNullOrEmpty(a) && IsGoalStateNullOrEmpty(b))
                    return false;
                
                return IsGoalStateNullOrEmpty(a) || a.Equals(b);
            }

            bool IsGoalStateNullOrEmpty(GoalStateDto g)
            {
                return g?.state == null || g.state.Count == 0;
            }
        }
    }

    [Serializable]
    public class GoalScoreRulesDto
    {
        public DamageSource[] damageFlags;
        public Stage stage;
        public ScoreMultipliersDto scoreMultipliers;
        public BaseScoresDto baseScores;
        public List<TileStateHpScoreDto> tileStateHpDecreasesScores;
        public List<TileStateHpScoreDto> tileStateDestroyScores;
        public List<TileStateHpScoreDto> tileSpecialityDestroyScores;
        public List<TileStateHpScoreDto> tileSpecialityAdjacentDamageScores;
        public List<TileStateHpScoreDto> specialScores;
        public List<TileStateHpScoreDto> damageSourceBonusScores;

        [Serializable]
        public class ScoreMultipliersDto
        {
            public float comboMult;
            public float bestScoreMult;
            public float betterScoreMult;
            public float goodScoreMult;

            public ScoreMultipliersDto(ScoreMultipliers obj)
            {
                comboMult = obj.ComboMult;
                bestScoreMult = obj.BestScoreMult;
                betterScoreMult = obj.BetterScoreMult;
                goodScoreMult = obj.GoodScoreMult;
            }
            
            public ScoreMultipliersDto(ScoreMultipliersLocal obj)
            {
                comboMult = obj.ComboMult;
                bestScoreMult = obj.BestScoreMult;
                betterScoreMult = obj.BetterScoreMult;
                goodScoreMult = obj.GoodScoreMult;
            }

            public ScoreMultipliersLocal ConvertFromDto()
            {
                return new ScoreMultipliersLocal()
                {
                    ComboMult = comboMult,
                    BestScoreMult = bestScoreMult,
                    BetterScoreMult = betterScoreMult,
                    GoodScoreMult = goodScoreMult
                };
            }
        }

        public class ScoreMultipliersLocal
        {
            public float ComboMult { get; set; }
            public float BestScoreMult { get; set; }
            public float BetterScoreMult { get; set; }
            public float GoodScoreMult  { get; set; }

            public ScoreMultipliersLocal()
            {
                
            }

            public ScoreMultipliersLocal(ScoreMultipliers scoreMultipliers)
            {
                if(scoreMultipliers.IsNull())
                    return;
                
                ComboMult = scoreMultipliers.ComboMult;
                BestScoreMult = scoreMultipliers.BestScoreMult;
                BetterScoreMult = scoreMultipliers.BetterScoreMult;
                GoodScoreMult = scoreMultipliers.GoodScoreMult;
            }
        }

        public class BaseScoresLocal
        {
            public int NormalTileScore { get; set; }
            public int CometBaseScore { get; set; }
            public int BackgroundDamagedScore { get; set; }
            public int BackgroundRemovedScore  { get; set; }
            
            public BaseScoresLocal()
            {
                
            }

            public BaseScoresLocal(BaseScores baseScores)
            {
                if(baseScores.IsNull())
                    return;
                
                NormalTileScore = baseScores.NormalTileScore;
                CometBaseScore = baseScores.CometBaseScore;
                BackgroundDamagedScore = baseScores.BackgroundDamagedScore;
                BackgroundRemovedScore = baseScores.BackgroundRemovedScore;
            }
        }

        [Serializable]
        public class BaseScoresDto
        {
            public int normalTileScore;
            public int cometBaseScore;
            public int backgroundDamagedScore;
            public int backgroundRemovedScore;

            public BaseScoresDto(BaseScores obj)
            {
                normalTileScore = obj.NormalTileScore;
                cometBaseScore = obj.CometBaseScore;
                backgroundDamagedScore = obj.BackgroundDamagedScore;
                backgroundRemovedScore = obj.BackgroundRemovedScore;
            }
            
            public BaseScoresDto(BaseScoresLocal obj)
            {
                normalTileScore = obj.NormalTileScore;
                cometBaseScore = obj.CometBaseScore;
                backgroundDamagedScore = obj.BackgroundDamagedScore;
                backgroundRemovedScore = obj.BackgroundRemovedScore;
            }

            public BaseScoresLocal ConvertFromDto()
            {
                return new BaseScoresLocal()
                {
                    NormalTileScore = normalTileScore,
                    CometBaseScore = cometBaseScore,
                    BackgroundDamagedScore = backgroundDamagedScore,
                    BackgroundRemovedScore = backgroundRemovedScore
                };
            }
        }

        [Serializable]
        public struct TileStateHpScoreDto
        {
            public ulong State;
            public int Hp;
            public int Score;
            public TileSpeciality Speciality;
            public GoalActionType GoalAction;
            public DamageSource DamageSource;

            public TileStateHpScoreDto(TileState state, int score, int hp)
            {
                State = (ulong)state;
                Score = score;
                Hp = hp;
                Speciality = TileSpeciality.None;
                GoalAction = GoalActionType.None;
                DamageSource = DamageSource.None;
            }

            public TileStateHpScoreDto(TileState state, int score)
            {
                State = (ulong)state;
                Score = score;
                Hp = 0;
                Speciality = TileSpeciality.None;
                GoalAction = GoalActionType.None;
                DamageSource = DamageSource.None;
            }

            public TileStateHpScoreDto(TileSpeciality speciality, int score)
            {
                State = 0;
                Score = score;
                Hp = 0;
                Speciality = speciality;
                GoalAction = GoalActionType.None;
                DamageSource = DamageSource.None;
            }

            public TileStateHpScoreDto(GoalActionType goalAction, int score)
            {
                State = 0;
                Score = score;
                Hp = 0;
                Speciality = TileSpeciality.None;
                GoalAction = goalAction;
                DamageSource = DamageSource.None;
            }

            public TileStateHpScoreDto(DamageSource damageSource, int score)
            {
                State = 0;
                Score = score;
                Hp = 0;
                Speciality = TileSpeciality.None;
                GoalAction = GoalActionType.None;
                DamageSource = damageSource;
            }
        }
    }

    [Serializable]
    public class GoalStateDto
    {
        public List<GoalRecord> state;

        public bool Equals(GoalStateDto other)
        {
            bool isStateExist = state != null && state.Count > 0;
            bool isOtherStateExist = other.state != null && other.state.Count > 0;

            if (isStateExist && !isOtherStateExist || isOtherStateExist && !isStateExist) return false;
            if (!isStateExist) return true;
            if (state.Count != other.state.Count) return false;

            foreach (var item in state)
            {
                bool isGoalMatchFound = false;
                foreach (var otherItem in other.state)
                {
                    if (item.goalType == otherItem.goalType)
                    {
                        if (item.value != otherItem.value && item.goalType != (long)GoalType.Score) return false;
                        isGoalMatchFound = true;
                        break;
                    }
                }

                if (!isGoalMatchFound) return false;
            }

            return true;
        }
    }

    [Serializable]
    public struct GoalRecord
    {
        public long goalType;
        public int value;

        public GoalRecord(GoalType goal, int num)
        {
            goalType = (long)goal;
            value = num;
        }
    }
}