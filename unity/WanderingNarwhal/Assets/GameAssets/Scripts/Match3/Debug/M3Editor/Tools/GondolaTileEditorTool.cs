#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.CellTypes;
using BBB.M3Editor;
using BBB.Match3;
using BebopBee.Core;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class GondolaTileEditorTool : TileTool
    {
        public GondolaTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var originCell = M3EditorTile.GetGridCell(coords, false);

            if (originCell == null || !originCell.IsAnyOf(CellState.Water) ||
                originCell.IsAnyOf(CellState.FlagEnd)) return;

            var path = PathFactory.GetGondolaPath(originCell, grid);
            if (path == null || path.Count < 4) return;

            PathFactory.GetGondolaOrientation(path[0], path[1], out var orientation);

            PathFactory.GetGondolaSize(orientation, out var sizeX, out var sizeY);

            var mirror = orientation is 0 or 270;

            if (mirror)
                for (var x = coords.X; x < coords.X + sizeX; x++)
                for (var y = coords.Y; y > coords.Y - sizeY; y--)
                {
                    var pos = new Coords(x, y);
                    var cell = M3EditorTile.GetGridCell(pos, false);
                    if (cell == null || cell.HasMultiSizeCellReferenceWithMultiSizeTile()) return;
                }
            else
                for (var x = coords.X; x > coords.X - sizeX; x--)
                for (var y = coords.Y; y < coords.Y + sizeY; y++)
                {
                    var pos = new Coords(x, y);
                    var cell = M3EditorTile.GetGridCell(pos, false);
                    if (cell == null || cell.HasMultiSizeCellReferenceWithMultiSizeTile()) return;
                }

            if (mirror)
                for (var x = coords.X; x < coords.X + sizeX; x++)
                for (var y = coords.Y; y > coords.Y - sizeY; y--)
                {
                    grid.TryGetCell(new Coords(x, y), out var cell);
                    cell.HardRemoveTile(0);
                    cell.ClearState(true);
                    cell.UpdateCellBackgroundState();
                }
            else
                for (var x = coords.X; x > coords.X - sizeX; x--)
                for (var y = coords.Y; y < coords.Y + sizeY; y++)
                {
                    grid.TryGetCell(new Coords(x, y), out var cell);
                    cell.HardRemoveTile(0);
                    cell.ClearState(true);
                    cell.UpdateCellBackgroundState();
                }
            
            var tileParams = new List<TileParam>
            {
                new(TileParamEnum.SizeX, sizeX),
                new(TileParamEnum.SizeY, sizeY),
                new(TileParamEnum.GondolaOrientation, orientation)
            };

            var newTile = TileFactory.CreateTile(
                grid.TilesSpawnedCount++, TileAsset.Gondola,
                new TileOrigin(Creator.LevelEditor, originCell),TileKinds.None, tileParams);

            originCell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
