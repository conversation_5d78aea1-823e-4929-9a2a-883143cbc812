#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class MagicHatTileEditorTool : TileTool
    {
        public MagicHatTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords, false);
            if (cell == null) return;
            cell.HardRemoveTile(0);

            var newTile = TileFactory.CreateTile(
                grid.TilesSpawnedCount++, TileAsset.MagicHat,
                new TileOrigin(Creator.LevelEditor, cell));
            cell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
