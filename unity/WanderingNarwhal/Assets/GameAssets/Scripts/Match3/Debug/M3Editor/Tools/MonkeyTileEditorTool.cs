#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;
using BBB.Match3.Systems.CreateSimulationSystems;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class MonkeyTileEditorTool : TileTool
    {
        public MonkeyTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.Monkey, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var cell = M3EditorTile.GetGridCell(coords);
            if (cell == null) return;
            cell.HardRemoveTile(0);
            
            List<TileParam> tileParams = null;
            if (prm > 0)
                tileParams = new List<TileParam> { new() { Param = TileParamEnum.RestoresCount, Value = prm } };

            var adjacentHp = TileState.MonkeyMod.DefaultAdjacentHp();
            if (adjacentHp > 0)
            {
                tileParams ??= new List<TileParam>();
                tileParams.Add(new TileParam { Param = TileParamEnum.AdjacentHp, Value = adjacentHp });
            }

            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.Monkey, new TileOrigin(Creator.LevelEditor, cell),
                TileKinds.None, tileParams);
            cell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
