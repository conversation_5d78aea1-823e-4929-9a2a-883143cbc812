#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using BBB.CellTypes;
using BBB.Core;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3.Renderer;
using BBB.Match3.Systems;
using BBB.Match3.Systems.GoalsService;
using BBB.UI;
using BBB.UI.Level;
using GameAssets.Scripts.Match3.Logic;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorTestLevelsPopup : BbbMonoBehaviour, IContextInitializable
    {
        [SerializeField] private Button _closeButton;
        [SerializeField] private M3EditorTestLevelsLimitPanel _testLevelsPropertyPanel;
        [SerializeField] private Button _testParsingButton;
        [SerializeField] private Button _testLevelsButton;
        [SerializeField] private Button _fixIssuesButton;
        [SerializeField] private Button _fixTileSkinsButton;

        private GameController _gameController;
        private M3Editor _m3Editor;
        private M3EditorTest _m3EditorTest;
        private M3EditorLevelFileToConfigMap _fileToConfigMap;
        private SpawnerSettingsManager _spawnerSettingsManager;

        public void InitializeByContext(IContext context)
        {
            _m3Editor = context.Resolve<EditorLevelControllerLauncher>().M3Editor;
            _closeButton.ReplaceOnClick(Hide);
            _testParsingButton.ReplaceOnClick(TestParsingForSelectedLevels);
            _testLevelsButton.ReplaceOnClick(BruteTestSelectedLevels);
            
           //AT: Enable these buttons as needed
           //_fixIssuesButton.ReplaceOnClick(ConvertLevels);
           //_fixIssuesButton.ReplaceOnClick(ReplaceTileKinds);
           //_fixTileSkinsButton.ReplaceOnClick(FixTileSkinsInLevels);
        }

        public void Show()
        {
            var context = _m3Editor.Context;
            _gameController = context.Resolve<GameController>();
            _fileToConfigMap = context.Resolve<M3EditorLevelFileToConfigMap>();
            _spawnerSettingsManager = context.Resolve<SpawnerSettingsManager>();
            _m3EditorTest = _m3Editor.GetSystem<M3EditorTest>();

            RefreshView();
            gameObject.SetActive(true);
        }

        private void ReplaceTileKinds()
        {
            var folderPath = GetFolderPath();

            if (string.IsNullOrEmpty(folderPath))
                return;

            var filePaths = Directory.GetFiles(folderPath, "*" + ".M3L.bytes", SearchOption.AllDirectories);
            foreach (var filePath in filePaths)
            {
                if (filePath.Contains("AB") || filePath.Contains("03.M3L") || filePath.Contains("02.M3L") ||
                    filePath.Contains("_test") || filePath.Contains("_temp") || filePath.Contains("_bugs"))
                {
                    continue;
                }

                M3SaveLoadUtility.LoadLevelFromFile(filePath, out var level);
                var whiteChangeDetected = ReplaceTiles(level, TileKinds.White);
                var purpleChangeDetected = ReplaceTiles(level, TileKinds.Purple);
                
                if (whiteChangeDetected || purpleChangeDetected)
                {
                    M3SaveLoadUtility.SaveLevelIntoFile(level, filePath);
                }
            }
        }

        private static bool ReplaceTiles(Level level, TileKinds replaceColor)
        {
            var changeDetected = false;

            var replacementColors = new[] { TileKinds.Blue, TileKinds.Red, TileKinds.Yellow, TileKinds.Green };

            foreach (var color in replacementColors)
            {
                if (level.UsedKinds.Contains(replaceColor) && !level.UsedKinds.Contains(color))
                {
                    changeDetected = true;
                    level.UsedKinds.Add(color);
                    level.UsedKinds.Remove(replaceColor);

                    foreach (var cell in level.Grid.Cells)
                    {
                        if (cell.HasTile() && cell.Tile.Kind == replaceColor)
                        {
                            cell.Tile.Kind = color;
                        }

                        if (cell.IsAnyOf(CellState.Tnt) && cell.TntTarget is TntTargetType.Simple &&
                            cell.TntKind == replaceColor)
                        {
                            cell.TntKind = color;
                        }

                        if (cell.HasTile())
                        {
                            if (cell.Tile.Speciality is TileSpeciality.Soda)
                            {
                                var currentCount = cell.Tile.GetParam(TileParamEnum.SodaBottlesCount);
                                var currentState = cell.Tile.GetParam(TileParamEnum.SodaColors);
                                for (var i = 0; i < currentCount; i++)
                                {
                                    if (SodaTileLayer.GetColorNumFromState(currentState, i) ==
                                        SodaTileLayer.ColorToEncodedNum(replaceColor))
                                    {
                                        currentState = SodaTileLayer.SetColorNumInState(currentState,
                                            SodaTileLayer.ColorToEncodedNum(color), i);
                                    }
                                }

                                cell.Tile.SetParam(TileParamEnum.SodaColors, currentState);
                            }

                            if (cell.Tile.Speciality is TileSpeciality.DynamiteBox)
                            {
                                var currentCount = cell.Tile.GetParam(TileParamEnum.DynamiteSticksCount);
                                var currentState = cell.Tile.GetParam(TileParamEnum.DynamiteBoxColors);
                                for (var i = 0; i < currentCount; i++)
                                {
                                    if (DynamiteBoxTileLayer.GetColorNumFromState(currentState, i) ==
                                        DynamiteBoxTileLayer.ColorToEncodedNum(replaceColor))
                                    {
                                        currentState = DynamiteBoxTileLayer.SetColorNumInState(currentState,
                                            DynamiteBoxTileLayer.ColorToEncodedNum(color), i);
                                    }
                                }

                                cell.Tile.SetParam(TileParamEnum.DynamiteBoxColors, currentState);
                            }

                            if (cell.Tile.Speciality is TileSpeciality.Squid)
                            {
                                var currentCount = cell.Tile.GetParam(TileParamEnum.SquidsCount);
                                var currentState = cell.Tile.GetParam(TileParamEnum.SquidsState);
                                for (var i = 0; i < currentCount; i++)
                                {
                                    if (SquidTileLayer.GetColorNumFromState(currentState, i) ==
                                        SquidTileLayer.ColorToEncodedNum(replaceColor))
                                    {
                                        currentState = SquidTileLayer.SetColorNumInState(currentState,
                                            SquidTileLayer.ColorToEncodedNum(color), i);
                                    }
                                }

                                cell.Tile.SetParam(TileParamEnum.SquidsState, currentState);
                            }

                            if (cell.Tile.Speciality is TileSpeciality.TukTuk)
                            {
                                var currentColor = cell.Tile.GetParam(TileParamEnum.TukTukColor);
                                if (currentColor == (int)replaceColor)
                                {
                                    cell.Tile.SetParam(TileParamEnum.TukTukColor, (int)color);
                                }
                            }

                            if (cell.Tile.Speciality is TileSpeciality.ColorCrate)
                            {
                                cell.Tile.Kind = color;
                            }
                        }
                    }
                }
            }

            return changeDetected;
        }

        private void RefreshView()
        {

        }

        private string GetFolderPath()
        {

            var folder = Application.dataPath + "/Levels/";
            bool testAllLevels = _testLevelsPropertyPanel != null && _testLevelsPropertyPanel.AllLevelsToggleValue;
            string folderPath;

            if (testAllLevels)
            {
                //todo: set proper path only for city levels.
                folderPath = folder;
            }
            else
            {
                folderPath = EditorUtility.OpenFolderPanel("Load level file", folder, string.Empty);
            }

            return folderPath;
        }

        private void TestParsingForSelectedLevels()
        {
            var folderPath = GetFolderPath();

            if (string.IsNullOrEmpty(folderPath))
                return;

            _gameController.StartCoroutineMethod(TestLevels(folderPath, false));
        }

        private void BruteTestSelectedLevels()
        {
            var folderPath = GetFolderPath();
            if (string.IsNullOrEmpty(folderPath))
                return;

            int passesLimit = _testLevelsPropertyPanel == null ? 0 : _testLevelsPropertyPanel.LimitValue;
            _gameController.StartCoroutineMethod(TestLevels(folderPath, true, passesLimit));
        }

        private IEnumerator TestLevels(string folderPath, bool bruteTestLevel, int maxPassesLimit = 0)
        {
            Hide();

            var filePaths = Directory.GetFiles(folderPath, "*" + LevelHelper.LEVELS_DOT_EXT, SearchOption.AllDirectories);
            var levelBackup = _gameController.Level;
            var progressPopup = _m3Editor.Context.Resolve<M3EditorProgressPopup>();
            progressPopup.SetProcessTitle(bruteTestLevel ? "Brute test levels of " + Path.GetFileNameWithoutExtension(folderPath) : "Testing levels files parsing and configs validness");
            var index = 0;
            var valuesSet = new HashSet<int>();
            foreach (TileKinds value in Enum.GetValues(typeof(TileKinds)))
                valuesSet.Add((int)value);

            var shouldBreak = false;
            var shouldSkip = new [] { false };
            progressPopup.SetCancelCallback(() => { shouldBreak = true; });
            progressPopup.SetSkipCallback(() => { shouldSkip[0] = true; });

            var totalStats = new List<(string levelUid, Match3Statistics stats)>();

            Level level;
            foreach (var filePath in filePaths)
            {
                if (shouldBreak)
                    break;

                progressPopup.ShowFirstBar(ShortenDisplayPath(filePath), index / (float)filePaths.Length);

                yield return null;

                BDebug.Log(LogCat.Match3, $"Parsing file {Path.GetFileName(filePath)}\n{filePath}");
                try
                {
                    M3SaveLoadUtility.LoadLevelFromFile(filePath, out level, allowSaveAsLastLevel: false);
                    var remoteData = _fileToConfigMap.GetRemoteDataForFile(filePath, false);
                    if (remoteData == null)
                    {
                        BDebug.LogWarning(LogCat.Config, $"{filePath} is not currently being referenced from LevelConfig");
                        continue;
                    }

                    level.SetupRemoteData(remoteData, _spawnerSettingsManager.SpawnerSettings, allowAutoFixGoals: false);
                    if (_testLevelsPropertyPanel.OnlyDropItemLevelsToggleValue)
                    {
                        if (!level.Grid.HasDropItems())
                        {
                            bruteTestLevel = false;
                        }
                    }

                    _m3EditorTest.RunSimulation(level);

                    foreach (var kind in level.UsedKinds)
                    {
                        if (!valuesSet.Contains((int)kind))
                        {
                            Debug.LogError($"For level {filePath} corrupted tile kind found");
                        }
                    }

                    if (level.Grid.Width > Grid.MaxDimensionX)
                    {
                        BDebug.LogErrorFormat(LogCat.Match3, "For {0} grid width > 9", filePath);
                        continue;
                    }

                    if (level.Grid.Height > Grid.MaxDimensionY)
                    {
                        BDebug.LogErrorFormat(LogCat.Match3, "For {0} grid width > 9", filePath);
                        continue;
                    }

                    LevelIssuesDoctor.CheckInvalidTilesStates(level);
                    LevelIssuesDoctor.CheckGoalsInLevel(level, remoteData, _spawnerSettingsManager.SpawnerSettings);
                    LevelIssuesDoctor.CheckDropItemsHaveExit(level, filePath);
                    LevelIssuesDoctor.CheckTntTargets(level, _spawnerSettingsManager.SpawnerSettings);
                    LevelIssuesDoctor.CheckForColoredTargets(level, _spawnerSettingsManager.SpawnerSettings);
                }
                catch (Exception e)
                {
                    BDebug.LogError(LogCat.Match3, "Parsing exception in level +" + filePath + ": " + e.Message);
                    totalStats.Add((Path.GetFileName(filePath), null));
                    continue;
                }

                yield return null;

                if (bruteTestLevel)
                {
                    _gameController.SetLevel(level);
                    _m3Editor.Context.Resolve<GoalsSystem>().RefreshForLevel(level);
                    // int limitNumber;
                    // int.TryParse(_inputStatisticsLimitNumber.text, out limitNumber);

                    var autoBruteParams = new AutoBruteParams
                    {
                        Limit = LimitType.Goal,
                        PickLogic = _testLevelsPropertyPanel.PickLogic,
                        Runs = maxPassesLimit,
                        LimitNumber = level.TurnsLimit,
                        TestName = filePath,
                        AllowSnapshots = true,
                        ShowBruteProgress = true,
                        ForceContinueTestUntilFirstWinLimit = maxPassesLimit > 0 ? maxPassesLimit : 100,
                        GoalSyncCheck = true,
                        UseSuperBoost = true,
                    };

                    var autoBruteSystem = _m3Editor.Context.Resolve<AutoBruteSystem>();
                    shouldSkip[0] = false;

                    yield return autoBruteSystem.GatherStatistics(level, autoBruteParams, shouldSkip, (stats, code, message) =>
                    {
                        BDebug.Log(LogCat.Match3, stats);
                        totalStats.Add((level.Config.Uid, stats));

                        var initialMatchesTuple = stats.GetFirstOccurrenceOfInitialMatches();
                        if (initialMatchesTuple.HasValue)
                        {
                            var tuple = initialMatchesTuple.Value;
                            BDebug.LogError(LogCat.Match3, $"Initial matches: for level {level.LevelUid} on run number {tuple.runIndex+1} initial matches occured {tuple.matchesCount}");
                        }

                        foreach(var str in stats.GoalDeSyncStrList)
                            BDebug.LogError(LogCat.Match3, str);

                        // BDebug.LogError(LogCat.Match3, "Dummy error to test if level test works level: " + level.LevelUid);

                        if (stats.ShuffleFailedCount > 0 || stats.NoPossibleMovesAfterShuffleCount > 0)
                        {
                            BDebug.LogError(LogCat.Match3, $"Shuffle failed on level '{level.Config.Uid}' (stage {(level.Stage + 1)} '{(Stage)level.Stage}'): {stats.ShuffleFailedCount} times, game ended due no possible move: {stats.NoPossibleMovesAfterShuffleCount} times");
                        }

                        if (stats.GetWinsCount() <= 0)
                        {
                            BDebug.LogWarning(LogCat.Match3, $"Bot couldn't win level '{level.Config.Uid}' (stage {(level.Stage + 1)} '{(Stage)level.Stage}') at least once");

                            foreach (var goalType in stats.GoalResultTypes)
                            {
                                var count = stats.ResultsSum(goalType);
                                if (count <= 0)
                                {
                                    Debug.LogWarning($"Bot couldn't reach goal '{goalType}' on level {level.Config.Uid} at least once");
                                }
                            }
                        }
                    });
                }

                index++;
            }

            progressPopup.Hide();
            _gameController.SetLevel(levelBackup);
            _m3Editor.ApplyGrid(levelBackup.Grid);
        }

        private static string ShortenDisplayPath(string path)
        {
            if (path.StartsWith(Application.dataPath))
            {
                return ".." + path.Substring(Application.dataPath.Length);
            }
            return path;
        }



        private void Hide()
        {
            gameObject.SetActive(false);
        }
    }
}
#endif
