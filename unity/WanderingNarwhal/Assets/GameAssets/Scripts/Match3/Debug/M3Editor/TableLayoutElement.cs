using System.Collections.Generic;
using TMPro;
using UnityEngine;

namespace BBB.M3Editor
{
    public class TableLayoutElement : BbbMonoBehaviour
    {
        [SerializeField] private GameObject _rowInputTemplate;

        private RectTransform _selfTransform;
        
        private readonly List<TMP_InputField> _rowInputFields = new ();
        
        public void Apply(Dictionary<string, string> rowData, List<string> columnNames)
        {
            
            foreach (var inputField in _rowInputFields)
            {
                inputField.text = string.Empty;
                inputField.gameObject.SetActive(false);
            }
            
            for (var i = 0; i < columnNames.Count; i++)
            {
                TMP_InputField inputField = null;
                if (i < _rowInputFields.Count)
                {
                    inputField = _rowInputFields[i];
                }
                else
                {
                    var go = Instantiate(_rowInputTemplate, transform);
                    inputField = go.GetComponent<TMP_InputField>();
                    _rowInputFields.Add(inputField);
                }
                
                inputField.gameObject.SetActive(true);
                inputField.text = rowData.ContainsKey(columnNames[i]) ? 
                    rowData[columnNames[i]] 
                    : $"{columnNames[i]} not found";
            }
        }

        public Dictionary<string, string> GetRowData(List<string> columnNames)
        {
            var result = new Dictionary<string, string>();
            for (int i = 0; i < columnNames.Count; i++)
            {
                if (_rowInputFields[i].gameObject.activeSelf)
                {
                    result.Add(columnNames[i], _rowInputFields[i].text);
                }
            }

            return result;
        }
    }
}