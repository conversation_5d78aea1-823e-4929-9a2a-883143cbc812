#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.CellTypes;
using BBB.M3Editor;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public class BackgroundCellEditorTool : BaseEditorTool, ICellTool
    {
        public BackgroundCellEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.Grass, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            SetBackground(grid, coords, 1);
            M3EditorTile.RefreshGoals();
            M3EditorTile.RefreshGrid();
        }

        public override void UnApply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            SetBackground(grid, coords, -1);
            M3EditorTile.RefreshGoals();
            M3EditorTile.RefreshGrid();
        }

        private static void SetBackground(Grid grid, Coords coords, int count)
        {
            if (!grid.TryGetCell(coords, out var cell)) return;
            var newBackgroundCount = cell.BackgroundCount + count;
            newBackgroundCount = newBackgroundCount > 2 ? 0 : newBackgroundCount < 0 ? 0 : newBackgroundCount;
            cell.BackgroundCount = newBackgroundCount;

            switch (newBackgroundCount)
            {
                case 0:
                    cell.Remove(CellState.BackOne);
                    cell.Remove(CellState.BackDouble);
                    break;
                
                case 1:
                    cell.Remove(CellState.BackDouble);
                    cell.Add(CellState.BackOne);
                    break;
                
                case 2:
                    cell.Remove(CellState.BackOne);
                    cell.Add(CellState.BackDouble);
                    break;
            }
        }
    }
}
#endif
