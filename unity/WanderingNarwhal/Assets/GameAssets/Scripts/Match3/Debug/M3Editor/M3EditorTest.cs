#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using BBB.Core;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using BBB.UI.Level;
using Core.Debug;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;
using GameAssets.Scripts.Utils;
using UnityEditor;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
using Object = UnityEngine.Object;
using Path = System.IO.Path;

namespace BBB.M3Editor
{
    public class M3EditorTest : M3EditorBase
    {
        private Dropdown _dropDownStatisticsLimit;
        private Dropdown _dropDownStatisticsType;
        private InputField _inputStatisticsLimitRuns;
        private InputField _inputStatisticsLimitNumber;
        private readonly M3EditorLevelFileToConfigMap _fileToConfigMap;
        private SpawnerSettingsManager _spawnerSettingsManager;
        private TileResourceSelector _tileResources;

        public M3EditorTest(M3Editor m3Editor) : base(m3Editor)
        {
            _fileToConfigMap = m3Editor.Context.Resolve<M3EditorLevelFileToConfigMap>();
            _spawnerSettingsManager = m3Editor.Context.Resolve<SpawnerSettingsManager>();
            _tileResources = m3Editor.Context.Resolve<TileResourceSelector>();
        }

        public override void SetUpUi()
        {
            base.SetUpUi();

            var inputFieldChildren = _m3Editor.GetComponentsInChildren<InputField>(true);
            foreach (var child in inputFieldChildren)
            {
                if (child.name == "StatisticsLimitRuns")
                {
                    _inputStatisticsLimitRuns = child;
                    _inputStatisticsLimitRuns.text = "50";
                }

                if (child.name == "StatisticsLimitNumber")
                {
                    _inputStatisticsLimitNumber = child;
                    _inputStatisticsLimitNumber.text = "40";
                }
            }

            var dropdownChildren = _m3Editor.GetComponentsInChildren<Dropdown>(true);
            foreach (var child in dropdownChildren)
            {
                if (child.name == "StatisticsLimitDropDown")
                {
                    _dropDownStatisticsLimit = child;
                    _dropDownStatisticsLimit.ClearOptions();
                    _dropDownStatisticsLimit.AddOptions(Enum.GetNames(typeof(LimitType)).ToList());
                    _dropDownStatisticsLimit.value = (int) LimitType.Goal;
                }

                if (child.name == "StatisticsTypeDropDown")
                {
                    _dropDownStatisticsType = child;
                    _dropDownStatisticsType.ClearOptions();
                    _dropDownStatisticsType.AddOptions(Enum.GetNames(typeof(PickLogic)).ToList());
                    _dropDownStatisticsType.value =  (int) PickLogic.UsefulMovesWin;
                }
            }

            var children = _m3Editor.GetComponentsInChildren<Button>(true);
            foreach (var child in children)
            {
                if (child.name == "TestLevels")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(OpenTestLevelsPopup);

                    ObjectsDisabledDuringPlay.Add(child.gameObject);
                }

                if (child.name == "BalanceReport")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(GenerateBalanceReport);
                    ObjectsDisabledDuringPlay.Add(child.gameObject);
                }

                if (child.name == "CaptureAllLevels")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(CaptureScreenshotsForLevels);
                    ObjectsDisabledDuringPlay.Add(child.gameObject);
                }

                if (child.name == "TrainAi")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(TrainAi);
                    ObjectsDisabledDuringPlay.Add(child.gameObject);
                }

                if (child.name == "EditAiState")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(EditAiState);
                    ObjectsDisabledDuringPlay.Add(child.gameObject);
                }

                if (child.name == "SimulationTest")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(SimulationTest);
                    ObjectsDisabledDuringPlay.Add(child.gameObject);
                }

                if (child.name == "ShowTrainingData")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(ShowTrainingData);
                    ObjectsDisabledDuringPlay.Add(child.gameObject);
                }

                if (child.name == "GatherStatistics")
                {
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(GatherStatisticsForOpenedLevel);
                    ObjectsDisabledDuringPlay.Add(child.gameObject);
                }
            }
        }

        private void GatherStatisticsForOpenedLevel()
        {
            _m3Editor.BackUpGrid();

            var limitType = Enum.GetValues(typeof(LimitType))
                .Cast<LimitType>()
                .ElementAt(_dropDownStatisticsLimit.value);

            var pickLogic = Enum.GetValues(typeof(PickLogic))
                .Cast<PickLogic>()
                .ElementAt(_dropDownStatisticsType.value);

            int.TryParse(_inputStatisticsLimitRuns.text, out var limitRuns);
            int.TryParse(_inputStatisticsLimitNumber.text, out var limitNumber);

            var autoBruteSystem = _m3Editor.Context.Resolve<AutoBruteSystem>();
            var autoBruteParams = new AutoBruteParams
            {
                Limit = limitType,
                PickLogic = pickLogic,
                Runs = limitRuns,
                LimitNumber = limitNumber,
                TestName = "OpenedLevel",
                AllowSnapshots = true,
                ShowBruteProgress = true,
                UseSuperBoost = true,
                GoalSyncCheck = true
            };

            var level = GameController.Level;

            var currentFile = M3SaveLoadUtility.CurrentLevelUid;
            var filePath = _fileToConfigMap.GetFilePathWithLevelUid(currentFile);
            var remoteData = _fileToConfigMap.GetRemoteDataForFile(filePath);

            LevelIssuesDoctor.CheckInvalidTilesStates(level);
            LevelIssuesDoctor.CheckGoalsInLevel(level, remoteData, _spawnerSettingsManager.SpawnerSettings);
            LevelIssuesDoctor.CheckDropItemsHaveExit(level, filePath);

            var gatherStatsRoutine = autoBruteSystem.GatherStatistics(level, autoBruteParams, new bool[1], (stats, code, message) =>
            {
                _m3Editor.HistPopup.Show(stats);
                _m3Editor.RestoreGridFromBackUp();

                var initialMathcesTuple = stats.GetFirstOccurrenceOfInitialMatches();
                if (initialMathcesTuple.HasValue)
                {
                    var tuple = initialMathcesTuple.Value;
                    BDebug.LogError(LogCat.Match3, $"Initial matches: for level {level.LevelUid} on run number {tuple.runIndex+1} initial matches occured {tuple.matchesCount}");
                }

                foreach(var str in stats.GoalDeSyncStrList)
                    BDebug.LogError(LogCat.Match3, str);

                // BDebug.LogError(LogCat.Match3, "Dummy error to test if level test works level: " + level.LevelUid);


                if (stats.ShuffleFailedCount > 0 || stats.NoPossibleMovesAfterShuffleCount > 0)
                {
                    BDebug.LogError(LogCat.Match3, $"Shuffle failed on level '{level.Config.Uid}' (stage {(level.Stage + 1)} '{(Stage)level.Stage}'): {stats.ShuffleFailedCount} times, game ended due no possible move: {stats.NoPossibleMovesAfterShuffleCount} times");
                }

                if (stats.GetWinsCount() <= 0)
                {
                    BDebug.LogError(LogCat.Match3, $"Bot couldn't win level '{level.Config.Uid}' (stage {(level.Stage + 1)} '{(Stage)level.Stage}') at least once");

                    foreach (var goalType in stats.GoalResultTypes)
                    {
                        var count = stats.ResultsSum(goalType);
                        if (count <= 0)
                        {
                            Debug.LogError($"Bot couldn't reach goal '{goalType}' on level {level.Config.Uid} at least once");
                        }
                    }
                }

                var balanceReporter = _m3Editor.Context.Resolve<BalanceReporter>();
                var reportConfig = AiDataManagement.LoadBalacingReportConfig();
                var goalsDict = level.Goals.ToNameDict();
                var txtResult = BalanceReporter.ConvertStatsToReportString(reportConfig, stats, goalsDict, code);
                GUIUtility.systemCopyBuffer = txtResult;
                Debug.Log("Level statistics has been copied into clipboard");
            });
            
            GameController.StartCoroutineMethod(gatherStatsRoutine);
        }

        private void GenerateBalanceReport()
        {
            var balanceReporter = _m3Editor.Context.Resolve<BalanceReporter>();
            balanceReporter.GenerateBalanceReportFor();
        }

        private void OpenTestLevelsPopup()
        {
            var context = _m3Editor.Context;
            var popup = context.Resolve<M3EditorTestLevelsPopup>();
            popup.Show();
        }

        private void CaptureScreenshotsForLevels()
        {
            var startingPath = PlayerPrefs.GetString("LevelEditor.LastLevelsPath", Application.dataPath + "/Levels/");
            var levelsFolderPath = EditorUtility.OpenFolderPanel("Step 1. Select levels folder", startingPath, string.Empty);
            var destinationFolderPath = EditorUtility.OpenFolderPanel("Step 2. Select Images Destination Folder Outside Unity.", levelsFolderPath, string.Empty);
            this._m3Editor.StartCoroutineMethod(LevelsScreenshotsCaptureProcessAllDirectories(destinationFolderPath, levelsFolderPath));
        }

        /// <summary>
        /// Coroutine for making screenshots for all levels in rootDirectory (and subdirectories) which will put them inside destination directory with same folders structure.
        /// </summary>
        private IEnumerator LevelsScreenshotsCaptureProcessAllDirectories(string destinationDirectory, string rootDirectory)
        {
            if (!Directory.Exists(destinationDirectory))
            {
                BDebug.Log("Destination directory not found");
                yield break;
            }
            var processedDirectories = 0;
            var capturingCamera = GameObject.FindObjectOfType<Canvas>().worldCamera;
            var files = Directory.GetFiles(rootDirectory, "*" + LevelHelper.LEVELS_DOT_EXT);
            if (files.Length > 0)
            {
                yield return LevelsScreenshotsCaptureRoutineInSingleDirectory(destinationDirectory, files, capturingCamera);
                processedDirectories++;
            }

            // Process all children directories at any depth WITHOUT recursion.
            var directories = new List<string[]>();
            var indexes = new List<int>();
            var currentDepth = 0;
            directories.Add(Directory.GetDirectories(rootDirectory));
            indexes.Add(0);

            // Loop while not processed all directories.
            // No AllDirectories option used in GetFiles API to avoid infinity loop.
            var foldersLimit = 500;
            while (!(currentDepth == 0 && indexes[0] >= directories[0].Length) && foldersLimit > 0)
            {
                foldersLimit--;
                var directory = directories[currentDepth][indexes[currentDepth]];
                files = Directory.GetFiles(directory, "*" + LevelHelper.LEVELS_DOT_EXT);
                if (files.Length > 0)
                {
                    var subpath = directory.Substring(rootDirectory.Length);
                    while (subpath != null && subpath.Length > 1 && (subpath.StartsWith("/") || subpath.StartsWith("\\")))
                    {
                        subpath = subpath.Substring(1);
                    }
                    var destination = Path.Combine(destinationDirectory, subpath);
                    yield return LevelsScreenshotsCaptureRoutineInSingleDirectory(destination, files, capturingCamera);
                    processedDirectories++;
                }
                var nestedDirectories = Directory.GetDirectories(directory);
                if (nestedDirectories.Length > 0)
                {
                    directories.Add(nestedDirectories);
                    indexes.Add(0);
                    currentDepth++;
                }
                else
                {
                    indexes[currentDepth]++;
                    while (currentDepth > 0 && indexes[currentDepth] > directories[currentDepth].Length - 1)
                    {
                        directories.RemoveAt(currentDepth);
                        indexes.RemoveAt(currentDepth);
                        currentDepth--;
                        indexes[currentDepth]++;
                    }
                }
            }
            if (processedDirectories == 0)
            {
                BDebug.Log("No m3 levels found in target directory or any subdirectory.");
            }
            else
            {
                var loadedLevel = SceneManager.GetActiveScene();
                SceneManager.LoadScene(loadedLevel.buildIndex);
            }
        }

        /// <summary>
        /// Coroutine to process levels files by specified paths array and put screenshot png files of levels into destination directory.
        /// </summary>
        private IEnumerator LevelsScreenshotsCaptureRoutineInSingleDirectory(string destinationDirectory, string[] files, Camera capturingCamera)
        {
            if (files.Length == 0)
            {
                BDebug.Log("Levels not found in root directory");
                yield break;
            }

            BDebug.Log("Processing directory: " + destinationDirectory);
            var playAreaRatio = _m3Editor.Context.Resolve<UI.EditorLevelControllerLauncher>().LevelHolder.GetComponent<RectTransform>().anchorMax.x;
            foreach (var file in files)
            {
                var levelName = Path.GetFileName(file);
                BDebug.Log("Opening level for capturing: " + levelName);
                var filePath = M3SaveLoadUtility.LoadLevelFromFile(file, out var level);
                var levelRemoteData = _fileToConfigMap.GetRemoteDataForFile(filePath, false);
                if (levelRemoteData == null)
                {
                    continue;
                }

                level.SetupRemoteData(levelRemoteData, _spawnerSettingsManager.SpawnerSettings);
                GameController.SetLevel(level);
                var fileName = M3SaveLoadUtility.GetShortPath(file);
                try
                {
                    _m3Editor.HardRestartWith(fileName, level.Grid);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Exception encountered on level '{levelName}'");
                    Debug.LogException(ex);
                    continue;
                }

                BDebug.Log($"Start play mode for {levelName}");
                var gameController = _m3Editor.GameController;
                _m3Editor.PlayState = true;
                _m3Editor.ClearTool();
                gameController.SimulateLoopException = true;

                yield return WaitCache.EndOfFrame;
                _m3Editor.OnGameRun(_m3Editor.PlayState, true);
                gameController.LockInput(true);
                yield return WaitCache.Seconds(1.5f);

                BDebug.Log($"Start capturing screenshot for {levelName}");
                var width = Mathf.CeilToInt(capturingCamera.pixelWidth * playAreaRatio * 0.5f);
                var height = capturingCamera.pixelHeight;
                var renderTexture = new RenderTexture(capturingCamera.pixelWidth, capturingCamera.pixelHeight, 24);
                RenderTexture.active = renderTexture;
                capturingCamera.targetTexture = renderTexture;
                capturingCamera.Render();
                var screenShot = new Texture2D(width, height, TextureFormat.RGB24, false);
                screenShot.ReadPixels(new Rect(width * 0.24f, 0, width, height), 0, 0, recalculateMipMaps: false);
                capturingCamera.targetTexture = null;
                Object.Destroy(renderTexture);
                var bytes = screenShot.EncodeToPNG();
                if (!Directory.Exists(destinationDirectory))
                {
                    Directory.CreateDirectory(destinationDirectory);
                }
                var spritePath = Path.Combine(destinationDirectory, Path.GetFileNameWithoutExtension(file) + ".png");
                BDebug.Log("Put sprite in: " + spritePath);
                File.WriteAllBytes(spritePath, bytes);
                BDebug.Log("Stop level");
                _m3Editor.PlayState = false;
                _m3Editor.GameController.StopAllCoroutines();
                yield return null;
            }
            BDebug.Log($"Directory '{destinationDirectory}' processing complete");
        }

        private void TrainAi()
        {
            GameController.StartCoroutineMethod(TrainAiRoutine());
        }

        private void EditAiState()
        {
            var context = _m3Editor.Context;
            var popup = context.Resolve<M3EditorAiEditPopup>();
            popup.Show();
        }

        private void SimulationTest()
        {
            int simCount = 100;
            var logStopWatch = new LogStopwatch("red");
            logStopWatch.Start();

            //Profiler.BeginSample($"Running {simCount} Simulations");
            var totalTurnsDone = 0;
            for (int i = 0; i < simCount; i++)
                totalTurnsDone += RunSimulation(GameController.Level);
            //Profiler.EndSample();

            logStopWatch.StopLog($"{simCount} playouts finished in {totalTurnsDone} total turns in ");
        }

        public int RunSimulation(ILevel currentLevel)
        {
            var context = _m3Editor.Context;
            var goalSystem = context.Resolve<GoalsSystem>();
            goalSystem.RefreshForLevel(currentLevel);
            var gameControllerSettings = GameController.Settings;
            var gridToTest = currentLevel.Grid.Clone();
            gridToTest.DebugCurrentLevelUid = currentLevel.LevelUid;
            var turnCounter = 0;
            
            var pendingBoosters = new ExtraBoostersHelper.PendingBoosters(new List<AutoBoostInstance> {new("bomb"), new("linecrush"), new("lightningstrike")}, null);
            
            var simParams = new SimulationInputParams
            {
                UsedKinds = currentLevel.UsedKinds,
                Settings = gameControllerSettings,
                SimulateLoopException = GameController.SimulateLoopException,
                TurnsLimit = currentLevel.TurnsLimit,
                PendingBoosters = pendingBoosters
            };

            var spawnSystem = context.Resolve<M3SpawnSystem>().Clone();
            spawnSystem.Setup(currentLevel);
            spawnSystem.ReplaceSpawnerSettingsManager(GameController.DebugSpanwerSettingsManager);

            var gravitySystem = new GravitySystem(simParams, goalSystem, _tileResources, null, null);
            gravitySystem.CreateSimulationSync(
                grid: gridToTest,
                playerInput: new PlayerInputNone(),
                remainingMoves: currentLevel.TurnsLimit,
                assistParams: null,
                spawnSystem: spawnSystem);

            while (!goalSystem.HasCompletedGoals())
            {
                var allPossibleMoves = SearchMatchesSystem.SearchForAllPossibleMoves(gridToTest).ToList();
                var selectedPossibleMove = allPossibleMoves.GetRandomItem();
                var inputSwap =  SearchMatchesSystem.ToPlayerInput(selectedPossibleMove);

                //Profiler.BeginSample($"Running CreateSimulationSync");
                new GravitySystem(simParams, goalSystem, _tileResources, null, null).CreateSimulationSync(
                    grid: gridToTest,
                    playerInput: inputSwap,
                    remainingMoves: currentLevel.TurnsLimit - turnCounter,
                    assistParams: null,
                    spawnSystem: spawnSystem);
                //Profiler.EndSample();

                turnCounter++;

                if (turnCounter >= currentLevel.TurnsLimit)
                {
                    break;
                }
            }

            goalSystem.RefreshForLevel(currentLevel);
            Debug.Log($"Game simulation finished in {turnCounter} moves");
            return turnCounter;
        }

        private void ShowTrainingData()
        {
            var trainingData = AiDataManagement.LoadLevelsTrainingData("AiTrainingData");
            var popup = _m3Editor.Context.Resolve<M3EditorLevelSelectionPopup>();
            popup.ShowLevelSelection(trainingData.Keys, selectedLevelName =>
            {
                var stats = trainingData[selectedLevelName];
                _m3Editor.HistPopup.Show(stats);
            });
        }

        private IEnumerator TrainAiRoutine()
        {
            var trainingSystem = _m3Editor.Context.Resolve<ITrainingSystem>();
            yield return trainingSystem.TrainRoutine();
        }
    }
}
#endif
