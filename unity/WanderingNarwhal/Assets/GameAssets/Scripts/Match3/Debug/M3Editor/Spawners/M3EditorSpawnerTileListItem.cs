#if UNITY_EDITOR
using System;
using BBB.Match3.Systems;
using UnityEngine;
using TMPro;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorSpawnerTileListItem : BbbMonoBehaviour
    {
        public M3EditorSpawnerTileImage PreviewImage;
        public TMP_Dropdown TileAssetDropdown;
        public TMP_Dropdown TileKindDropDown;
        public Toggle VaseToggle;
        public TextMeshProUGUI HpText;
        public Slider HpSlider;
        public Slider WeightSlider;
        public TMP_InputField WeightInputField;
        public Toggle SpawnIfGoalNotReachedToggle;
        public TMP_InputField MaxItemsOnGridInputField;
        public Button RemoveButton;
        public Action<int> OnTileDeleteClickedEvent;
        private TileSpawnSettings _currentTile;
        private int _currentIndex;
        private ISpawnerTileImageProvider _currentImageProvider;

        private readonly TileAsset[] _allowedAssets = {
            TileAsset.Simple,
            TileAsset.Litter,
            TileAsset.Pinata,
            TileAsset.Watermelon,
            TileAsset.DropItem,
            TileAsset.ColorBomb,
            TileAsset.RowBreaker,
            TileAsset.ColumnBreaker,
            TileAsset.Bomb,
            TileAsset.MoneyBag,
            TileAsset.Penguin,
            TileAsset.Egg,
            TileAsset.Bird,
            TileAsset.FlowerPot
        };

        private TileKinds[] _allowedTileKinds = new[]
        {
            TileKinds.Blue,
            TileKinds.Green,
            TileKinds.Red,
            TileKinds.Yellow,
            TileKinds.Undefined,
        };

        private bool _isRefreshing = false;

        private void Awake()
        {
            InitDropDowns();

            RemoveButton.onClick.AddListener(() =>
            {
                if (_currentTile != null)
                {
                    OnTileDeleteClickedEvent?.Invoke(_currentIndex);
                }
            });
            Subscribe();
        }

        private void Subscribe()
        {
            TileAssetDropdown.onValueChanged.AddListener((value) =>
            {
                if (_isRefreshing || _currentTile == null) return;

                _currentTile.Asset = _allowedAssets[value];
                InternalRefresh();
            });

            TileKindDropDown.onValueChanged.AddListener((value) =>
            {
                if (_isRefreshing || _currentTile == null) return;

                _currentTile.Kind = _allowedTileKinds[value];
                InternalRefresh();
            });

            VaseToggle.onValueChanged.AddListener((f) =>
            {
                if (_isRefreshing || _currentTile == null) return;

                if (f)
                {
                    _currentTile.Mods = (long)((TileState)_currentTile.Mods | TileState.Vase);
                    if (!_currentTile.IsExistParam(TileParamEnum.VaseLayerCount))
                    {
                        _currentTile.SetParamValue(TileParamEnum.VaseLayerCount, 1);
                    }
                }
                else
                {
                    _currentTile.Mods = (long)((TileState)_currentTile.Mods & (~TileState.Vase));
                    _currentTile.RemoveParam(TileParamEnum.VaseLayerCount);
                }

                InternalRefresh();
            });

            HpSlider.onValueChanged.AddListener((v) => {
                if (_isRefreshing || _currentTile == null) return;

                SetHpValue(_currentTile, (int)v);
                InternalRefresh();
            });

            WeightSlider.onValueChanged.AddListener((v) => {
                if (_isRefreshing || _currentTile == null) return;

                _currentTile.ProbabilityWeight = v;
                InternalRefresh();
            });

            WeightInputField.onEndEdit.AddListener((str) => {
                if (_isRefreshing || _currentTile == null) return;

                int value = 0;
                int.TryParse(str, out value);
                _currentTile.ProbabilityWeight = value;
                InternalRefresh();
            });

            SpawnIfGoalNotReachedToggle.onValueChanged.AddListener((f) => {
                if (_isRefreshing || _currentTile == null) return;

                _currentTile.SpawnUntilGoalReached = f;
                InternalRefresh();
            });

            MaxItemsOnGridInputField.onEndEdit.AddListener((str) =>
            {
                if (_isRefreshing || _currentTile == null) return;

                int value = 0;
                int.TryParse(str, out value);
                _currentTile.MaxAllowedItemsOnGrid = value;
                InternalRefresh();
            });
        }

        private void InternalRefresh()
        {
            RefreshView(_currentTile, _currentIndex, _currentImageProvider);
        }

        private void InitDropDowns()
        {
            TileAssetDropdown.ClearOptions();

            foreach (var asset in _allowedAssets)
            {
                TileAssetDropdown.options.Add(new TMP_Dropdown.OptionData(asset.ToString()));
            }

            TileKindDropDown.ClearOptions();

            foreach (var kind in _allowedTileKinds)
            {
                TileKindDropDown.options.Add(new TMP_Dropdown.OptionData(kind.ToString()));
            }
        }

        public void RefreshView(TileSpawnSettings tile, int tileIndex, ISpawnerTileImageProvider imageProvider)
        {
            _isRefreshing = true;
            _currentTile = tile;
            _currentIndex = tileIndex;
            _currentImageProvider = imageProvider;
            RefreshTileAssetDropDown(tile);
            RefreshTileKindDropDown(tile);
            PreviewImage.SetupImage(tile, imageProvider);
            VaseToggle.isOn = (tile.Mods & (long)TileState.VaseMod) != 0;
            RefreshHpView(tile);
            RefreshWeightView(tile);
            SpawnIfGoalNotReachedToggle.isOn = tile.SpawnUntilGoalReached;
            MaxItemsOnGridInputField.text = tile.MaxAllowedItemsOnGrid > 0 ? tile.MaxAllowedItemsOnGrid.ToString() : "";
            _isRefreshing = false;
        }

        private void RefreshTileAssetDropDown(TileSpawnSettings tile)
        {
            var assetIndex = -1;
            for (int i = 0; i < _allowedAssets.Length; i++)
            {
                if (tile.Asset == _allowedAssets[i])
                {
                    assetIndex = i;
                    break;
                }
            }

            if (assetIndex < 0)
            {
                tile.Asset = _allowedAssets[0];
                assetIndex = 0;
            }

            TileAssetDropdown.value = assetIndex;
        }

        private void RefreshTileKindDropDown(TileSpawnSettings tile)
        {
            if (tile.IsTileShouldHaveKind())
            {
                var kindIndex = -1;
                for (int i = 0; i < _allowedTileKinds.Length; i++)
                {
                    if (tile.Kind == _allowedTileKinds[i])
                    {
                        kindIndex = i;
                        break;
                    }
                }

                if (kindIndex < 0)
                {
                    tile.Kind = _allowedTileKinds[0];
                    kindIndex = 0;
                }

                TileKindDropDown.interactable = true;
                TileKindDropDown.value = kindIndex;
            }
            else
            {
                TileKindDropDown.interactable = false;
                TileKindDropDown.value = 0;
            }
        }

        private void RefreshHpView(TileSpawnSettings tile)
        {
            var isShouldHaveHp = tile.IsTileShouldHaveHp();

            if (isShouldHaveHp)
            {
                var hp = GetHpValue(tile);
                if (hp <= 0)
                {
                    hp = 1;
                    SetHpValue(tile, 1);
                }

                HpText.text = hp.ToString();
                HpSlider.minValue = 1;
                HpSlider.maxValue = 3;
                HpSlider.wholeNumbers = true;
                HpSlider.value = hp;
                HpSlider.interactable = true;
            }
            else
            {
                HpText.text = "";
                HpSlider.minValue = 0;
                HpSlider.maxValue = 0;
                HpSlider.wholeNumbers = true;
                HpSlider.value = 0;
                HpSlider.interactable = false;
            }
        }

        /// <summary>
        /// Get hp parameter name for tile.
        /// </summary>
        /// <remarks>
        /// Different tile types have different hp parameter type.
        /// </remarks>
        private int GetHpValue(TileSpawnSettings tile)
        {
            if ((tile.Mods & (long)TileState.VaseMod) != 0)
            {
                return tile.GetParamValue(TileParamEnum.VaseLayerCount);
            }
            else if (tile.Asset == TileAsset.Egg)
            {
                return tile.GetParamValue(TileParamEnum.EggLayerCount);
            }
            else if (tile.Asset == TileAsset.FlowerPot)
            {
                return tile.GetParamValue(TileParamEnum.FlowerPotLayerCount);
            }
            else
            {
                return tile.GetParamValue(TileParamEnum.AdjacentHp);
            }
        }

        private void SetHpValue(TileSpawnSettings tile, int value)
        {
            if ((tile.Mods & (long)TileState.VaseMod) != 0)
            {
                tile.SetParamValue(TileParamEnum.VaseLayerCount, value);
            }
            else
            {
                if (tile.Asset == TileAsset.Egg)
                {
                    tile.SetParamValue(TileParamEnum.EggLayerCount, value);
                }
                else if (tile.Asset == TileAsset.FlowerPot)
                {
                    tile.SetParamValue(TileParamEnum.FlowerPotLayerCount, value);
                }
                else
                {
                    tile.SetParamValue(TileParamEnum.AdjacentHp, value);
                }
            }
        }

        private void RefreshWeightView(TileSpawnSettings tile)
        {
            WeightSlider.minValue = 0f;
            WeightSlider.maxValue = 100f;
            WeightSlider.value = Mathf.Clamp(tile.ProbabilityWeight, 0f, 100f);
            WeightSlider.wholeNumbers = true;
            WeightInputField.text = tile.ProbabilityWeight.ToString();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            OnTileDeleteClickedEvent = null;
        }
    }
}
#endif