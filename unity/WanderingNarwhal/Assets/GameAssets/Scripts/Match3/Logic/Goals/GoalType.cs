using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;

namespace GameAssets.Scripts.Match3.Logic
{
    public static class GoalTypeExtensions
    {
        private static GoalType[] _cachedGridBaseTypes;
        private static GoalType[] _cachedTileKindTypes;

        public static IEnumerable<GoalType> GetAllSingularGoals()
        {
            foreach (GoalType goalType in Enum.GetValues(typeof(GoalType)))
            {
                if (goalType != GoalType.GridBasedTypes && goalType != GoalType.StrictGridGoal &&
                    goalType != GoalType.TileKindTypes)
                {
                    yield return goalType;
                }
            }
        }

        public static float GetPriority(this GoalType goalType)
        {
            switch (goalType)
            {
                case GoalType.Score:
                    return 1f;
                case GoalType.Green:
                case GoalType.Yellow:
                case GoalType.Blue:
                case GoalType.Red:
                case GoalType.Purple:
                case GoalType.Orange:
                case GoalType.White:
                    return 2f;
                case GoalType.Litters:
                    return 3f;
                case GoalType.Stickers:
                case GoalType.ColorCrate:
                    return 4f;
                case GoalType.IceCubes:
                case GoalType.Chains:    
                    return 5f;
                case GoalType.Backgrounds:
                case GoalType.Ivy:
                case GoalType.Petal:
                case GoalType.DestructibleWall:    
                case GoalType.Tnt:    
                    return 6f;
                case GoalType.DropItems:
                    return 7f;
                case GoalType.Animal:
                    return 8f;
                case GoalType.Pinata:
                    return 9f;
                case GoalType.Sand:
                    return 10f;
                case GoalType.Watermelon:
                    return 12f;
                case GoalType.Vase:
                    return 13f;
                case GoalType.MoneyBag:
                    return 14f;
                case GoalType.Penguin:
                    return 15f;
                case GoalType.Egg:
                    return 16f;
                case GoalType.Bird:
                    return 17f;
                case GoalType.Banana:
                    return 18f;
                case GoalType.Sheep:
                    return 19f;
                case GoalType.Skunk:
                    return 20f;
                case GoalType.Chicken:
                    return 21f;
                case GoalType.Bee:
                    return 22f;
                case GoalType.Mole:
                    return 23f;
                case GoalType.Squid:
                    return 24f;
                case GoalType.BowlingPin:
                    return 25f;
                case GoalType.Bush:
                    return 26f;
                case GoalType.SodaBottle:
                    return 27f;
                case GoalType.MagicHat:
                    return 28f;
                case GoalType.Safe:
                    return 29f;
                case GoalType.FlowerPot:
                    return 30f;
                case GoalType.IceBar:
                    return 31f;
                case GoalType.DynamiteStick:
                    return 32f;
                case GoalType.GiantPinata:
                    return 33f;
                case GoalType.MetalBar:
                    return 34f;
                case GoalType.Shelf:
                    return 35f;
                case GoalType.JellyFish:
                    return 36f;
                case GoalType.GoldenScarab:
                    return 37f;
                case GoalType.Gondola:
                    return 38f;
                case GoalType.TukTuk:
                    return 39f;
                case GoalType.FireWorks:
                    return 40f;
                case GoalType.SlotMachine:
                    return 41f;
                default:
                    return 0f;
            }
        }

        public static bool IsAssistRedefined(this GoalType goal)
        {
            return (goal & GoalType.AssistRedefinedTypes) != 0;
        }

        public static bool IsAnyOf(this GoalType goal, GoalType other)
        {
            return goal != GoalType.None && (goal & other) != 0;
        }

        public static bool IsGoalTileHaveSkins(this GoalType goal)
        {
            return goal == GoalType.Chicken || goal == GoalType.Bee || goal == GoalType.Mole || goal == GoalType.Squid
                   || goal == GoalType.BowlingPin || goal == GoalType.SodaBottle || goal == GoalType.DynamiteStick ||
                   goal == GoalType.Shelf || goal == GoalType.JellyFish;
        }

        public static string ToShortString(this GoalType goalType)
        {
            if (goalType.IsTileKind())
                return goalType.ToNameString().Substring(0, 2);

            var str = goalType.ToNameString();

            var result = string.Empty;
            foreach(var ch in str)
                if (char.IsUpper(ch))
                    result += ch;

            return result;
        }

        public static string ToNameString(this GoalType goalType)
        {
            return goalType.ToString();
        }

        /// <summary>
        /// Check if goal has associated tile, which can possibly be spawned from cell spawners.
        /// </summary>
        /// <remarks>
        /// Non-spawnable goals can refresh remaining counter simply by checking all cells on grid,
        /// while spawnable goals can not, and they need more complicated logic, which will also check all spawners if they have required tile to spawn.
        /// </remarks>
        public static bool IsSpawnable(this GoalType goal)
        {
            return (goal & (GoalType.TileKindTypes | GoalType.DropItems | GoalType.Litters | GoalType.Pinata | GoalType.Watermelon | GoalType.Vase | GoalType.MoneyBag | GoalType.Penguin)) != 0;
        }

        /// <summary>
        /// Is goal can be replenished at runtime (total count increase during gameplay).
        /// </summary>
        /// <remarks>
        /// Currently only Sand goal have counter, which may increase total remaining items (as sand can grow on grid).
        /// Also sand's total counter is same as remaining counter.
        /// </remarks>
        public static bool CanReplenish(this GoalType goal)
        {
            return goal is GoalType.Sand or GoalType.Backgrounds or GoalType.Petal;
        }

        public static GoalType[] GridBasedTypes()
        {
            if (_cachedGridBaseTypes == null)
            {
                int count = 0;
                var types = GetAllSingularGoals();
                foreach (GoalType type in types)
                {
                    if ((type & GoalType.GridBasedTypes) != 0)
                    {
                        count++;
                    }
                }

                _cachedGridBaseTypes = new GoalType[count];
                int index = 0;
                foreach (GoalType type in types)
                {
                    if ((type & GoalType.GridBasedTypes) != 0)
                    {
                        _cachedGridBaseTypes[index] = type;
                        index++;
                    }
                }
            }

            return _cachedGridBaseTypes;
        }

        public static GoalType[] TileKindTypes()
        {
            if (_cachedTileKindTypes == null)
            {
                int count = 0;
                var types =  GetAllSingularGoals();
                foreach (GoalType type in types)
                {
                    if ((type & GoalType.TileKindTypes) != 0)
                    {
                        count++;
                    }
                }

                _cachedTileKindTypes = new GoalType[count];
                int index = 0;
                foreach (GoalType type in types)
                {
                    if ((type & GoalType.TileKindTypes) != 0)
                    {
                        _cachedTileKindTypes[index] = type;
                        index++;
                    }
                }
            }

            return _cachedTileKindTypes;
        }

        /// <summary>
        /// Is goal visual state should not have current and total counters,
        /// instead only one counter should be displayed.
        /// </summary>
        /// <remarks>
        /// For sand we display only one counter (not two, like for all others),
        /// because sand can grow on grid and therefore change max remaining count.
        /// </remarks>
        public static bool IsDisplayOnlyRemainingCount(this GoalType goalType)
        {
            return goalType == GoalType.Sand;
        }

        public static bool IsGridBased(this GoalType goalType)
        {
            return (goalType & GoalType.GridBasedTypes) != 0;
        }

        public static bool IsTileKind(this GoalType goalType)
        {
            return (goalType & GoalType.TileKindTypes) != 0;
        }

        public static TileKinds ToTileKind(this GoalType goalType)
        {
            switch (goalType)
            {
                case GoalType.Green:
                    return TileKinds.Green;
                case GoalType.Yellow:
                    return TileKinds.Yellow;
                case GoalType.Blue:
                    return TileKinds.Blue;
                case GoalType.Red:
                    return TileKinds.Red;
                case GoalType.Purple:
                    return TileKinds.Purple;
                case GoalType.Orange:
                    return TileKinds.Orange;
                case GoalType.White:
                    return TileKinds.White;
                default:
                    throw new Exception($"goalType goal type has no conversion to tile kind");
            }
        }

        public static void DebugValidateGoalIsSingular(this GoalType goal)
        {
            DebugValidateGoalIsSingular((long)goal);
        }

        public static void DebugValidateGoalIsSingular(this long goalNum)
        {
            // GoalType is serialized in scriptable object as long typed value (this was required
            // because enum serialization doesn't support long base type in Unity).
            // In combination with the fact that the goal enum is BitFlag means
            // that there is possible new kind of human-mistake in serialized settings.
            // This method helps detect possible mistake by counting how many bits flipped inside integer. -VK
            int count = 0;
            for (int i = 0; i < 64; i++)
            {
                var num = 1L << i;
                if ((goalNum & num) != 0)
                {
                    count++;
                    if (count > 1)
                    {
                        BDebug.LogError(LogCat.Match3,"Goal is not singular");
                        break;
                    }
                }
            }
        }
    }

    [Flags]
    public enum GoalType : long
    {
        None           = 0,
        Score          = 1L << 0, // 1,

        //grid based

        /// <summary>
        /// Grass.
        /// </summary>
        /// <remarks>
        /// Corresponding to cells with CellState BackOne or BackDouble.
        /// And also cell Background property, which is hp value.
        /// </remarks>
        Backgrounds    = 1L << 1, // 2,
        DropItems      = 1L << 2, // 4,
        Stickers       = 1L << 3, // 8,
        Litters        = 1L << 4, // 16,
        IceCubes       = 1L << 5, // 32,
        Pinata         = 1L << 6, // 64,
        Animal         = 1L << 7, // 128,
        Sand           = 1L << 8, // 256,

        /// <summary>
        /// Overlay grass, which hides tiles under itself.
        /// </summary>
        /// <remarks>
        /// Uses cell Background property as hp value.
        /// </remarks>
        Ivy            = 1L << 9, // 512,
        //tile kind
        Green          = 1L << 10, //1024,
        Yellow         = 1L << 11, //2048,
        Blue           = 1L << 12, //4096,
        Red            = 1L << 13, //8192,
        Purple         = 1L << 14, //16384,
        Orange         = 1L << 15, //32768,
        White          = 1L << 16, //65536,
        ColorCrate     = 1L << 17, //131072,
        Watermelon     = 1L << 18, //262144,
        Vase           = 1L << 19, //524288,
        MoneyBag       = 1L << 20, //1048576,
        Penguin        = 1L << 21, //2097152,
        Egg            = 1L << 22, //4194304,
        Bird           = 1L << 23, //8388608,
        Banana         = 1L << 24, //16777216
        Sheep          = 1L << 25, //33554432
        Skunk          = 1L << 26,
        GameEventScore = 1L << 27,
        Chicken        = 1L << 28,
        Bee            = 1L << 29,
        Mole           = 1L << 30,
        Squid          = 1L << 31,
        Toad           = 1L << 32,
        StealingHatScore  = 1L << 33,
        BowlingPin     = 1L << 34,
        Bush           = 1L << 35, //34359738368
        SodaBottle     = 1L << 36, //68719476736
        MagicHat       = 1L << 37, //137438953472
        Safe           = 1L << 38, //************
        FlowerPot      = 1L << 39, //************
        Petal          = 1L << 40, //1099511627776
        Chains         = 1L << 41, //2199023255552
        Tnt            = 1L << 42, //4398046511104
        IceBar         = 1L << 43, //8796093022208
        DynamiteStick  = 1L << 44, //17592186044416
        GiantPinata    = 1L << 45, //35184372088832
        MetalBar       = 1L << 46, //70368744177664
        Shelf          = 1L << 47, //140737488355328
        JellyFish      = 1L << 48, //281474976710656
        DestructibleWall  = 1L << 49, //562949953421312
        GoldenScarab   = 1L << 50, //1125899906842624
        Gondola        = 1L << 51, //2251799813685248
        TukTuk         = 1L << 52, //4503599627370496
        FireWorks      = 1L << 53, //9007199254740992
        SlotMachine    = 1L << 54, //18014398509481984

        AssistRedefinedTypes = Animal | Banana | Backgrounds | Bird | BowlingPin | Bush | Chains | Chicken | ColorCrate |
                               DynamiteStick | Egg | FireWorks | FlowerPot | GiantPinata | GoldenScarab | IceBar |
                               IceCubes | JellyFish | Litters | MetalBar | MoneyBag | Pinata | Petal | Safe | Shelf |
                               Sheep | SlotMachine | SodaBottle | Squid | Stickers | Vase | Watermelon,
        TileKindTypes = Green | Yellow | Blue | Red | Purple | Orange | White,
        GridBasedTypes = ~(TileKindTypes | Score),
        StrictGridGoal = Stickers | Litters | Backgrounds | DropItems | IceCubes | Pinata | ColorCrate | Watermelon | 
                         Squid | Mole | BowlingPin | Bush | SodaBottle | Safe | Chains | IceBar | DynamiteStick | GiantPinata |
                         MetalBar | Shelf | JellyFish | GoldenScarab | FireWorks | SlotMachine,
        AdjacentGoalTypes = Stickers | Litters | IceCubes | Animal | Sand | Watermelon | Vase | MoneyBag | Penguin | Egg | Bird |
                            Banana | Sheep | Skunk | Chicken | Bee | Mole | Squid | Toad | BowlingPin | Bush | SodaBottle |
                            MagicHat | FlowerPot | Ivy | Shelf | JellyFish | GoldenScarab | Gondola | FireWorks | SlotMachine,
        OnCellGoalType = Backgrounds | Petal,
    }

    /// <summary>
    /// Attribute that should be applied to serialized GoalType fields of type 'long'.
    /// This attribute will make goal name to appear in Unity Inspector window for this field.
    /// </summary>
    /// <remarks>
    /// Goal type enum can't be serialized directly, because it is a long-base-type enum that is not supported by Unity editor.
    /// Because of this limitation all serialized GoalType fields must be declared as 'long' type fields,
    /// and then converted to enum in runtime.
    ///
    /// This attribute helps visualize the actual goal type name in unity editor for integer fields.
    /// </remarks>
    [AttributeUsage(AttributeTargets.Field)]
    public class GoalTypeNameAttribute : UnityEngine.PropertyAttribute
    {
    }

#if UNITY_EDITOR

    [UnityEditor.CustomPropertyDrawer(typeof(GoalTypeNameAttribute))]
    public class GoalTypeNameDrawer : UnityEditor.PropertyDrawer
    {
        public override float GetPropertyHeight(UnityEditor.SerializedProperty property, UnityEngine.GUIContent label)
        {
            return UnityEditor.EditorGUI.GetPropertyHeight(property, label, includeChildren: true);
        }

        public override void OnGUI(UnityEngine.Rect position, UnityEditor.SerializedProperty property, UnityEngine.GUIContent label)
        {
            UnityEditor.EditorGUI.PropertyField(position, property, label);
            GoalType layerStateValue = (GoalType)property.longValue;
            const int width = 120;
            var labelPosition = new UnityEngine.Rect(new UnityEngine.Vector2(position.xMax - width - 10f, position.yMin), new UnityEngine.Vector2(width, 20f));
            UnityEditor.EditorGUI.LabelField(labelPosition, layerStateValue.ToString(), UnityEngine.GUI.skin.GetStyle("miniLabel"));
        }
    }
#endif
}
