using System;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB
{
    public static class TileMapper
    {
        private const TileState InTransitionNotMatchable = TileState.InTransition | TileState.NotMatchable;
        public static bool IsSimple(this Tile tile)
        {
            if (ReferenceEquals(tile, null))
                return false;

            return tile.Speciality == TileSpeciality.None && tile.Kind.IsColored();
        }

        public static bool IsMatchable(this Tile tile)
        {
            if (ReferenceEquals(tile, null))
                return false;

            return tile.Kind.IsColored() && tile.IsNoneOf(InTransitionNotMatchable);
        }

        public static bool IsSpecialMatchable(this Tile tile)
        {
            if (ReferenceEquals(tile, null))
                return false;

            return !tile.IsAnyOf(InTransitionNotMatchable) || tile.Speciality == TileSpeciality.ColorBomb;
        }

        public static TileState ParamToModState(TileParamEnum tileParam)
        {
            switch (tileParam)
            {
                case TileParamEnum.ChainLayerCount: return TileState.Chained;
                case TileParamEnum.IceLayerCount: return TileState.IceCube;
                case TileParamEnum.VaseLayerCount: return TileState.Vase;
                case TileParamEnum.EggLayerCount: return TileState.Egg;
                case TileParamEnum.FlowerPotLayerCount: return TileState.FlowerPot;
                default:
                    return TileState.None;
            }
        }

        public static TileState UnwrapMods(TileState state, bool forSerialization = false)
        {
            var result = TileState.None;
            var fullModsState = TileStateExtensions.ModFullStates();
            foreach (var mod in TileStateExtensions.ModStates)
            {
                if (forSerialization)
                {
                    if (mod == TileState.MoneyBagMod)
                    {
                        // We must manually exclude money bag mod when this method is used for serialization,
                        // because money bag is serialized in tile speciality
                        // and some levels already contain tiles with moneybag state, which should not have been there.
                        // todo: remove this when all level will be resaved without moneybag tile state. -VK
                        continue;
                    }
                }

                if ((state & mod) != 0 && fullModsState.ContainsKey(mod))
                {
                    result |= fullModsState[mod];
                }
            }

            return result;
        }

        public static BoosterItem GetAssetDefaultBoosterApplicability(TileSpeciality spec)
        {
            switch (spec)
            {
                case TileSpeciality.DropItem:
                    return BoosterItem.None;
                case TileSpeciality.RowBreaker:
                case TileSpeciality.ColumnBreaker:
                case TileSpeciality.Bomb:
                case TileSpeciality.Propeller:
                    return BoosterItem.All & ~BoosterItem.CreateBomb;
                case TileSpeciality.ColorBomb:
                    return BoosterItem.Wind;
                case TileSpeciality.Litter:
                case TileSpeciality.Sticker:
                case TileSpeciality.ColorCrate:
                case TileSpeciality.Frame:
                case TileSpeciality.Pinata:
                case TileSpeciality.Watermelon:
                    return BoosterItem.Shovel | BoosterItem.Balloon | BoosterItem.Wind | BoosterItem.Rain |
                           BoosterItem.Vertical | BoosterItem.Horizontal;
            }

            return BoosterItem.All;
        }

        public static DamageSource GetTileStateExcludedDamageSOurce(TileState tilState)
        {
            if ((tilState & TileState.ChainMod) != 0)
            {
                return DamageSource.AdjacentGeneral;
            }
            
            return DamageSource.None;
        }

        public static DamageSource GetTileStateAllowedDamageSource(TileState tileState)
        {
            var result = DamageSource.None;
            if ((tileState & TileState.SandMod) != 0)
            {
                return DamageSource.AdjacentGeneral | DamageSource.PowerUp | DamageSource.UsableBoost | DamageSource.SuperBoost;
            }

            if ((tileState & TileState.IceCubeMod) != 0 || (tileState & TileState.VaseMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp | DamageSource.UsableBoost |
                          DamageSource.SuperBoost | DamageSource.Whirlpool | DamageSource.Dynamite |
                          DamageSource.Skunk | DamageSource.FireWorks;

            if ((tileState & TileState.ChainMod) != 0)
                result |= DamageSource.AllBase & ~DamageSource.AdjacentGeneral;

            if ((tileState & TileState.ColorCrateMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.SameTileKind | DamageSource.PowerUp;

            if ((tileState & TileState.MoneyBagMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.PenguinMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.EggMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.FlowerPotMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.BirdMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.SheepMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.BananaMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.MonkeyMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.SkunkMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.HenMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.ChickenMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.HiveMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.BeeMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.SquidMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.SameTileKind | DamageSource.PowerUp;

            if ((tileState & TileState.ToadMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.MagicHatMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.BowlingMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.SodaMod) != 0)
                result |= DamageSource.SameTileKind | DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.BushMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.IceBarMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.DynamiteBoxMod) != 0)
                result |= DamageSource.SameTileKind | DamageSource.AdjacentGeneral | DamageSource.PowerUp;

            if ((tileState & TileState.GiantPinataMod) != 0)
                result |= DamageSource.PowerUp | DamageSource.Whirlpool | DamageSource.Dynamite |
                          DamageSource.UsableBoost | DamageSource.SuperBoost | DamageSource.Skunk | DamageSource.FireWorks;

            if ((tileState & TileState.ShelfMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.JellyFishMod) != 0)
                result |= DamageSource.SameTileKind | DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.GoldenScarabMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.GondolaMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp | DamageSource.Gondola;
            
            if ((tileState & TileState.FireWorksMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.SlotMachineMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            if ((tileState & TileState.BigMonkeyMod) != 0)
                result |= DamageSource.AdjacentGeneral | DamageSource.PowerUp;
            
            return result;
        }

        public static DamageSource GetTileSpecialityAllowedDamageSource(TileSpeciality spec)
        {
            switch (spec)
            {
                case TileSpeciality.BlinkingTile:
                    return DamageSource.Bomb | DamageSource.MultiBomb | DamageSource.LineBreakerArrow | DamageSource.Skunk | DamageSource.FireWorks;
                case TileSpeciality.RowBreaker:
                case TileSpeciality.ColumnBreaker:
                case TileSpeciality.Bomb:
                case TileSpeciality.Propeller:
                    return DamageSource.AllBase | DamageSource.TapOrSwap;
                case TileSpeciality.ColorBomb:
                    return DamageSource.Propeller | DamageSource.TapOrSwap;
                case TileSpeciality.DropItem:
                    return DamageSource.None;
                case TileSpeciality.Pinata:
                case TileSpeciality.Safe:
                case TileSpeciality.GiantPinata:
                case TileSpeciality.MetalBar:
                    return DamageSource.PowerUp | DamageSource.Whirlpool | DamageSource.Dynamite |
                           DamageSource.UsableBoost | DamageSource.SuperBoost | DamageSource.Skunk | DamageSource.FireWorks;
                case TileSpeciality.ColorCrate:
                    return DamageSource.AdjacentGeneral | DamageSource.SameTileKind | DamageSource.PowerUp
                           | DamageSource.Whirlpool | DamageSource.Dynamite | DamageSource.UsableBoost |
                           DamageSource.SuperBoost | DamageSource.Skunk | DamageSource.FireWorks;
                case TileSpeciality.Litter:
                case TileSpeciality.Sticker:
                case TileSpeciality.Watermelon:
                case TileSpeciality.Frame:
                case TileSpeciality.Mole:
                case TileSpeciality.Toad:
                case TileSpeciality.MagicHat:
                case TileSpeciality.Bowling:
                case TileSpeciality.Bush:
                case TileSpeciality.Soda:
                case TileSpeciality.IceBar:
                case TileSpeciality.DynamiteBox:
                case TileSpeciality.Shelf:
                case TileSpeciality.JellyFish: 
                case TileSpeciality.GoldenScarab:    
                case TileSpeciality.Gondola:
                case TileSpeciality.FireWorks:
                case TileSpeciality.SlotMachine:
                    return DamageSource.AllBase | DamageSource.AdjacentGeneral;
                case TileSpeciality.Squid:
                {
                    return DamageSource.AdjacentGeneral | DamageSource.SameTileKind | DamageSource.Skunk |
                           DamageSource.FireWorks | DamageSource.PowerUp | DamageSource.Whirlpool |
                           DamageSource.Dynamite | DamageSource.UsableBoost | DamageSource.SuperBoost;
                    
                }
            }

            return DamageSource.AllBase;
        }

        public static TileSpeciality GetAssetDefaultSpeciality(TileAsset tileAsset)
        {
            switch (tileAsset)
            {
                case TileAsset.ColorBomb:     return TileSpeciality.ColorBomb;
                case TileAsset.Sticker:       return TileSpeciality.Sticker;
                case TileAsset.Frame:         return TileSpeciality.Frame;
                case TileAsset.DropItem:      return TileSpeciality.DropItem;
                case TileAsset.Litter:        return TileSpeciality.Litter;
                case TileAsset.Pinata:        return TileSpeciality.Pinata;
                case TileAsset.ColumnBreaker: return TileSpeciality.ColumnBreaker;
                case TileAsset.RowBreaker:    return TileSpeciality.RowBreaker;
                case TileAsset.Bomb:          return TileSpeciality.Bomb;
                case TileAsset.BlinkingTile:  return TileSpeciality.BlinkingTile;
                case TileAsset.Sand:          return TileSpeciality.Sand;
                case TileAsset.ColorCrate:    return TileSpeciality.ColorCrate;
                case TileAsset.Watermelon:    return TileSpeciality.Watermelon;
                case TileAsset.MoneyBag:      return TileSpeciality.MoneyBag;
                case TileAsset.Penguin:       return TileSpeciality.Penguin;
                case TileAsset.Egg:           return TileSpeciality.Egg;
                case TileAsset.Bird:          return TileSpeciality.Bird;
                case TileAsset.Sheep:         return TileSpeciality.Sheep;
                case TileAsset.Banana:        return TileSpeciality.Banana;
                case TileAsset.Monkey:        return TileSpeciality.Monkey;
                case TileAsset.Skunk:         return TileSpeciality.Skunk;
                case TileAsset.Hen:           return TileSpeciality.Hen;
                case TileAsset.Chicken:       return TileSpeciality.Chicken;
                case TileAsset.Hive:          return TileSpeciality.Hive;
                case TileAsset.Bee:           return TileSpeciality.Bee;
                case TileAsset.Mole:          return TileSpeciality.Mole;
                case TileAsset.Squid:         return TileSpeciality.Squid;
                case TileAsset.Toad:          return TileSpeciality.Toad;
                case TileAsset.Propeller:     return TileSpeciality.Propeller;
                case TileAsset.Bowling:       return TileSpeciality.Bowling;
                case TileAsset.Bush:          return TileSpeciality.Bush;
                case TileAsset.Soda:          return TileSpeciality.Soda;
                case TileAsset.MagicHat:      return TileSpeciality.MagicHat;
                case TileAsset.Safe:          return TileSpeciality.Safe;
                case TileAsset.FlowerPot:     return TileSpeciality.FlowerPot;
                case TileAsset.IceBar:        return TileSpeciality.IceBar;
                case TileAsset.DynamiteBox:   return TileSpeciality.DynamiteBox;
                case TileAsset.GiantPinata:   return TileSpeciality.GiantPinata;
                case TileAsset.MetalBar:      return TileSpeciality.MetalBar;
                case TileAsset.Shelf:         return TileSpeciality.Shelf;
                case TileAsset.JellyFish:     return TileSpeciality.JellyFish;
                case TileAsset.GoldenScarab:  return TileSpeciality.GoldenScarab;
                case TileAsset.Gondola:       return TileSpeciality.Gondola;
                case TileAsset.TukTuk:        return TileSpeciality.TukTuk;
                case TileAsset.FireWorks:     return TileSpeciality.FireWorks;
                case TileAsset.SlotMachine:   return TileSpeciality.SlotMachine;
                case TileAsset.BigMonkey:     return TileSpeciality.BigMonkey;
            }

            return TileSpeciality.None;
        }

        public static TileState GetSpecialityDefaultState(TileSpeciality spec)
        {
            switch (spec)
            {
                case TileSpeciality.None:
                    return TileState.None;
                case TileSpeciality.BlinkingTile:
                    return TileState.Blinking;
                case TileSpeciality.RowBreaker:
                case TileSpeciality.ColumnBreaker:
                    return TileState.LineBreaker;
                case TileSpeciality.Bomb:
                    return TileState.Bomb;
                case TileSpeciality.ColorBomb:
                    return TileState.ColorBomb;
                case TileSpeciality.DropItem:
                    return TileState.DropItem;
                case TileSpeciality.Litter:
                    return TileState.Litter;
                case TileSpeciality.Sand:
                    return TileState.Sand;
                case TileSpeciality.Sticker:
                    return TileState.Sticker;
                case TileSpeciality.Frame:
                    return TileState.Frame;
                case TileSpeciality.Pinata:
                    return TileState.Pinata;
                case TileSpeciality.ColorCrate:
                    return TileState.ColorCrate;
                case TileSpeciality.Watermelon:
                    return TileState.Watermelon;
                case TileSpeciality.MoneyBag:
                    return TileState.MoneyBag;
                case TileSpeciality.Penguin:
                    return TileState.Penguin;
                case TileSpeciality.Egg:
                    return TileState.Egg;
                case TileSpeciality.Bird:
                    return TileState.Bird;
                case TileSpeciality.Sheep:
                    return TileState.Sheep;
                case TileSpeciality.Banana:
                    return TileState.Banana;
                case TileSpeciality.Monkey:
                    return TileState.Monkey;
                case TileSpeciality.Skunk:
                    return TileState.Skunk;
                case TileSpeciality.Hen:
                    return TileState.Hen;
                case TileSpeciality.Chicken:
                    return TileState.Chicken;
                case TileSpeciality.Hive:
                    return TileState.Hive;
                case TileSpeciality.Bee:
                    return TileState.Bee;
                case TileSpeciality.Mole:
                    return TileState.Mole;
                case TileSpeciality.Squid:
                    return TileState.Squid;
                case TileSpeciality.Toad:
                    return TileState.Toad;
                case TileSpeciality.MagicHat:
                    return TileState.MagicHat;
                case TileSpeciality.Propeller:
                    return TileState.Propeller;
                case TileSpeciality.Bowling:
                    return TileState.Bowling;
                case TileSpeciality.Bush:
                    return TileState.Bush;
                case TileSpeciality.Soda:
                    return TileState.Soda;
                case TileSpeciality.Safe:
                    return TileState.Safe;
                case TileSpeciality.FlowerPot:
                    return TileState.FlowerPot;
                case TileSpeciality.IceBar:
                    return TileState.IceBar;
                case TileSpeciality.DynamiteBox:
                    return TileState.DynamiteBox;
                case TileSpeciality.GiantPinata:
                    return TileState.GiantPinata;
                case TileSpeciality.MetalBar:
                    return TileState.MetalBar;
                case TileSpeciality.Shelf:
                    return TileState.Shelf;
                case TileSpeciality.JellyFish:
                    return TileState.JellyFish;
                case TileSpeciality.GoldenScarab:
                    return TileState.GoldenScarab;
                case TileSpeciality.Gondola:
                    return TileState.Gondola;
                case TileSpeciality.TukTuk:
                    return TileState.TukTuk;
                case TileSpeciality.FireWorks:
                    return TileState.FireWorks;
                case TileSpeciality.SlotMachine:
                    return TileState.SlotMachine;
                case TileSpeciality.BigMonkey:
                    return TileState.BigMonkey;
                default:
                    throw new ArgumentOutOfRangeException("TileSpeciality", spec, null);
            }
        }
    }
}
