using System;
using System.Collections.Generic;
using BBB.Match3.Renderer;

namespace BBB.CellTypes
{
    [Flags]
    public enum CellState
    {
        None                 = 0,
        Spawner              = 1,
        Despawner            = 1 << 1,
        Wall                 = 1 << 4,
        BackOne              = 1 << 5,
        BackDouble           = 1 << 6,
        // States:
        AssistMarker         = 1 << 9,
        NotShuffable         = 1 << 11,

        /// <summary>
        /// Ivy state is mutually exclusive with BackOne and BackDouble.
        /// </summary>
        Ivy                  = 1 << 12,

        /// <summary>
        /// TNT Cell state.
        /// </summary>
        /// <remarks>
        /// TNT is multi-cell state.
        /// Cell with this state needs to have SizeX and SizeY parameters.
        /// </remarks>
        Tnt                  = 1 << 13,

        InvisibleWall        = 1 << 14,
        
        InvinvibleWhileBusy  = 1 << 15,
        
        Petal                = 1 << 16,
        
        IceBar               = 1 << 17,
        
        MetalBar             = 1 << 18,
        
        DestructibleWall     = 1 << 19,
        
        Water                = 1 << 20,
        
        FlagEnd              = 1 << 21,

        NotAcceptingTiles = 1 << 10,
        
        HammerTarget =  BackOne | BackDouble | Ivy | Petal | DestructibleWall,
    }

    public static class CellStateExtensions
    {
        public static IEnumerable<FxType> ToFxTypes(this CellState cellState)
        {
            if ((cellState & CellState.BackOne) != 0)
            {
                yield return FxType.BackgroundRemove;
            }
            
            if ((cellState & CellState.BackDouble) != 0)
            {
                yield return FxType.BackgroundRemove;
                yield return FxType.BackgroundDoubleRemove;
            }
            
            if ((cellState & CellState.Petal) != 0)
            {
                yield return FxType.PetalRemove;
            }

            if ((cellState & CellState.Ivy) != 0)
            {
                yield return FxType.IvyDestroy;
            }

            if ((cellState & CellState.Tnt) != 0)
            {
                yield return FxType.TntDestroy;
            }
        }
    }
}
