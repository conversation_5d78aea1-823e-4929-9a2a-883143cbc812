using System;
using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using UnityEngine;

namespace BBB.Match3.Systems
{
    /// <summary>
    /// Generation settings for spawners, which contains set of allowed tiles with probability weights.
    /// </summary>
    /// <remarks>
    /// Spawn setting is stored inside scriptable object asset, which is stored with levels in the bundle.
    /// Each spawner on scene referencing one spawner settings by uid, which then is used to generate new tiles on grid.
    ///
    /// List of all spawner settings is shared across all levels.
    /// </remarks>
    [Serializable]
    public class SpawnerSettings
    {
        /// <summary>
        /// Id, which is used for referencing from level spawners (same as array index).
        /// </summary>
        public int Uid;

        /// <summary>
        /// Display name for designers.
        /// </summary>
        public string Name;

        /// <summary>
        /// Spawn settings.
        /// </summary>
        public TileSpawnSettings[] TilesSettings = new TileSpawnSettings[0];

        public List<PreSpawnedTile> FixedTilesToPreSpawn;

        public static SpawnerSettings FindSpawnerByUid(SpawnerSettings[] settings, int uid)
        {
            if (settings == null || settings.Length == 0) return null;
            for (int i = 0; i < settings.Length; i++)
            {
                if (settings[i].Uid == uid)
                {
                    return settings[i];
                }
            }

            // In any case if spawner setting not found, then
            // fallback to default config, which will always be present at first index.
            return settings[0];
        }

        public bool IsSpawningAnyTileRelatedToGoal(GoalType goal)
        {
            for (int i = 0; i < TilesSettings.Length; i++)
            {
                if (TilesSettings[i].IsSpawnedTileRelatedToGoal(goal))
                {
                    return true;
                }
            }
            
            if(FixedTilesToPreSpawn != null && goal == GoalType.DropItems)
                foreach (var item in FixedTilesToPreSpawn)
                {
                    if (item.Speciality == TileSpeciality.DropItem)
                        return true;
                }

            return false;
        }

        public TileSpawnSettings FindTileSpawnSettingRelatedToGoal(GoalType goal)
        {
            for (int i = 0; i < TilesSettings.Length; i++)
            {
                if (TilesSettings[i].IsSpawnedTileRelatedToGoal(goal))
                {
                    return TilesSettings[i];
                }
            }

            return null;
        }
    }

    [Serializable]
    public class PreSpawnedTile
    {
        [JsonConverter(typeof(StringEnumConverter))]
        public TileSpeciality Speciality;
        public TileKinds TileKind;
        public int Count;
    }

    [Serializable]
    public class TileSpawnSettings
    {
        /// <summary>
        /// Tile asset determines base tile type.
        /// </summary>
        public TileAsset Asset = TileAsset.Simple;

        /// <summary>
        /// Mods state determines additional tile state,
        /// for example sand or ice. 
        /// </summary>
        public long Mods;

        /// <summary>
        /// Initial tile params.
        /// </summary>
        public TileParam[] Params;

        /// <summary>
        /// Initial tile color.
        /// </summary>
        public TileKinds Kind;

        /// <summary>
        /// Option to stop spawning item if goal for this item exists on level and it has been reached.
        /// </summary>
        /// <remarks>
        /// If goal for this item doesn't exist, then it will spawn without limit.
        /// </remarks>
        public bool SpawnUntilGoalReached;

        /// <summary>
        /// Allow spawn item from spawner only if there are less items of this kind than specified.
        /// </summary>
        /// <remarks>
        /// For example, on some levels drop item should start spawn only if there are less than 2 drop items on grid.
        /// </remarks>
        public int MaxAllowedItemsOnGrid;

        /// <summary>
        /// Weight probability value for tile to be spawned.
        /// </summary>
        public float ProbabilityWeight = 1f;

        public bool IsTileCorrespondingSpawnSetting(Tile tile)
        {
            if (tile.IsNull()) return false;
            if (Mods != (long)TileState.None && !tile.IsAnyOf((TileState)Mods)) return false;
            if (tile.Asset != Asset) return false;
            if (Kind != TileKinds.Undefined && IsTileShouldHaveKind())
            {
                return tile.Kind == Kind;
            }
            else
            {
                return true;
            }
        }

        /// <summary>
        /// Get goal type, corresponding to tile asset.
        /// </summary>
        /// <remarks>
        /// Convert tile asset to goal type, but only for those tile types,
        /// which have gravity, and therefore, can be spawned. -VK
        /// </remarks>
        public GoalType GetCorrespondingTileAssetGoalType()
        {
            switch (Asset)
            {
                case TileAsset.Simple:
                    switch (Kind)
                    {
                        case TileKinds.Green:
                            return GoalType.Green;
                        case TileKinds.Yellow:
                            return GoalType.Yellow;
                        case TileKinds.Blue:
                            return GoalType.Blue;
                        case TileKinds.Red:
                            return GoalType.Red;
                        case TileKinds.Purple:
                            return GoalType.Purple;
                        case TileKinds.Orange:
                            return GoalType.Orange;
                        case TileKinds.White:
                            return GoalType.White;
                    }

                    break;
                case TileAsset.DropItem:
                    return GoalType.DropItems;
                case TileAsset.Litter:
                    return GoalType.Litters;
                case TileAsset.Pinata:
                    return GoalType.Pinata;
                case TileAsset.Watermelon:
                    return GoalType.Watermelon;
                case TileAsset.MoneyBag:
                    return GoalType.MoneyBag;
                case TileAsset.Penguin:
                    return GoalType.Penguin;
            }

            return GoalType.None;
        }

        public GoalType GetCorrespondingModGoalType()
        {
            if (Mods != (long)TileState.None)
            {
                if ((Mods & (long)TileState.VaseMod) != 0)
                {
                    return GoalType.Vase;
                }
            }

            return GoalType.None;
        }

        public bool IsTileShouldHaveHp()
        {
            return Asset == TileAsset.Watermelon || (Mods & (long)TileState.Vase) != 0 || (Mods & (long)TileState.EggMod) !=0 || (Mods & (long)TileState.FlowerPotMod) != 0;
        }

        public bool IsTileShouldHaveKind()
        {
            return Asset == TileAsset.Simple
                || Asset == TileAsset.ColumnBreaker
                || Asset == TileAsset.RowBreaker
                || Asset == TileAsset.Bomb
                || Asset == TileAsset.ColorCrate;
        }

        public bool IsExistParam(TileParamEnum paramType)
        {
            if (Params == null) return false;
            for (int i = 0; i < Params.Length; i++)
            {
                if (Params[i].Param == paramType)
                {
                    return true;
                }
            }

            return false;
        }

        public int GetParamValue(TileParamEnum paramType)
        {
            if (Params == null) return 0;
            for (int i = 0; i < Params.Length; i++)
            {
                if (Params[i].Param == paramType)
                {
                    return Params[i].Value;
                }
            }

            return 0;
        }

        public void SetParamValue(TileParamEnum paramType, int value)
        {
            if (Params == null)
            {
                Params = new TileParam[1];
                Params[0] = new TileParam(paramType, value);
                return;
            }

            for (var i = 0; i < Params.Length; i++)
            {
                if (Params[i].Param != paramType) continue;
                
                Params[i].Value = value;
                return;
            }

            // Param not found, add new param
            var newParams = new TileParam[Params.Length + 1];
            for (var i = 0; i < Params.Length; i++)
            {
                newParams[i] = Params[i];
            }
            newParams[Params.Length] = new TileParam(paramType, value);
            Params = newParams;
        }


        public void RemoveParam(TileParamEnum paramType)
        {
            if (Params == null)
                return;

            var countToRemove = 0;

            for (var i = 0; i < Params.Length; i++)
            {
                if (Params[i].Param == paramType)
                {
                    countToRemove++;
                }
            }

            if (countToRemove == 0)
                return;

            var newParams = new TileParam[Params.Length - countToRemove];
            var index = 0;

            for (var i = 0; i < Params.Length; i++)
            {
                if (Params[i].Param != paramType)
                {
                    newParams[index++] = Params[i];
                }
            }

            Params = newParams;
        }

        public bool IsSpawnedTileRelatedToGoal(GoalType goal)
        {
            var mod = (TileState)Mods;
            switch (goal)
            {
                case GoalType.DropItems:
                    return Asset == TileAsset.DropItem;
                case GoalType.Stickers:
                    return Asset == TileAsset.Sticker;
                case GoalType.Litters:
                    return Asset == TileAsset.Litter;
                case GoalType.IceCubes:
                    return (mod & TileState.IceCubeMod) != 0;
                case GoalType.Chains:
                    return (mod & TileState.ChainMod) != 0;
                case GoalType.Pinata:
                    return Asset == TileAsset.Pinata;
                case GoalType.Animal:
                    return (mod & TileState.AnimalMod) != 0;
                case GoalType.Sand:
                    return (mod & TileState.SandMod) != 0;
                case GoalType.Green:
                    return IsTileShouldHaveKind() && Kind == TileKinds.Green;
                case GoalType.Yellow:
                    return IsTileShouldHaveKind() && Kind == TileKinds.Yellow;
                case GoalType.Blue:
                    return IsTileShouldHaveKind() && Kind == TileKinds.Blue;
                case GoalType.Red:
                    return IsTileShouldHaveKind() && Kind == TileKinds.Red;
                case GoalType.Purple:
                    return IsTileShouldHaveKind() && Kind == TileKinds.Purple;
                case GoalType.Orange:
                    return IsTileShouldHaveKind() && Kind == TileKinds.Orange;
                case GoalType.White:
                    return IsTileShouldHaveKind() && Kind == TileKinds.White;
                case GoalType.ColorCrate:
                    return Asset == TileAsset.ColorCrate;
                case GoalType.Watermelon:
                    return Asset == TileAsset.Watermelon;
                case GoalType.Vase:
                    return (mod & TileState.VaseMod) != 0;
                case GoalType.MoneyBag:
                    return Asset == TileAsset.MoneyBag;
                case GoalType.Penguin:
                    return Asset == TileAsset.Penguin;
                case GoalType.Egg:
                    return (mod & TileState.EggMod) != 0;
                case GoalType.Bird:
                    return (mod & TileState.BirdMod) != 0;
                case GoalType.Banana:
                    return (mod & TileState.BananaMod) != 0;
                case GoalType.Chicken:
                    return (mod & TileState.ChickenMod) != 0;
                case GoalType.Bee:
                    return (mod & TileState.BeeMod) != 0;
                case GoalType.Mole:
                    return (mod & TileState.MoleMod) != 0;
                case GoalType.FlowerPot:
                    return (mod & TileState.FlowerPotMod) != 0;
            }
            return false;
        }
    }

    [CreateAssetMenu(menuName = "BBB/M3/SpawnerSettingsAsset", fileName = "SpawnerSettingsAsset", order = 1)]
    public class SpawnerSettingsContainer : ScriptableObject
    {
        public List<SpawnerSettings> Spawners = new List<SpawnerSettings>();

        public void OnEnable()
        {
            if (Spawners != null)
            {
                foreach (var spawner in Spawners)
                {
                    if (spawner.FixedTilesToPreSpawn == null)
                    {
                        spawner.FixedTilesToPreSpawn = new List<PreSpawnedTile>();
                    }
                }
            }
        }

        public SpawnerSettings FindItemByUid(int uid)
        {
            for (int i = 0; i < Spawners.Count; i++)
            {
                if (Spawners[i].Uid == uid)
                {
                    return Spawners[i];
                }
            }

            return null;
        }
    }
}