using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Core;
using BBB.Match3;
using BBB.Match3.Debug;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB
{
    public sealed class Cell : IGridActor
    {
        public Coords Coords; // To be considered as Cell's Id
        public Tile Tile { get; private set; }
        public int HitSourceUid { get; private set; } = -1;

        /// <summary>
        /// Hp value of current background state.
        /// </summary>
        /// <remarks>
        /// Background with hp can be: BackOne, BackDouble, Ivy.
        /// This is determined by current State value of the cell. -VK
        /// </remarks>
        public int BackgroundCount { get; set; }
        
        public int IvyCount { get; set; }

        public int TntCount { get; set; }
        
        public bool IceBarStatus { get; set; }
        
        public bool MetalBarStatus { get; set; }

        public TileKinds TntKind { get; set; }

        public TntTargetType TntTarget { get; set; }

        // Speciality modifiers:
        public CellWalls Walls { get; set; }
        public CellWalls InvisibleWalls { get; set; }
        public DestructibleWalls DestructibleWalls { get; set; }

        /// <summary>
        /// Multi-size parameter.
        /// Used only if this cell contains multi-size CelLState.
        /// </summary>
        /// <remarks>
        /// Multi-size cell occupies area to the top and to the right from origin point (by increasing x and y coords).
        /// </remarks>
        public int SizeX { get; set; }

        /// <summary>
        /// Multi-size parameter.
        /// Used only if this cell contains multi-size CelLState.
        /// </summary>
        public int SizeY { get; set; }

        public int IsBusy { get; set; } // Don't change manually!!!

        public CellState State { get; private set; }

        public CellDir CellDir { get; private set; }

        /// <summary>
        /// Runtime references to cells that contains multi-size tiles overlaping with this cell.
        /// Allows to quickly determine if this cell is currently under some multi-size tile.
        /// </summary>
        /// <remarks>
        /// Can contain reference to iteslf.
        /// </remarks>
        private List<Cell> _cachedMultiSizeCellsReferences;

        public List<Cell> CachedMultiSizeCellsReferences => _cachedMultiSizeCellsReferences;

        /// <summary>
        /// Flag indicating that multi-size cell reference contains multi-size cell overlay or otherwise it's just a tile.
        /// </summary>
        /// <remarks>
        /// Multi-size tiles (Squid) and multi-size overlays (Tnt) work differently in m3 simulation.
        /// </remarks>
        private bool _isReferencedMultiSizeCellOverlayType;

        /// <summary>
        /// Unlike _cachedMultiSizeCellsReferences, which holds the reference to the main cell, this list is held by
        /// the main cell and contains the cells that belong to the multi-sized blocker (including the main cell)
        /// </summary>
        private readonly HashSet<Cell> _referencedCells = new HashSet<Cell>();
        public HashSet<Cell> ReferencedCells => _referencedCells;

        public bool IsDirectlyInteractable => !Tile.HasAnyArmor() && IvyCount <= 0;
        
        private static readonly (long, int) GrassSingleLayerAssistState = ((long) GoalType.Backgrounds, 1);
        private static readonly (long, int) GrassDoubleLayerAssistState = ((long) GoalType.Backgrounds, 2);
        private static readonly (long, int) PetalSingleLayerAssistState = ((long) GoalType.Petal, 1);
        private (long, int value) _destructibleWallLayerAssistState = ((long) GoalType.Backgrounds, 1);

        /// <summary>
        /// If True then this indicates that this cell is overlapped with another cell that contains
        /// multi-size Tile (for example, Squid)  or Overlay (for example, TNT);
        /// </summary>
        /// <remarks>
        /// Every cell 'under' multi-size entity must obey specific limitation:
        /// no swaps are allowed, no gravity is allowed,
        /// no matches are allowed, all damage that hits this tile must be transfered to main cell.
        /// Multi-size entity itself exists in bottom-left cell of occupied square of cells. -VK
        /// </remarks>
        public bool HasMultiSizeCellReference()
        {
            return _cachedMultiSizeCellsReferences != null && _cachedMultiSizeCellsReferences.Count > 0;
        }

        /// <summary>
        /// Same as HasMultiSizeCellReference, but check only for multi-size cell overlays (ignore multi-size tiles).
        /// </summary>
        public bool HasMultiSizeCellReferenceWithCellOverlay()
        {
            return _isReferencedMultiSizeCellOverlayType;
        }

        /// <summary>
        /// Same as HasMultiSizeCellReference, but check only for multi-size tiles (ignore cell overlays).
        /// </summary>
        public bool HasMultiSizeCellReferenceWithMultiSizeTile()
        {
            if (_isReferencedMultiSizeCellOverlayType)
            {
                if (_cachedMultiSizeCellsReferences == null) return false;
                foreach (var c in _cachedMultiSizeCellsReferences)
                {
                    if (!c.IsAnyOf(CellState.Tnt))
                    {
                        return true;
                    }
                }

                return false;
            }
            else
            {
                return _cachedMultiSizeCellsReferences != null && _cachedMultiSizeCellsReferences.Count > 0;
            }
        }


        public void ClearMultiSizeCellsReferences()
        {
            if (_cachedMultiSizeCellsReferences != null)
            {
                _cachedMultiSizeCellsReferences.Clear();
            }

            _isReferencedMultiSizeCellOverlayType = false;
        }


        /// <summary>
        /// Returns self
        /// or reference to Multi-Size main cell, which is overlapping this cell.
        /// </summary>
        /// <remarks>
        /// Multi-size cell is cell with defined sizeX and sizeY (or with tile that contains such params),
        /// and it is located in bottom left corner of rectangle.
        /// Every cell in this rectangle contains reference to main cell. -VK
        /// </remarks>
        /// <param name="coordsOffset">The relative coords of this cell relative to main cell.</param>
        /// <param name="isCellOverlay">Is search only for multi-size CellOverlay, otherwise, search only for multi-size tiles.</param>
        public Cell GetMainCellReference(out Coords coordsOffset, bool isCellOverlay = false)
        {
            if (HasMultiSizeCellReference())
            {
                if (isCellOverlay)
                {
                    if (_isReferencedMultiSizeCellOverlayType)
                    {
                        foreach (var cellRef in _cachedMultiSizeCellsReferences)
                        {
                            if (cellRef.TntCount > 0)
                            {
                                coordsOffset = this.Coords - cellRef.Coords;
                                return cellRef;
                            }
                        }
                    }

                    coordsOffset = new Coords();
                    return this;
                }
                else
                {
                    if (_isReferencedMultiSizeCellOverlayType)
                    {
                        foreach (var cellRef in _cachedMultiSizeCellsReferences)
                        {
                            if (cellRef.TntCount <= 0)
                            {
                                coordsOffset = this.Coords - cellRef.Coords;
                                return cellRef;
                            }
                        }
                    }
                    else
                    {
                        var result = _cachedMultiSizeCellsReferences[0];
                        coordsOffset = this.Coords - result.Coords;
                        return result;
                    }
                }
            }

            coordsOffset = new Coords();
            return this;
        }

        public void AddMultisizeCellReference(Cell c)
        {
            if (_cachedMultiSizeCellsReferences == null)
            {
                _cachedMultiSizeCellsReferences = new List<Cell>(1);
            }

            if (!_cachedMultiSizeCellsReferences.Contains(c))
            {
                _cachedMultiSizeCellsReferences.Add(c);
            }

            _isReferencedMultiSizeCellOverlayType = false;
            foreach (var cellRef in _cachedMultiSizeCellsReferences)
            {
                if (cellRef.IsAnyOf(CellState.Tnt))
                {
                    _isReferencedMultiSizeCellOverlayType = true;
                    break;
                }
            }
        }

        public bool CanReceiveAdjacentDamageWhenCellIsEmpty
        {
            get { return IsAnyOf(CellState.Ivy) && !HasMultiSizeCellReferenceWithCellOverlay(); }
        }

        public bool CanReceiveDirectDamageWhenCellIsEmpty
        {
            get { return IsAnyOf(CellState.Ivy | CellState.BackOne | CellState.BackDouble | CellState.Petal | CellState.DestructibleWall); }
        }

        /// <summary>
        /// Reference to spawner settings.
        /// </summary>
        /// <remarks>
        /// Cell can reference special spawner setting by uid, which is used in case, if cell contains active Spawner.
        /// Default spawner at index 0 contains most common spawner settings and all cells referencing it if not manually overriden.
        /// </remarks>
        public int SpawnerUid = 0;

        public Cell(Coords coords)
        {
            Coords = coords;
            CellDir = CellDir.SE;
        }

        public void SwitchCellDir()
        {
            CellDir = CellDir.Opposite();
        }

        public bool IsShuffable()
        {
            if (IsAnyOf(CellState.NotShuffable)) return false;

            if (IsAnyOf(CellState.Ivy)) return false;

            if (HasMultiSizeCellReference()) return false;

            return !Tile.IsNull() && !Tile.IsAnyOf(TileState.NotShufflable) && Tile.Kind.IsColored();
        }

        public bool IsSuperBoostable()
        {
            return !Tile.IsNull() && Tile.IsSimple() && IsNoneOf(CellState.Ivy) && !HasMultiSizeCellReference();
        }

        public bool IsBackState()
        {
            return (State & (CellState.BackOne | CellState.BackDouble)) != 0;
        }
        public bool IsPetal()
        {
            return (State & (CellState.Petal)) != 0;
        }

        public bool Is(CellState state)
        {
            return (State & state) == state;
        }

        public bool IsAnyOf(CellState state)
        {
            return (State & state) != 0;
        }

        public bool IsNoneOf(CellState state)
        {
            return (State & state) == 0;
        }

        public bool IsColorBombTargetable()
        {
            return (State & CellState.Ivy) == 0 && !HasMultiSizeCellReference() && !Tile.IsNull() && Tile.IsSimple();
        }
        
        public void ClearState(bool excludeSpecialStates = false)
        {
            if(!excludeSpecialStates || !IsAnyOf(CellState.Despawner | CellState.Spawner | CellState.BackDouble | CellState.BackOne |
                                               CellState.Petal | CellState.DestructibleWall | CellState.Water | CellState.FlagEnd))
            {
                State = CellState.None;
            }
        }

        public void Add(CellState state)
        {
            State |= state;
        }

        public void Remove(CellState state)
        {
            State &= ~state;

            switch (state)
            {
                case CellState.None:
                    break;

                case CellState.Spawner:
                    break;

                case CellState.Despawner:
                    break;

                case CellState.Wall:
                    Walls = null;
                    break;

                case CellState.InvisibleWall:
                    InvisibleWalls = null;
                    break;
                
                case CellState.DestructibleWall:
                    DestructibleWalls = null;
                    break;

                case CellState.BackOne:
                case CellState.BackDouble:
                case CellState.NotShuffable:
                case CellState.AssistMarker:
                case CellState.Ivy:
                case CellState.Tnt:
                case CellState.Petal:
                case CellState.IceBar:
                case CellState.MetalBar:
                case CellState.Water:
                case CellState.FlagEnd:
                    break;


                default:
                    BDebug.LogError(LogCat.Match3, "Not expected cell state removed: " + state);
                    break;
            }
        }

        /// <summary>
        /// Is cell allowed for swap move, not including check is Tile swappable.
        /// </summary>
        public bool IsBaseCellSwappable()
        {
            const CellState stateToExclude = CellState.Ivy | CellState.NotShuffable | CellState.Water | CellState.FlagEnd;
            return IsNoneOf(stateToExclude)
                && !HasMultiSizeCellReference();
        }

        /// <summary>
        /// Is cell allowed for matches, not including check is Tile can be matched.
        /// </summary>
        public bool IsBaseCellMatchable()
        {
            return IsNoneOf(CellState.Ivy) && !HasMultiSizeCellReference();
        }

        public bool HasTile()
        {
            return !ReferenceEquals(Tile, null);
        }

        public bool CanAcceptTile()
        {
            return ReferenceEquals(Tile, null) && IsBusy <= 0 && !IsAnyOf(CellState.NotAcceptingTiles) && !HasMultiSizeCellReference();
        }

        /// <summary>
        /// Is this cell receives damage before tile.
        /// </summary>
        public bool CanCellReceiveDirectDamage(DamageSource damage, Grid grid, BoosterItem item = BoosterItem.Shovel)
        {
            if (IsAnyOf(CellState.InvinvibleWhileBusy))
                return false;
             

            var mainCell = GetMainCellReference(out _);

            if (mainCell.HasTile() && mainCell.Tile.Speciality is TileSpeciality.Gondola or TileSpeciality.TukTuk )
            {
                return mainCell.Tile.CheckApplicability(item, mainCell, grid);
            }

            if (Tile is null && !CanReceiveAdjacentDamageWhenCellIsEmpty)
            {
                return (DamageSource.Adjacent & damage) == 0;
            }
            
            if (damage == DamageSource.PropellerCombo)
                return false;
            
            if (HasMultiSizeCellReferenceWithCellOverlay()) 
                return false;

            if (BackgroundCount > 0 || TntCount > 0 || IvyCount > 0 || GetDestructibleWallCount() > 0)
            {
                var applicableDamage = ApplicableDamageSource();
                return (applicableDamage & damage) != 0;
            }

            return false;

        }

        private DamageSource ApplicableDamageSource()
        {
            if (IsAnyOf(CellState.Ivy | CellState.Tnt))
            {
                const DamageSource ivyOrTntHitDamageSources = DamageSource.AdjacentGeneral | DamageSource.PowerUp |
                                                              DamageSource.Whirlpool | DamageSource.Dynamite |
                                                              DamageSource.UsableBoost | DamageSource.SuperBoost |
                                                              DamageSource.Skunk | DamageSource.FireWorks;

                return ivyOrTntHitDamageSources;
            }
            
            if (HasTile())
            {
                if (Tile.Speciality == TileSpeciality.ColorBomb &&
                    IsAnyOf(CellState.BackOne | CellState.BackDouble | CellState.Petal | CellState.DestructibleWall))
                {
                    return DamageSource.PowerUp | DamageSource.Whirlpool | DamageSource.Dynamite | DamageSource.FireWorks;
                }
            }

            return DamageSource.None;
        }

        public void ReplaceTile(Tile tile)
        {
            if(HasTile())
                tile.Id = Tile.Id;
            
            Tile = tile;
        }

        public void AddTile(Tile tile)
        {
            if (Tile is not null)
            {
                M3Debug.LogError("Trying to add tile where it already exists");
            }

            Tile = tile;
        }

        public void HardRemoveTile(int busyTime, HitWaitParams hitWaitParams = null)
        {
            if (Tile is null)
                return;

            HitSourceUid = -1;
            if (hitWaitParams != null)
            {
                if (!Tile.IsAffectingCellState(hitWaitParams.DamageSource, true))
                {
                    HitSourceUid = hitWaitParams.HitSourceUid;
                }
            }

            Tile = null;
            IsBusy = Mathf.Max(0, Mathf.Max(IsBusy, busyTime));
        }

        public void AddWall(CardinalDirections wallDirection)
        {
            if (IsAnyOf(CellState.Wall))
            {
                Walls.Directions |= wallDirection;
            }
            else
            {
                if (Walls != null)
                {
                    Walls.Directions |= wallDirection;
                }
                else
                {
                    Walls = new CellWalls(wallDirection);
                }

                Add(CellState.Wall);
            }
        }

        public void AddInvisibleWall(CardinalDirections wallDirection)
        {
            if (IsAnyOf(CellState.InvisibleWall))
            {
                InvisibleWalls.Directions |= wallDirection;
            }
            else
            {
                if (InvisibleWalls != null)
                {
                    InvisibleWalls.Directions |= wallDirection;
                }
                else
                {
                    InvisibleWalls = new CellWalls(wallDirection);
                }

                Add(CellState.InvisibleWall);
            }
        }

        public void RemoveWall(CardinalDirections wallDirection)
        {
            if (Walls == null)
            {
                return;
            }
            Walls.Directions &= ~wallDirection;

            if (Walls.Directions != CardinalDirections.None) return;
            Remove(CellState.Wall);
        }

        public void RemoveInvisibleWall(CardinalDirections wallDirection)
        {
            if (InvisibleWalls == null)
            {
                return;
            }

            InvisibleWalls.Directions &= ~wallDirection;

            if (InvisibleWalls.Directions != CardinalDirections.None) return;
            Remove(CellState.InvisibleWall);
        }

        public bool HasAnyWall(CardinalDirections wallDirection)
        {
            var hasWall = (State & CellState.Wall) != 0 && Walls != null && (Walls.Directions & wallDirection) != 0;
            var hasInvisibleWall = (State & CellState.InvisibleWall) != 0 && InvisibleWalls != null && (InvisibleWalls.Directions & wallDirection) != 0;
            var hasDestructibleWall = (State & CellState.DestructibleWall) != 0 && DestructibleWalls != null && DestructibleWalls.DestructibleWall[DestructibleWalls.CardinalToIndex(wallDirection)].Count > 0;
            return  hasWall || hasInvisibleWall || hasDestructibleWall;
        }

        public bool HasDestructibleWall()
        {
            if (DestructibleWalls == null)
            {
                return false;
            }

            foreach (var wall in DestructibleWalls.DestructibleWall)
            {
                if (wall.Count > 0)
                {
                    return true;
                }
            }

            return false;
        }

        public int GetDestructibleWallCount()
        {
            if (DestructibleWalls == null)
            {
                return 0;
            }

            var totalCount = 0;
            foreach (var wall in DestructibleWalls.DestructibleWall)
            {
                if (wall != null)
                {
                    totalCount += wall.Count;
                }
            }

            return totalCount;
        }


        public bool HasWallTo(Coords coords)
        {
            var dir = CardinalDirectionsHelper.GetCardinalDirectionFromCoords(Coords, coords);
            return HasAnyWall(dir);
        }

        public bool IsTileKind(TileKinds kind)
        {
            if (ReferenceEquals(Tile, null)) return false;
            return Tile.Kind == kind;
        }
        
        public bool IsTileKind(List<TileKinds> kind)
        {
            return !ReferenceEquals(Tile, null) && kind.Contains(Tile.Kind);
        }

        public override string ToString()
        {
            return "{" + Coords + " CellState=" + State + " " + (!ReferenceEquals(Tile, null) ? Tile.ToStringWithOrigin() : "[No]") + "}";
        }

        public string ToConsole()
        {
            return "{" + Coords + " CellState=" + State + " " + (!ReferenceEquals(Tile, null) ? Tile.ToConsole() : "[No]") + "}";
        }

        Coords IGridActor.GetCoords(Grid grid)
        {
            return Coords;
        }

        public Cell CloneIntoUndefined()
        {
            var newCell = new Cell(Coords)
            {
                State = State,
                BackgroundCount = BackgroundCount,
                IvyCount = IvyCount,
                IceBarStatus = IceBarStatus,
                MetalBarStatus = MetalBarStatus,
                IsBusy = IsBusy,
                SpawnerUid = SpawnerUid,
                Walls = Walls?.Clone(),
                InvisibleWalls = InvisibleWalls?.Clone(),
                DestructibleWalls = DestructibleWalls?.Clone(),
                SizeX = SizeX,
                SizeY = SizeY,
                TntCount = TntCount,
                TntKind = TntKind,
                TntTarget = TntTarget
            };

            if (!ReferenceEquals(Tile, null))
                newCell.AddTile(Tile.CloneIntoUndefined());

            return newCell;
        }

        public Cell Clone(Coords? newCoords = null)
        {
            var newCell = new Cell(newCoords ?? Coords)
            {
                State = State,
                BackgroundCount = BackgroundCount,
                IvyCount = IvyCount,
                IceBarStatus = IceBarStatus,
                MetalBarStatus = MetalBarStatus,
                IsBusy = IsBusy,
                SpawnerUid = SpawnerUid,
                Walls = Walls?.Clone(),
                InvisibleWalls = InvisibleWalls?.Clone(),
                DestructibleWalls = DestructibleWalls?.Clone(),
                SizeX = SizeX,
                SizeY = SizeY,
                TntCount = TntCount,
                TntKind = TntKind,
                TntTarget = TntTarget,
            };

            if (!ReferenceEquals(Tile, null))
                newCell.AddTile(Tile.Clone());

            return newCell;
        }

        public void SwapTileWith(Cell otherCell)
        {
            (Tile, otherCell.Tile) = (otherCell.Tile, Tile);
        }

        public int SpecialOrder()
        {
            if (!ReferenceEquals(Tile, null))
            {
                if (Tile.IsBoost)
                    return 0;
            }

            return 1;
        }

        /// <summary>
        /// Update cell state based on background value.
        /// </summary>
        /// <remarks>
        /// If background hp value changed, state must be updated correspondingly.
        /// </remarks>
        public void UpdateCellBackgroundState()
        {
            if (IsAnyOf(CellState.Ivy))
            {
                if (IvyCount == 0)
                {
                    Remove(CellState.Ivy);
                }
            }
            else if (IsPetal())
            {
                if (BackgroundCount == 0)
                {
                    Remove(CellState.Petal);
                }
            }
            else if (IsBackState())
            {
                if (IsAnyOf(CellState.BackDouble))
                {
                    Remove(CellState.BackDouble);
                }

                if (IsAnyOf(CellState.BackOne))
                {
                    Remove(CellState.BackOne);
                }

                if (BackgroundCount == 1)
                {
                    Add(CellState.BackOne);
                }
                if (BackgroundCount == 2)
                {
                    Add(CellState.BackDouble);
                }
            }
            else if (IsAnyOf(CellState.FlagEnd))
            {
                Remove(CellState.FlagEnd);
            }
        }
        
        public CellDto ToDto()
        {
            var dto = new CellDto
            {
                X = Coords.X,
                Y = Coords.Y,
                TileDto = Tile?.ToDto(),
                BackgroundCount = BackgroundCount,
                IvyCount =  IvyCount,
                IceBarStatus = IceBarStatus,
                MetalBarStatus = MetalBarStatus,
                State = State,
                Walls = Walls?.ToDto(),
                InvisibleWalls = InvisibleWalls?.ToDto(),
                DestructibleWalls = DestructibleWalls?.Clone(),
                SizeX = SizeX,
                SizeY = SizeY,
                TntCount = TntCount,
                TntKind = TntKind,
                TntTarget = TntTarget,
            };
            return dto;
        }

        public void FromDto(CellDto dto)
        {
            Coords = new Coords(dto.X, dto.Y);
            if (dto.TileDto is null || dto.TileDto.hp <= 0)
            {
                Tile = null;
            }
            else
            {
                var tileDto = dto.TileDto;
                var tempParams = tileDto.TileParams == null ? null : new List<TileParam>(tileDto.TileParams);
                Tile ??= TileFactory.CreateTile(tileDto.Id,tileDto.Asset,new TileOrigin(),(TileKinds)tileDto.Kind, tempParams);
                Tile.FromDto(dto.TileDto);
            }

            BackgroundCount = dto.BackgroundCount;
            IvyCount = dto.IvyCount;
            IceBarStatus = dto.IceBarStatus;
            MetalBarStatus = dto.MetalBarStatus;
            State = dto.State;
            if (dto.Walls is null)
            {
                Walls = null;
            }
            else
            {
                Walls ??= new CellWalls(CardinalDirections.None);
                Walls.FromDto(dto.Walls);
            }

            if (dto.InvisibleWalls is null)
            {
                InvisibleWalls = null;
            }
            else
            {
                InvisibleWalls ??= new CellWalls(CardinalDirections.None);
                InvisibleWalls.FromDto(dto.InvisibleWalls);
            }
            
            DestructibleWalls = dto.DestructibleWalls?.Clone();
            
            SizeX = dto.SizeX;
            SizeY = dto.SizeY;
            TntCount = dto.TntCount;
            TntKind = dto.TntKind;
            TntTarget = dto.TntTarget;
        }

        public bool CanCellReceiveDropItemDamage(DamageSource damageSource)
        {
            return HasTile() && Tile.Speciality == TileSpeciality.DropItem;
        }

        /// <summary>
        /// For multi-size blockers, we are storing a list with all the cells that 'belong' to the main cell
        /// </summary>
        /// <param name="otherCell"></param>
        public void AddReferencedCell(Cell otherCell)
        {
            _referencedCells.Add(otherCell);
        }
        
        public IEnumerable<(long key, int value)> GetAssistState()
        {
            if (IsAnyOf(CellState.Petal))
            {
                yield return PetalSingleLayerAssistState;
            }

            if (IsAnyOf(CellState.DestructibleWall))
            {
                _destructibleWallLayerAssistState.value = GetDestructibleWallCount();
                yield return _destructibleWallLayerAssistState;
            }

            if (IsAnyOf(CellState.BackOne))
            {
                yield return GrassSingleLayerAssistState;
            }

            if (IsAnyOf(CellState.BackDouble))
            {
                yield return GrassDoubleLayerAssistState;
            }
        }
    }
}
