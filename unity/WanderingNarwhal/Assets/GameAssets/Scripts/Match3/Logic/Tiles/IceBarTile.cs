using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class IceBarTile : BarTile
    {
        private (long, int value) _assistState = ((long) GoalType.IceBar, DefaultHp);
        
        public IceBarTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.IceBar;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.IceBar;
            AddMandatoryParamsTile();
            BarOrientation = TileParamEnum.IceBarOrientation;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }
    }
}