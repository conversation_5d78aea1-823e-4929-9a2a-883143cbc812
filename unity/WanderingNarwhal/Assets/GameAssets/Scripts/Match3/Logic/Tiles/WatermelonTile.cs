using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Core;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class WatermelonTile : Tile
    {
        private (long, int value) _assistState = ((long) GoalType.Watermelon, DefaultHp);
        
        public WatermelonTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Watermelon;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            BoostersApplicability = BoosterItem.Shovel | BoosterItem.Balloon | BoosterItem.Wind | BoosterItem.Rain |
                                    BoosterItem.Vertical | BoosterItem.Horizontal;
            State |= TileState.Watermelon;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override void AddMandatoryParamsTile()
        {
            if (GetParam(TileParamEnum.AdjacentHp) <= 0)
            {
                BDebug.Log(LogCat.Match3, "Spawned watermelon tile doesn't have hp parameter!");
                SetParam(TileParamEnum.AdjacentHp, 1);
            }

            base.AddMandatoryParamsTile();
        }
    }
}