using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Systems.GoalsService;
using BBB.Match3.Systems;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class TukTukTile : Tile
    {
        public TukTukTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.TukTuk;
            State |= TileState.TukTuk;
            AddMandatoryParamsTile();
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            var count = GetParam(TileParamEnum.TukTukCount);
            var damageSource = hitContext.HitWaitParams.DamageSource;
            if (count == 0 && damageSource == DamageSource.TukTuk)
            {
                MarkAsDead();
                return false;
            }

            return true;
        }

        public override bool CheckApplicability(BoosterItem boosterItem, Cell cell, Grid grid)
        {
            return (State & (TileState.ChainMod | TileState.IceCubeMod | TileState.SandMod)) != 0;
        }
    }
}