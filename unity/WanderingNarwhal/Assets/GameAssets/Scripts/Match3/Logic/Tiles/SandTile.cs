using BBB;
using BBB.Match3;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SandTile : Tile
    {
        public SandTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Sand;
            State |= TileState.Sand;
            AddMandatoryParamsTile();
        }
    }
}