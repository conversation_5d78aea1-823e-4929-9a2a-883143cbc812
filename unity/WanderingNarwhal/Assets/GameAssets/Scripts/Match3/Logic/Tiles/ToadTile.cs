using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class ToadTile : Tile
    {
        public ToadTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Toad;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.Toad;
            AddMandatoryParamsTile();
        }
    }
}