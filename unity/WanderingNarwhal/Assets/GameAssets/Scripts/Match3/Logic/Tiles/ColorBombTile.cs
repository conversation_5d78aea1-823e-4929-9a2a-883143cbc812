using System.Collections.Generic;
using BBB;
using BBB.Match3;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class ColorBombTile : Tile
    {
        public ColorBombTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Kind = (int) TileKinds.None;
            AllowedDamageSource = DamageSource.TapOrSwap;
            Speciality = TileSpeciality.ColorBomb;
            BoostersApplicability = BoosterItem.Wind;
            State |= TileState.ColorBomb;
            AddMandatoryParamsTile();
        }
    }
}