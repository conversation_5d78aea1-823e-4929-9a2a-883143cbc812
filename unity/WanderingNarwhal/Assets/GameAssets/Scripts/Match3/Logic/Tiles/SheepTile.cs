using BBB;
using BBB.Match3;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SheepTile : Tile
    {
        private (long, int value) _assistState = ((long) GoalType.Sheep, DefaultHp);
        
        public SheepTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams) : base(id,tileAsset,tileOrigin,tileKind,tileParams)
        {
            Speciality = TileSpeciality.Sheep;
            State |= TileState.Sheep;
            AddMandatoryParamsTile();
        }
        
        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }
    }
}