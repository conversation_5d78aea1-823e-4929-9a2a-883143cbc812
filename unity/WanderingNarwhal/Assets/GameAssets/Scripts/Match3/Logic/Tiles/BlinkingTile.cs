using System.Collections.Generic;
using BBB;
using BBB.Match3;
using BBB.Match3.Logic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class BlinkingTile : Tile
    {
        public BlinkingTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.BlinkingTile;
            AllowedDamageSource = DamageSource.Bomb | DamageSource.MultiBomb | DamageSource.LineBreakerArrow |
                                  DamageSource.Skunk | DamageSource.FireWorks;
            State |= TileState.Blinking;
            AddMandatoryParamsTile();
        }
    }
}