using BBB;
using BBB.Match3;
using BebopBee.Core;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class GondolaTile : Tile
    {
        private const int GondolaMinPathLength = 2;

        public GondolaTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Gondola;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.Gondola;
            AddMandatoryParamsTile();
        }

        public override bool CheckApplicability(BoosterItem boosterItem, Cell cell, Grid grid)
        {
            if ((State & (TileState.ChainMod | TileState.IceCubeMod | TileState.SandMod)) != 0) return true;

            var isGondolaModActive = (State & TileState.GondolaMod) != 0;
            return !isGondolaModActive || (!IsGondolaReached() && !IsGondolaBlocked(cell, grid));
        }

        private bool IsGondolaReached()
        {
            return GetParam(TileParamEnum.GondolaReached) > 0;
        }

        private static bool IsGondolaBlocked(Cell cell, Grid grid)
        {
            var path = PathFactory.GetGondolaPath(cell, grid);

            if (path is not {Count: > GondolaMinPathLength}) return false;
            var nextCell = grid.GetCell(path[GondolaMinPathLength]);
            nextCell = nextCell.GetMainCellReference(out _);
            return nextCell.HasTile();
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            if (simulationContext.CellsToDamageQueue == null || simulationContext.Queue == null)
            {
                return base.TryApplyAdjacentDamage(simulationContext, hitContext);
            }

            // Make sure the hit was on Main cell
            if ((hitContext.HitWaitParams.DamageSource & DamageSource.TargetingMechanics) != 0)
            {
                var gondolaHit = new Hit<Cell>(
                    hitContext.Hit.ImmuneTiles,
                    null,
                    hitContext.Hit.BusyWait,
                    DamageSource.Gondola,
                    coords: hitContext.Hit.Coords,
                    sourceCoords: hitContext.Hit.SourceCoords,
                    hitSourceUid: hitContext.Hit.HitSourceUid,
                    boostInfo: hitContext.Hit.BoostInfo,
                    previousDamageSource: hitContext.HitWaitParams.DamageSource);

                simulationContext.CellsToDamageQueue.AppendHit(1, gondolaHit);
            }
            else
            {
                const int minPath = 2;
                var path = PathFactory.GetGondolaPath(hitContext.MainCell, simulationContext.Grid);
                if (path is not { Count: > minPath })
                {
                    return false;
                }

                var nextCell = simulationContext.Grid.GetCell(path[1]);
                var goalCell = simulationContext.Grid.GetCell(path[2]);

                var blockerCell = goalCell.GetMainCellReference(out _);
                if (blockerCell.HasTile())
                {
                    return false;
                }

                PathFactory.GetGondolaOrientation(nextCell.Coords, goalCell.Coords, out var orientation);
                PathFactory.GetGondolaSize(orientation, out var sizeX, out var sizeY);

                hitContext.MainCell.HardRemoveTile(0);
                nextCell.ReplaceTile(this);
                nextCell.Tile.SetParam(TileParamEnum.SizeX, sizeX);
                nextCell.Tile.SetParam(TileParamEnum.SizeY, sizeY);
                nextCell.Tile.SetParam(TileParamEnum.GondolaOrientation, orientation);

                var tileParamList = new List<(TileParamEnum, int)>
                {
                    new(TileParamEnum.SizeX, sizeX),
                    new(TileParamEnum.SizeY, sizeY),
                    new(TileParamEnum.GondolaOrientation, orientation)
                };

                var collectGoal = false;

                if (goalCell.IsAnyOf(CellState.FlagEnd))
                {
                    nextCell.Tile.SetParam(TileParamEnum.GondolaReached, 1);
                    tileParamList.Add((TileParamEnum.GondolaReached, 1));
                    collectGoal = true;
                }

                simulationContext.Grid.RefrehsAllCellsMultisizeCaches();

                simulationContext.Handler.HandleGondolaMovementAction(hitContext.MainCell.Coords, nextCell.Coords,
                    goalCell.Coords, Id, orientation, collectGoal, hitContext.HitWaitParams, tileParamList);

                if (!collectGoal)
                {
                    return true;
                }
                simulationContext.GoalSystem.TryReduceGoalIfNeeded(GoalType.Gondola);
                simulationContext.PopSystem.KillTile(simulationContext.Grid, simulationContext.ReactionHandler,
                    simulationContext.InputParams, simulationContext.GoalSystem, simulationContext.Queue,
                    simulationContext.CellsToDamageQueue, nextCell, hitContext.HitWaitParams, false);
            }

            return true;
        }
    }
}