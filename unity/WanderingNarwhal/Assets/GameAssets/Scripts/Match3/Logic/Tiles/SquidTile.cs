using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Core;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Systems.GoalsService;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SquidTile : Tile
    {
        public SquidTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Squid;
            AllowedDamageSource = DamageSource.AdjacentGeneral | DamageSource.SameTileKind | DamageSource.Skunk |
                                  DamageSource.PowerUp | DamageSource.Whirlpool | DamageSource.Dynamite |
                                  DamageSource.UsableBoost | DamageSource.SuperBoost | DamageSource.FireWorks;
            State |= TileState.Squid;
            AddMandatoryParamsTile();
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);

            var asSingleSquid = adjacentHp > 0;
            
            // Extract ref parameters to modify
            var coordsOffset = hitContext.CoordsOffset;
            var skin = hitContext.Skin;
    
            var appliedDamage = TryApplySquidTileDamage(simulationContext.InputParams, hitContext.MainCell, 
                ref coordsOffset, hitContext.Hit, hitContext.HitWaitParams.DamageSource, 
                ref skin, simulationContext.Handler, asSingleSquid);
    
            // Update ref parameters back to context
            hitContext.CoordsOffset = coordsOffset;
            hitContext.Skin = skin;

            if (appliedDamage)
            {
                simulationContext.GoalSystem.OnGoalsReceived(new GoalActionRemoveAdjacentLayer(Speciality,
                    hitContext.Coords + hitContext.CoordsOffset));
            }

            if (adjacentHp <= 0)
            {
                return false;
            }
            var reducedAdjacentHp = adjacentHp - 1;
            if (reducedAdjacentHp > 0)
            {
                return true;
            }

            //Need to run the calculation again since we can have multi size tile with multiple squids 
            appliedDamage = TryApplySquidTileDamage(simulationContext.InputParams, hitContext.MainCell,
                ref coordsOffset, hitContext.Hit, hitContext.HitWaitParams.DamageSource, 
                ref skin, simulationContext.Handler, asSingleSquid: false);
    
            // Update ref parameters again
            hitContext.CoordsOffset = coordsOffset;
            hitContext.Skin = skin;

            if (appliedDamage)
            {
                simulationContext.GoalSystem.OnGoalsReceived(new GoalActionRemoveAdjacentLayer(Speciality,
                    hitContext.Coords + hitContext.CoordsOffset));
            }

            return false;
        }

        protected override bool CanBeDamagedBy(DamageSource damageSource, TileKinds damageTileKind, DamageSource totalAllowedDamageSource)
        {
            if (IsSameTileKindDamage(damageSource, totalAllowedDamageSource) && HasParam(TileParamEnum.SquidsState))
            {
                var state = GetParam(TileParamEnum.SquidsState);
                var colorNum = SquidTileLayer.ColorToEncodedNum(damageTileKind);
                if (!SquidTileLayer.IsColorStateExistInState(state, colorNum))
                {
                    return false;
                }
            }

            return base.CanBeDamagedBy(damageSource, damageTileKind, totalAllowedDamageSource);
        }

        /// <summary>
        ///     Apply hit to Squid tile.
        /// </summary>
        /// <remarks>
        ///     Squid has two possible damage models: with AdjacentDamage and without,
        ///     which is determined by asSingleSquid flag parameter.
        ///     This two models are different, because there are two kinds of squid tiles possible:
        ///     squid tile with many little octopus inside with different colors and
        ///     second type is one big octopus in entire tile. -VK
        /// </remarks>
        /// <param name="damageSource"></param>
        /// <param name="skin">Skin index, which will be used to setup fly goal object.</param>
        /// <param name="cell"></param>
        /// <param name="coordsOffset"></param>
        /// <param name="inputParams"></param>
        /// <param name="hit"></param>
        /// <param name="handler"></param>
        /// <param name="asSingleSquid"></param>
        private bool TryApplySquidTileDamage(SimulationInputParams inputParams, Cell cell,
            ref Coords coordsOffset, Hit hit, DamageSource damageSource, ref int? skin, IRootSimulationHandler handler,
            bool asSingleSquid = false)
        {
            if (inputParams.InitialLoop)
                return false;

            if (!HasParam(TileParamEnum.SquidsState)) return false;
            if (HasParam(TileParamEnum.AdjacentHp))
            {
                var reducedAdjacentHp = GetParam(TileParamEnum.AdjacentHp) - 1;
                SetParam(TileParamEnum.AdjacentHp, reducedAdjacentHp);
            }

            var squidsCount = GetParam(TileParamEnum.SquidsCount);
            var state = GetParam(TileParamEnum.SquidsState);
            var sizeX = GetParam(TileParamEnum.SizeX);
            var sizeY = GetParam(TileParamEnum.SizeY);
            var damageColorNum = SquidTileLayer.ColorToEncodedNum(hit.SourceKind);

            var damagedSpecificStateIndex = SquidTileLayer.CoordsOffsetToStateIndex(coordsOffset, sizeX, sizeY);
            var colorNumAtCoordsOffset = SquidTileLayer.GetColorNumFromState(state, damagedSpecificStateIndex);
            var isDamageColorMatchesStateAtCoordsOffset =
                damagedSpecificStateIndex >= 0 && (damageSource & DamageSource.AdjacentGeneral) == 0 &&
                colorNumAtCoordsOffset != 0
                || (damageSource & DamageSource.AdjacentGeneral) != 0 &&
                colorNumAtCoordsOffset == damageColorNum;
            if (isDamageColorMatchesStateAtCoordsOffset)
            {
                // the coordsOffset value should not change here (conversion should return same value) but it is here just in case and because it is present in other 2 cases.
                coordsOffset =
                    new Coords(SquidTileLayer.ConvertNumIndexToCoordsOffset(damagedSpecificStateIndex, sizeX, sizeY,
                        sizeX * sizeY));
                skin = SquidTileLayer.GetColorNumFromState(state, damagedSpecificStateIndex);

                // Nullify color state at given position, because it is equal to incoming hit color num and it is exactly at hit position.
                // If this branch is not executed, then we nullify random num in the state (in second and third branches).
                state = SquidTileLayer.SetColorNumInState(state, 0, damagedSpecificStateIndex);
            }
            else
            {
                // Count how many color nums in the state are equal to incoming hit color num.
                var sameColorCount = 0;
                if ((damageSource & DamageSource.AdjacentGeneral) != 0 && damageColorNum != 0)
                    for (var i = 0; i < SquidTileLayer.MAX_SUBITEMS_COUNT; i++)
                    {
                        var subState = SquidTileLayer.GetColorNumFromState(state, i);
                        if (subState == damageColorNum) sameColorCount++;
                    }

                if (sameColorCount == 0)
                {
                    // Try to hit and nullify random non-zero num at random index in the state.
                    var randomIndex = RandomSystem.Next(squidsCount);
                    var nonZeroSubStateIndex = 0;
                    for (var i = 0; i < SquidTileLayer.MAX_SUBITEMS_COUNT; i++)
                    {
                        var subState = SquidTileLayer.GetColorNumFromState(state, i);
                        if (subState == 0) continue;
                        if (nonZeroSubStateIndex == randomIndex)
                        {
                            coordsOffset =
                                new Coords(SquidTileLayer.ConvertNumIndexToCoordsOffset(i, sizeX, sizeY,
                                    sizeX * sizeY));
                            skin = SquidTileLayer.GetColorNumFromState(state, i);
                            state = SquidTileLayer.SetColorNumInState(state, 0, i);
                            break;
                        }

                        nonZeroSubStateIndex++;
                    }

                    if (nonZeroSubStateIndex < randomIndex)
                        BDebug.LogError(LogCat.Match3, "PopSystem: Failed to find any item to remove from state");
                }
                else
                {
                    // Try to hit and nullify num with same value as damage color num at random index in the state.
                    var sameColorToRemoveIndex = RandomSystem.Next(sameColorCount);
                    var sameColorIndex = 0;
                    for (var i = 0; i < SquidTileLayer.MAX_SUBITEMS_COUNT; i++)
                    {
                        var subState = SquidTileLayer.GetColorNumFromState(state, i);
                        if (subState != damageColorNum) continue;
                        if (sameColorIndex == sameColorToRemoveIndex)
                        {
                            coordsOffset =
                                new Coords(SquidTileLayer.ConvertNumIndexToCoordsOffset(i, sizeX, sizeY,
                                    sizeX * sizeY));
                            skin = damageColorNum;
                            state = SquidTileLayer.SetColorNumInState(state, 0, i);
                            break;
                        }

                        sameColorIndex++;
                    }
                }
            }

            SetParam(TileParamEnum.SquidsState, state);
            if (!asSingleSquid)
            {
                var newSquidsCount = squidsCount - 1;
                SetParam(TileParamEnum.SquidsCount, newSquidsCount);

                var tileParamList = new List<(TileParamEnum, int)>
                {
                    new(TileParamEnum.SquidsCount, newSquidsCount),
                    new(TileParamEnum.SquidsState, state)
                };

                handler.AddAction(new ActionChangeTileParam(Id, cell.Coords,
                    tileParamList, hit.GetHitParams()));

                if (newSquidsCount <= 0) MarkAsDead();
            }
            else
            {
                var count = GetParam(TileParamEnum.AdjacentHp);

                var tileParamList = new List<(TileParamEnum, int)>
                {
                    new(TileParamEnum.AdjacentHp, count),
                    new(TileParamEnum.SquidsState, state)
                };

                handler.AddAction(new ActionChangeTileParam(Id, cell.Coords,
                    tileParamList, hit.GetHitParams()));
            }

            return true;
        }
    }
}