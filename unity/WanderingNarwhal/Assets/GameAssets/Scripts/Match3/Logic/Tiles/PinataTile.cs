using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class PinataTile : Tile
    {
        private const int PinataHp = 1;
        private static readonly (long, int) AssistState = ((long) GoalType.Pinata, PinataHp);
        
        public PinataTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams) 
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Pinata;
            AllowedDamageSource = DamageSource.PowerUp | DamageSource.Whirlpool | DamageSource.Dynamite |
                                  DamageSource.UsableBoost | DamageSource.SuperBoost |
                                  DamageSource.Skunk | DamageSource.FireWorks;
            BoostersApplicability = BoosterItem.Shovel | BoosterItem.Balloon | BoosterItem.Wind | BoosterItem.Rain |
                                    BoosterItem.Vertical | BoosterItem.Horizontal;
            State |= TileState.Pinata;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            yield return AssistState;
        }
    }
}