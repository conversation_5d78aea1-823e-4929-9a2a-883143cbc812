using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class HiveTile : Tile
    {
        private const int BeeSpawnCount = 1;

        public HiveTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams = null)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Hive;
            State |= TileState.Hive;
            AddMandatoryParamsTile();
        }

        public override void AddMandatoryParamsTile()
        {
            SetParam(TileParamEnum.TileToSpawnFromReaction, (int) TileAsset.Bee);
            SetParam(TileParamEnum.TileCreateCountForReaction, BeeSpawnCount);
            base.AddMandatoryParamsTile();
        }

        public override bool CheckApplicability(BoosterItem boosterItem, Cell cell, Grid grid)
        {
            if ((State & (TileState.ChainMod | TileState.IceCubeMod | TileState.SandMod)) != 0) return true;
            return GetParam(TileParamEnum.BeeHiveOutOfBeesFlag) <= 0;
        }
    }
}