using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class FireWorksTile : Tile
    {
        private (long, int value) _assistState = ((long) GoalType.FireWorks, DefaultHp);
        
        public FireWorksTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.FireWorks;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.FireWorks;
            ShouldDelaySimulationOnReaction = true;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);
            if (adjacentHp <= 0)
            {
                return false;
            }
            
            var reducedAdjacentHp = adjacentHp - 1;

            if (reducedAdjacentHp <= 0)
            {
                var removeRandomTileQueue = SpecialTileSystem.RemoveWithFireWorks(hitContext.Cell, simulationContext.SpawnSystem, 
                    simulationContext.InputParams, simulationContext.SettleTileSystem, simulationContext.Handler, 
                    hitContext.HitWaitParams);

                if (removeRandomTileQueue != null)
                {
                    simulationContext.CellsToDamageQueue.Append(removeRandomTileQueue);
                }
                return base.TryApplyAdjacentDamage(simulationContext, hitContext);
            }

            SetParam(TileParamEnum.AdjacentHp, reducedAdjacentHp);

            simulationContext.Handler.AddAction(new ActionChangeTileParam(Id, hitContext.MainCell.Coords,
                new List<(TileParamEnum, int)> {new(TileParamEnum.AdjacentHp, reducedAdjacentHp)},
                hitContext.Hit.GetHitParams()));

            return true;
        }
    }
}