using System;
using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Debug;
using BBB.Match3.Logic;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Settings;
using Grid = BBB.Grid;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SlotMachineTile : Tile
    {
        public const int SlotMachineHp = 4;
        private (long, int value) _assistState = ((long) GoalType.SlotMachine, DefaultHp);

        public SlotMachineTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.SlotMachine;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.SlotMachine;
            ShouldDelaySimulationOnReaction = true;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);
            if (adjacentHp <= 0)
            {
                return false;
            }

            var reducedAdjacentHp = adjacentHp - 1;
            if (GetParam(TileParamEnum.SlotMachineOutcome) == (int)SlotMachineRewardType.None)
            {
                var (selectedOutcome, count) = DetermineAndSetSlotMachineOutcome(
                    simulationContext.Grid, simulationContext.InputParams.Settings, 
                    simulationContext.InputParams.Settings.SlotMachineConfiguration);
            
                var slotMachineOutcomeState = (int)GetSlotMachineOutcomeState(selectedOutcome);
                var outcome = (int)selectedOutcome;
                simulationContext.Handler.AddAction(new ActionUpdateSlotRewards(selectedOutcome));

                var tileParamList = new List<(TileParamEnum, int)>
                {
                    (TileParamEnum.SlotMachineOutcome, outcome),
                    (TileParamEnum.TileToSpawnFromReaction, slotMachineOutcomeState),
                    (TileParamEnum.TileCreateCountForReaction, count)
                };

                foreach (var (param, value) in tileParamList)
                {
                    SetParam(param, value);
                }

                simulationContext.Handler.AddAction(new ActionChangeTileParam(Id, hitContext.Cell.Coords,
                    tileParamList, hitContext.Hit.GetHitParams()));
            }

            if (reducedAdjacentHp <= 0)
            {
                return base.TryApplyAdjacentDamage(simulationContext, hitContext);
            }

            SetParam(TileParamEnum.AdjacentHp, reducedAdjacentHp);
            simulationContext.Handler.AddAction(new ActionChangeTileParam(Id, hitContext.MainCell.Coords,
                new List<(TileParamEnum, int)> {new(TileParamEnum.AdjacentHp, reducedAdjacentHp)},
                hitContext.Hit.GetHitParams()));
            
            return true;
        }

        private static (SlotMachineRewardType, int) DetermineAndSetSlotMachineOutcome(Grid grid, M3Settings m3Settings,
            SlotMachineConfiguration slotMachineConfiguration)
        {
            var weightedOutcomesDictionary = slotMachineConfiguration.RewardConfigurations;
            var outcomeWeightsList = new List<float>();
            var outcomeTypesList = new List<SlotMachineRewardType>();

            foreach (var kvp in weightedOutcomesDictionary)
            {
                if (grid.GetSlotMachineRewardCount(kvp.Key) >= m3Settings.SlotMachineMaxSameSpawnsPerLevel) continue;
                outcomeWeightsList.Add(kvp.Value.Weight);
                outcomeTypesList.Add(kvp.Key);
            }

            var selectedOutcome = GetWeightedRandomOutcome(outcomeTypesList, outcomeWeightsList);

            grid.UpdateSlotMachineRewardHolder(selectedOutcome);

            return (selectedOutcome, weightedOutcomesDictionary[selectedOutcome].Count);
        }

        private static TileAsset GetSlotMachineOutcomeState(SlotMachineRewardType slotMachineOutcome)
        {
            return slotMachineOutcome switch
            {
                SlotMachineRewardType.LineBreaker => RandomSystem.Next() > 0.5f ? TileAsset.RowBreaker : TileAsset.ColumnBreaker,
                SlotMachineRewardType.Bomb => TileAsset.Bomb,
                SlotMachineRewardType.ColorBomb => TileAsset.ColorBomb,
                SlotMachineRewardType.Propeller => TileAsset.Propeller,
                SlotMachineRewardType.None => TileAsset.Undefined,
                _ => throw new ArgumentOutOfRangeException(nameof(slotMachineOutcome), slotMachineOutcome,
                    $"<color=red>Unexpected Outcome in SlotMachine Mechanic '{slotMachineOutcome}'</color>")
            };
        }

        private static SlotMachineRewardType GetWeightedRandomOutcome(List<SlotMachineRewardType> outcomes, List<float> weights)
        {
            if (outcomes.Count != weights.Count)
                M3Debug.LogError("Outcomes and weights arrays must have the same length.");

            var totalWeight = 0f;
            foreach (var t in weights)
            {
                totalWeight += t;
            }

            var randomValue = RandomSystem.Next() * totalWeight;

            for (var i = 0; i < outcomes.Count; i++)
            {
                if (randomValue < weights[i])
                    return outcomes[i];
                randomValue -= weights[i];
            }

            // Fallback in case of rounding errors
            return outcomes[^1];
        }

        protected override bool CanBeDamagedBy(DamageSource damageSource, TileKinds damageTileKind, DamageSource totalAllowedDamageSource)
        {
            if ((damageSource & (DamageSource.AdjacentGeneral | DamageSource.RemoveColorTiles)) != 0)
            {
                if (GetParam(TileParamEnum.AdjacentHp) == SlotMachineHp)
                {
                    return false;
                }
            }

            return base.CanBeDamagedBy(damageSource, damageTileKind, totalAllowedDamageSource);
        }
    }
}