using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class BushTile : LayeredTile
    {
        private const int BushGrassSpawnCount = 16; //4x4 grass
        private static readonly (long, int) GrassAssistState = ((long) GoalType.Backgrounds, BushGrassSpawnCount);
        private (long, int value) _assistState = ((long) GoalType.Bush, DefaultHp);

        public BushTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Bush;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.Bush;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
            yield return GrassAssistState;
        }
    }
}