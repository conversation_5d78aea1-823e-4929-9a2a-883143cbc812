using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class DropItemTile : Tile
    {
        public DropItemTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.DropItem;
            AllowedDamageSource = DamageSource.None;
            BoostersApplicability = BoosterItem.None;
            State |= TileState.DropItem;
            AddMandatoryParamsTile();
        }
    }
}