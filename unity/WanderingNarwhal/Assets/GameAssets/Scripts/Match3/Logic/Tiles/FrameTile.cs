using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class FrameTile : Tile
    {
        private (long, int value) _assistState = ((long) GoalType.Animal, DefaultHp);
        
        public FrameTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Frame;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            BoostersApplicability = BoosterItem.Shovel | BoosterItem.Balloon | BoosterItem.Wind | BoosterItem.Rain |
                                    BoosterItem.Vertical | BoosterItem.Horizontal;
            State |= TileState.Frame;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            if (IsAnyOf(TileState.AnimalMod))
            {
                Remove(TileState.AnimalMod);
                simulationContext.Handler.AddAction(new ActionRemoveState(Id, hitContext.MainCell.Coords,
                    TileState.AnimalMod, hitContext.HitWaitParams));
                simulationContext.PopSystem.MarkAnimalReceivedDamage(hitContext.MainCell.Coords, Id, hitContext.HitWaitParams);
            }

            return base.TryApplyAdjacentDamage(simulationContext, hitContext);
        }
    }
}