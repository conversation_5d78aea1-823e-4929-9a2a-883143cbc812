using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class ColorCrateTile : Tile
    {
        private (long, int value) _assistState = ((long) GoalType.ColorCrate, DefaultHp);
        
        public ColorCrateTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.ColorCrate;
            AllowedDamageSource = DamageSource.AdjacentGeneral | DamageSource.SameTileKind | DamageSource.PowerUp |
                                  DamageSource.Whirlpool | DamageSource.Dynamite | DamageSource.UsableBoost |
                                  DamageSource.SuperBoost | DamageSource.Skunk | DamageSource.FireWorks;
            BoostersApplicability = BoosterItem.Shovel | BoosterItem.Balloon | BoosterItem.Wind | BoosterItem.Rain |
                                    BoosterItem.Vertical | BoosterItem.Horizontal;
            State |= TileState.ColorCrate;
            AddMandatoryParamsTile();
        }
        
        protected override bool CanBeDamagedBy(DamageSource damageSource, TileKinds damageTileKind, DamageSource totalAllowedDamageSource)
        {
            if (IsSameTileKindDamage(damageSource, totalAllowedDamageSource) && Kind != damageTileKind)
            {
                return false;
            }

            return base.CanBeDamagedBy(damageSource, damageTileKind, totalAllowedDamageSource);
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }
    }
}