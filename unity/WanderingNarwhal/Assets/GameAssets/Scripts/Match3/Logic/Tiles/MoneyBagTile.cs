using BBB;
using BBB.Match3;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class MoneyBagTile : Tile
    {
        private const int MoneyBagHp = 1;
        private static readonly (long, int) AssistState = ((long) GoalType.MoneyBag, MoneyBagHp);
        
        public MoneyBagTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.MoneyBag;
            State |= TileState.MoneyBag;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            yield return AssistState;
        }
    }
}