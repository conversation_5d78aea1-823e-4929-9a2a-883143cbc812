using System.Collections.Generic;
using BBB;
using BBB.Match3.Renderer;
using BebopBee.Core.Audio;
using Bebopbee.Core.Extensions.Unity;
using UnityEngine;

public class FixedHealthTileDamageBehaviour : TileLayerViewBase, ICounteredTile
{
    private TileLayerRendererBase _renderer;
    private int _initialHealth;

    protected FixedHealthTileDamageBehaviour(ITileLayer layer) : base(layer)
    {
    }

    protected override void OnInstantiateViewUpdated(GameObject instance, Tile tile, Vector2 cellSize)
    {
        _renderer = instance.GetComponent<TileLayerRendererBase>();
        _initialHealth = tile.GetParam(TileParamEnum.AdjacentHp);

        _renderer.LayerIndex = Animator.StringToHash(_renderer.LayerIndexName);
        _renderer.AnimationTrigger = Animator.StringToHash(_renderer.AnimationTriggerName);
        _renderer.Content.UpdateRectForTile(_renderer.TileBaseMinOffset, _renderer.TileBaseMaxOffset);
        _renderer.Init();
    }

    public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
        bool isVisible)
    {
        base.Apply(tile, coords, container, viewsList, isVisible);
        var count = SelectSheetsCountValue(tile);
        ApplyDamage(count, coords);
    }

    public void ChangeCount(Cell cell)
    {
        ApplyDamage(SelectSheetsCountValue(cell.Tile), cell.Coords);
    }

    private static int SelectSheetsCountValue(Tile tile)
    {
        return tile.GetParam(TileParamEnum.AdjacentHp);
    }

    private void ApplyDamage(int newLevel, Coords? coords = null)
    {
        if (newLevel == _initialHealth)
        {
            return;
        }

        var tileBehaviorContext = new TileBehaviorContext
        {
            FxContext = new PlayFxContext
            {
                FxType = _renderer.FXTypes[newLevel],
                FxRenderer = FxRenderer,
                Position = coords ?? Coords.Zero,
                Duration = _renderer.FXDestroyDurations[newLevel]
            },
            SoundContext = new PlaySoundContext
            {
                SoundId = _renderer.SoundIds[newLevel]
            }
        };

        // All our animations are indexed from 1
        newLevel += 1;

        if (_renderer.Animator != null)
        {
            tileBehaviorContext.AnimationContext = new PlayAnimationContext
            {
                Animator = _renderer.Animator,
                LayerIndex = _renderer.LayerIndex,
                AnimationTrigger = _renderer.AnimationTrigger,
                UpdatedValue = newLevel
            };
        }

        if (_renderer.SpineAnimator != null)
        {
            tileBehaviorContext.SpineContext = new PlaySpineContext
            {
                SkeletonGraphic = _renderer.SpineAnimator,
                LayerIndexName = _renderer.LayerIndexName,
                UpdatedValue = newLevel
            };
        }

        ApplyDamage(tileBehaviorContext);
    }

    private static void ApplyDamage(TileBehaviorContext tileBehaviorContext)
    {
        var fxContext = tileBehaviorContext.FxContext;
        var soundContext = tileBehaviorContext.SoundContext;
        var animationContext = tileBehaviorContext.AnimationContext;
        var spineContext = tileBehaviorContext.SpineContext;

        AudioProxy.PlaySound(soundContext.HasValue ? soundContext.Value.SoundId : string.Empty);

        fxContext?.FxRenderer.SpawnSingleAnimatorEffect(fxContext.Value.Position, fxContext.Value.FxType,
            fxContext.Value.Duration);

        animationContext?.Animator.SetInteger(animationContext.Value.LayerIndex, animationContext.Value.UpdatedValue);
        animationContext?.Animator.SetTrigger(animationContext.Value.AnimationTrigger);

        spineContext?.SkeletonGraphic.AnimationState.SetAnimation(0,
            spineContext.Value.LayerIndexName + spineContext.Value.UpdatedValue, false);
    }

    public override void Animate(Coords coords, TileLayerViewAnims anim,
        TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None)
    {
        switch (anim)
        {
            case TileLayerViewAnims.CustomAppear:
                _renderer.PlayAppear();
                break;

            case TileLayerViewAnims.Preview:
                _renderer.Content.UpdateRectForTile(_renderer.TileHelpMinOffset, _renderer.TileHelpMaxOffset);
                _renderer.Show();
                break;

            case TileLayerViewAnims.TapFeedback:
                _renderer.PlayTapFeedback(this);
                break;
        }
    }
}