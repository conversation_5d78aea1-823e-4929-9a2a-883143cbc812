// Settings for match 3 part:
using System;
using System.Collections.Generic;
using BBB;
using BBB.Match3;
using GameAssets.Scripts.Match3.Logic.Tiles;
using PBLevel;
using Grid = BBB.Grid;

public static class TileFactory
{
    public static Tile CreateTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind = TileKinds.None, List<TileParam> tileParams = null)
    {
        return CreateTileByAsset(id, tileAsset, tileOrigin, tileKind, tileParams);
    }
    
    public static Tile CreateTile(Grid grid, PBTile pbTile)
    {
        if (pbTile == null)
            return null;
        
        var id = grid.TilesSpawnedCount++;
        var tileAsset = (TileAsset) pbTile.Asset;
        var tileKind = (TileKinds)pbTile.Kind;
        var tileState = TileMapper.UnwrapMods((TileState)pbTile.Mod, forSerialization: true);
        var tileParams = pbTile.TileParams.ToParams(); 
        var tile = CreateTileByAsset(id, tileAsset, new TileOrigin(), tileKind, tileParams);
        tile.Add(tileState);
        return tile;
    }

    private static Tile CreateTileByAsset(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind,
        List<TileParam> tileParams)
    {
        switch (tileAsset)
        {
            case TileAsset.Undefined:
            case TileAsset.Simple:
                return new SimpleTile(id, TileAsset.Simple, tileOrigin, tileKind, tileParams);
            
            case TileAsset.BlinkingTile:
                return new BlinkingTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Propeller:
                return new PropellerTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.RowBreaker:
                return new RowBreakerTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.ColumnBreaker:
                return new ColumnBreakerTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Bomb:
                return new BombTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.ColorBomb:
                return new ColorBombTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Sand:
                return new SandTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.DropItem:
                return new DropItemTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Litter:
                return new LitterTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Pinata:
                return new PinataTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Frame:
                return new FrameTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.ColorCrate:
                return new ColorCrateTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Watermelon:
                return new WatermelonTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.MoneyBag:
                return new MoneyBagTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Penguin:
                return new PenguinTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Egg:
                return new EggTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Bird:
                return new BirdTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Sheep:
                return new SheepTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Banana:
                return new BananaTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Monkey:
                return new MonkeyTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Skunk:
                return new SkunkTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Hen:
                return new HenTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Chicken:
                return new ChickenTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Bee:
                return new BeeTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Mole:
                return new MoleTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Squid:
                return new SquidTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Toad:
                return new ToadTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.TukTuk:
                return new TukTukTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Hive:
                return new HiveTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.MagicHat:
                return new MagicHatTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Gondola:
                return new GondolaTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Bowling:
                return new BowlingTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Bush:
                return new BushTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Soda:
                return new SodaTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Safe:
                return new SafeTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.FlowerPot:
                return new FlowerPotTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.IceBar:
                return new IceBarTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.DynamiteBox:
                return new DynamiteBoxTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.GiantPinata:
                return new GiantPinataTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.MetalBar:
                return new MetalBarTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Shelf:
                return new ShelfTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.JellyFish:
                return new JellyFishTIle(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.GoldenScarab:
                return new GoldenScarabTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.Sticker:
                return new StickerTile(id, tileAsset, tileOrigin, tileKind, tileParams);
            
            case TileAsset.FireWorks:
                return new FireWorksTile(id, tileAsset, tileOrigin, tileKind, tileParams);
            
            case TileAsset.SlotMachine:
                return new SlotMachineTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            case TileAsset.BigMonkey:
                return new BigMonkeyTile(id, tileAsset, tileOrigin, tileKind, tileParams);

            default:
                return new Tile(id, tileAsset, tileOrigin, tileKind, tileParams);

        }
    }
    
    public static Tile CreateUnspecifiedTile(int id, TileOrigin origin, TileAsset asset, TileState mods, TileKinds kind, TileParam[] tileParams)
    {
        var result = CreateTile(id, asset, origin, kind, new List<TileParam>(tileParams ?? Array.Empty<TileParam>()));
        
        if (mods != TileState.None)
        {
            for (var i = 0; i < sizeof(TileState) * 8; i++)
            {
                var mod = (TileState)((ulong)1 << i);
                if ((mods & mod) != 0)
                {
                    result.Add(mod);
                }
            }
        }
        
        result.AddMandatoryParamsTile();
        return result;
    }
}