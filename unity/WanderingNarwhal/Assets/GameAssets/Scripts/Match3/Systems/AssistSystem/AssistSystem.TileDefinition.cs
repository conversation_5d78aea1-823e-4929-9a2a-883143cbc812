using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Match3.Debug;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.AssistSystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using BBB.Match3.Systems.GoalsService;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public partial class AssistSystem
    {
        private static bool _shouldResetCoolDowns;
        private const int BruteDefinitionAttempts = 100;
        private readonly TileKindsBag _tileKindsBag = new ();
        private readonly Dictionary<TileKinds, int> _groupedTileKindCountDict = new ();
        private readonly Dictionary<TileKinds, float> _shuffleUsedKindWeights = new ();
        private Dictionary<TileKinds, int> _tileKindCountCache = new ();

        /// <summary>
        /// the purpose of tile definition is to assign colors to undefined tiles
        /// in a way that the number of automatches and possible moves will be controlled by assist system
        /// </summary>
        public void DefineTiles(Grid grid, IRootSimulationHandler handler, SimulationInputParams inputParams,
            AssistParams assistParams, GoalsSystem goalsSystem, int iteration)
        {
            //if it is an initial loop (meaning simulation which runs as the very first before any player input to the board)
            //then use standard brute define and do not do any assist system perks
            if (inputParams.InitialLoop)
            {
                _shouldResetCoolDowns = true;
                
                BruteDefine(grid, inputParams.UsedKinds, handler, true);
                return;
            }

            if (assistParams != null)
            {
                if (_shouldResetCoolDowns)
                {
                    _shouldResetCoolDowns = false;
                    SetupAssistCooldowns(assistParams);
                }
                
                DefineByAssistParams(grid, handler, inputParams, goalsSystem, assistParams, iteration);
                return;
            }
            
            BruteDefine(grid, inputParams.UsedKinds, handler, false);
            TryApplyAutoMatches(grid, handler);
        }

        private void BruteDefine(Grid grid, List<TileKinds> usedKinds, IRootSimulationHandler simHandler, bool possibleMovesRequired)
        {
            var success = false;
            var loopDetector = 0;

            //brute define loops and generates grids until certain grid will satisfy the condition of having no
            //matches and having at least 3 tiles of the same kind (so that there would be at least one match after certain number of shuffles)
            while (!success)
            {
                var gridClone = grid.DefinitionAttemptClone();
                StandardDefine(gridClone, usedKinds);

                _groupedTileKindCountDict.Clear();
                GroupTileKindsCount(gridClone.Cells, _groupedTileKindCountDict);
                success = SearchMatchesSystem.AreNoMatches(gridClone) && _groupedTileKindCountDict.Count > 0;

                if (success)
                {
                    var maxCount = int.MinValue;
    
                    foreach (var count in _groupedTileKindCountDict.Values)
                    {
                        if (count > maxCount)
                        {
                            maxCount = count;
                        }
                    }
    
                    success = maxCount > 2;
                }

                if (possibleMovesRequired)
                {
                    success &= SearchMatchesSystem.SearchForAnyPossibleMove(gridClone);
                }

                _groupedTileKindCountDict.Clear();
                if (success)
                {
                    //if everything fine, copy definitions
                    CopyDefinitions(gridClone, grid, simHandler);
                }

                if (loopDetector >= BruteDefinitionAttempts)
                {
#if BBB_LOG
                    M3Debug.LogWarning("Could not find definition without matches");
#endif
                    CopyDefinitions(gridClone, grid, simHandler);
                    return;
                }

                loopDetector++;
            }
            
#if BBB_LOG
            if (loopDetector > 1)
            {
                M3Debug.LogWarning("BruteDefine found in loops " + loopDetector);
            }
#endif
        }

        private bool ShuffleDefine(Grid grid, Dictionary<TileKinds, int> groupedTileKindsCount, IRootSimulationHandler simHandler)
        {
            ApplyShuffleUsedKinds(groupedTileKindsCount);
            
#if BBB_LOG
            var undefinedCount = 0;
#endif

            foreach (var cell in grid.Cells)
            {
                if (cell.Tile is not { Kind: TileKinds.Undefined }) continue;
#if BBB_LOG
                undefinedCount++;
#endif
                StandardDefineTile(cell, grid, _shuffleUsedKindWeights, simHandler);
                var kind = cell.Tile.Kind;
                if (kind == TileKinds.Error)
                {
                    BDebug.LogError(LogCat.Match3, "Error kind is found in " + cell.Coords);
                    return false;
                }

                if (!groupedTileKindsCount.TryGetValue(kind, out var kindCound))
                {
                    var str = "ShuffleDefine: " + kind + " not found in ";
                    foreach (var key in groupedTileKindsCount.Keys)
                    {
                        const string space = " ";
                        str += key + space;
                    }

                    throw new Exception(str);
                }

                var value = kindCound - 1;

                if (value <= 0)
                {
                    groupedTileKindsCount.Remove(kind);
                    ApplyShuffleUsedKinds(groupedTileKindsCount);
                }
                else
                {
                    groupedTileKindsCount[kind] = value;
                }
            }

#if BBB_LOG
            if (groupedTileKindsCount.Count > 0)
            {
                M3Debug.LogError("Extra kinds left! Undefined count " + undefinedCount);
            }
#endif

            return true;
        }

        private void ApplyShuffleUsedKinds(Dictionary<TileKinds, int> groupedTileKindsCount)
        {
            _tileKindsBag.SetupUsed(new List<TileKinds>(groupedTileKindsCount.Keys));
            _shuffleUsedKindWeights.Clear();

            foreach (var kvp in groupedTileKindsCount)
            {
                if (kvp.Value > 0)
                {
                    _shuffleUsedKindWeights.Add(kvp.Key, 1f);
                }
            }
        }

        private void StandardDefine(Grid grid, List<TileKinds> usedKinds, AssistParams assistParams = null)
        {
            _tileKindsBag.SetupUsed(usedKinds);
            foreach (var cell in grid.Cells)
            {
                if (!ReferenceEquals(cell.Tile, null) && cell.Tile.Kind == TileKinds.Undefined)
                {
                    StandardDefineTile(cell, grid, null, null, assistParams);
                }

                if (cell.TntCount > 0 && (cell.TntKind == TileKinds.Undefined || cell.TntKind == TileKinds.None))
                {
                    cell.TntKind = usedKinds.DeterministicRandomInSelf();
                }
            }
        }

        private void StandardDefineTile(Cell cell, Grid grid, Dictionary<TileKinds, float> weights,
            IRootSimulationHandler simHandler, AssistParams assistParams = null)
        {
            _tileKindsBag.Reset();
            RemoveTileKindsWithMatch(grid, cell.Coords);
            
            if (_tileKindsBag.IsEmpty()) //empty, use any kind
            {
                _tileKindsBag.Reset();
            }

            TileKinds tileKind;
            if (assistParams != null)
            {
                var diff = assistParams.WinLossValueDiff();

                // If player if barely losing, use tile definition based on density (which should be less helpful than
                // equally distributed weights)
                var threshold = AssistParams.AimAtWinningLevel
                    ? assistParams.AssistSystemConfig.AimingToWin.Value.UseDensityDefThreshold
                    : assistParams.AssistSystemConfig.AimingToLose.Value.UseDensityDefThreshold;

                if (diff >= threshold)
                {
                    CalculateDensity(cell, grid, ref _tileKindCountCache);
                    tileKind = _tileKindsBag.GetRandomKindByDensity(_tileKindCountCache);
                }
                else
                {
                    tileKind = _tileKindsBag.GetWeightedRandomKind(weights);
                }
            }
            else
            {
                tileKind = _tileKindsBag.GetWeightedRandomKind(weights);
            }
            
            cell.Tile.SetKind(tileKind, grid, cell);
            simHandler?.OnTileKindDefined(cell.Tile.Id, tileKind);
        }

        private void CalculateDensity(Cell cell, Grid grid, ref Dictionary<TileKinds, int> tileKindDensity)
        {
            tileKindDensity.Clear();

            void AddToTileDensity(Cell _cell, Dictionary<TileKinds, int> _tileKindDensity)
            {
                if (_cell == null || !_cell.HasTile()) return;
                
                var tileKinds = _cell.Tile.Kind;
                if (!tileKinds.IsColored()) return;
                
                if (!_tileKindDensity.ContainsKey(tileKinds))
                    _tileKindDensity[tileKinds] = 0;
                _tileKindDensity[tileKinds] += 1;
            }

            const int radius = 1;
            for (var i = cell.Coords.X-radius; i <= cell.Coords.X+radius; i++)
            {
                for (var j = cell.Coords.Y - (radius*2); j <= cell.Coords.Y; j++)
                {
                    if(grid.TryGetCell(new Coords(i, j), out var tempCell))
                        AddToTileDensity(tempCell, tileKindDensity);
                }
            }
        }

        private bool OfSameMatchableKind(Cell firstCell, Cell secondCell, out TileKinds kind)
        {
            kind = TileKinds.None;

            if (IsNonMatchable(firstCell))
                return false;

            if (IsNonMatchable(secondCell))
                return false;

            if (firstCell.Tile.Kind == secondCell.Tile.Kind)
            {
                kind = firstCell.Tile.Kind;
                return true;
            }

            return false;
        }

        private bool IsNonMatchable(Cell cell)
        {
            return cell == null || !cell.IsBaseCellMatchable() || !cell.Tile.IsSpecialMatchable() || cell.Tile.Kind == TileKinds.Undefined;
        }
        
        private void MinimumDefine(Grid grid, List<TileKinds> usedKinds)
        {
            _tileKindsBag.SetupUsed(usedKinds);
            foreach (var cell in grid.Cells)
            {
                if (!ReferenceEquals(cell.Tile, null) && cell.Tile.Kind == TileKinds.Undefined)
                {
                    _tileKindsBag.Reset();
                    RemoveTileKindsWithMatch(grid, cell.Coords);
                    RemoveTileKindsWithMove(grid, cell.Coords, usedKinds);
                    if (_tileKindsBag.IsEmpty())
                    {
                        _tileKindsBag.Reset();
                        RemoveTileKindsWithMatch(grid, cell.Coords);
                    }
                    if (_tileKindsBag.IsEmpty()) //still empty, use any kind
                    {
                        _tileKindsBag.Reset();
                    }

                    CalculateDensity(cell, grid, ref _tileKindCountCache);
                    TileKinds tileKind = _tileKindsBag.GetRandomKindByDensity(_tileKindCountCache);

                    cell.Tile.SetKind(tileKind, grid, cell);
                }

                if (cell.TntCount > 0 && (cell.TntKind == TileKinds.Undefined || cell.TntKind == TileKinds.None))
                {
                    cell.TntKind = usedKinds.DeterministicRandomInSelf();
                }
            }
        }
        
        private void RemoveTileKindsWithMove(Grid grid, Coords coords, List<TileKinds> usedKinds)
        {
            //Remove tile kinds that can make move
            Cell cell = grid.GetCell(coords);
            for (int i = _tileKindsBag.Count - 1; i >= 0; --i)
            {
                TileKinds currentKind = _tileKindsBag[i];
                cell.Tile.SetKind(currentKind, grid, cell);
                foreach (var otherCell in grid.GetAdjacentCells(coords))
                {
                    if (SearchMatchesSystem.TryFindPossibleMoveBetweenCells(grid, cell, otherCell,
                            out PossibleMove move, true)
                        && move.Type == PossibleMoveType.Simple)
                    {
                        _tileKindsBag.Remove(currentKind);
                        break;
                    }
                }
            }

            //reset tile kind back to Undefined
            cell.Tile.SetKind(TileKinds.Undefined, grid, cell);
        }
        
        private void RemoveTileKindsWithMatch(Grid grid, Coords coords)
        {
            var leftByTwo = grid.GetCell(coords + new Coords(-2, 0));
            var leftByOne = grid.GetCell(coords + new Coords(-1, 0));
            var rightByOne = grid.GetCell(coords + new Coords(1, 0));
            var rightByTwo = grid.GetCell(coords + new Coords(2, 0));
            
            var bottomByTwo = grid.GetCell(coords + new Coords(0, -2));
            var bottomByOne = grid.GetCell(coords + new Coords(0, -1));
            var topByOne = grid.GetCell(coords + new Coords(0, 1));
            var topByTwo = grid.GetCell(coords + new Coords(0, 2));

            //Remove tile kinds that will make match
            if (OfSameMatchableKind(leftByTwo, leftByOne, out TileKinds matchLeftKind))
                _tileKindsBag.Remove(matchLeftKind);

            if (OfSameMatchableKind(rightByOne, leftByOne, out TileKinds matchCenterHorizontalKind))
                _tileKindsBag.Remove(matchCenterHorizontalKind);

            if (OfSameMatchableKind(rightByOne, rightByTwo, out TileKinds matchRightKind))
                _tileKindsBag.Remove(matchRightKind);

            if (OfSameMatchableKind(bottomByOne, topByOne, out TileKinds matchCenterVerticalKind))
                _tileKindsBag.Remove(matchCenterVerticalKind);

            if (OfSameMatchableKind(bottomByTwo, bottomByOne, out TileKinds matchBottomKind))
                _tileKindsBag.Remove(matchBottomKind);

            if (OfSameMatchableKind(topByOne, topByTwo, out TileKinds matchTopKind))
                _tileKindsBag.Remove(matchTopKind);
        }
    }
}
