using System;
using System.Collections.Generic;
using BBB.CellTypes;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public partial class AssistSystem
    {
        private class PossiblePut
        {
            private struct AutoMatch
            {
                public Match[] Matches;
                public int AssistMarkersCount;
            }
            
            public IEnumerable<Tuple<Coords, TileKinds>> TileChanges => _tileChanges;

            public int AssistMarkersCount
            {
                get
                {
                    var result = 0;
                    foreach (var autoMatch in _consequenceAutoMatches)
                    {
                        result += autoMatch.AssistMarkersCount;
                    }

                    return result;
                }
            }

            public int TotalLength
            {
                get
                {
                    var totalLength = 0;

                    foreach (var consequenceAutoMatch in _consequenceAutoMatches)
                    {
                        foreach (var match in consequenceAutoMatch.Matches)
                        {
                            totalLength += match.Length;
                        }
                    }

                    return totalLength;
                }
            }
            
            private readonly List<Tuple<Coords, TileKinds>> _tileChanges = new ();
            private readonly List<AutoMatch> _consequenceAutoMatches = new ();
            private readonly HashSet<GoalType> _consequences = new ();
            private readonly HashSet<Coords> _dropItemPathCache = new ();
            private HashSet<Coords> _adjacentCoords = new ();
            private Dictionary<Coords, Path> _cachedPath = new ();

            public void AddTileChange(Coords coords, TileKinds kind)
            {
                _tileChanges.Add(new Tuple<Coords, TileKinds>(coords, kind));
            }

            public void AddAutoMatch(TileKinds kind, int assistMarkersCount, Match[] matches)
            {
                var autoMatch = new AutoMatch
                {
                    Matches = matches,
                    AssistMarkersCount = assistMarkersCount
                };
                
                _consequenceAutoMatches.Add(autoMatch);    
            }

            public void ApplyToGrid(Grid grid)
            {
                foreach (var autoMatch in _consequenceAutoMatches)
                {
                    foreach (var match in autoMatch.Matches)
                    {
                        foreach (var coord in match.GetAllCoords())
                        {
                            if(grid.TryGetCell(coord, out var cell))
                            {
                                cell.Tile?.Add(TileState.AutomatchProduct);
                            }
                        }
                    }
                }
            }

            public void AnalyzeConsequences(Grid grid)
            {
                _dropItemPathCache.Clear();
                var tntKinds = new bool[8]; //None(0) - White(7)
                var tuktukKinds = new bool[8]; //None(0) - White(7)
                
                foreach (var cell in grid.Cells)
                {
                    if (cell.Tile != null && cell.Tile.IsAnyOf(TileState.DropItem))
                    {
                        RoutingCellSystem.CollectCoordsInCellPath(grid, cell, _dropItemPathCache, ref _cachedPath);
                    }

                    if (cell.IsAnyOf(CellState.Tnt) && cell.TntCount > 0)
                    {
                        tntKinds[(int)cell.TntKind] = true;
                    }
                    
                    var mainCell = cell.GetMainCellReference(out _);
                    if (mainCell.HasTile() && mainCell.Tile.Speciality == TileSpeciality.TukTuk)
                    {
                        tuktukKinds[mainCell.Tile.GetParam(TileParamEnum.TukTukColor)] = true;
                    }
                }

                foreach (var autoMatch in _consequenceAutoMatches)
                {
                    foreach (var match in autoMatch.Matches)
                    {
                        _consequences.Add(GoalType.Score);
                        _consequences.Add(match.Kind.ToGoalType());

                        foreach (var coord in match.GetAllCoords())
                        {
                            if (!grid.TryGetCell(coord, out var cell)) continue;
                            
                            if (cell.BackgroundCount > 0)
                            {
                                if (cell.IsBackState())
                                {
                                    AddConsequence(GoalType.Backgrounds);
                                }
                                else if (cell.IsPetal())
                                {
                                    AddConsequence(GoalType.Petal);
                                }
                            }

                            if (cell.IvyCount > 0)
                            {
                                if (cell.IsAnyOf(CellState.Ivy))
                                {
                                    AddConsequence(GoalType.Ivy);
                                }
                            }
                                
                            if (cell.HasDestructibleWall())
                            {
                                if (cell.IsAnyOf(CellState.DestructibleWall))
                                {
                                    AddConsequence(GoalType.DestructibleWall);
                                }
                            }

                            if (!ReferenceEquals(cell.Tile, null))
                            {
                                if (cell.Tile.IsAnyOf(TileState.IceCubeMod))
                                {
                                    AddConsequence(GoalType.IceCubes);
                                }
                                else if (cell.Tile.IsAnyOf(TileState.VaseMod))
                                {
                                    AddConsequence(GoalType.Vase);
                                }
                                else if (cell.Tile.IsAnyOf(TileState.SandMod))
                                {
                                    AddConsequence(GoalType.Sand);
                                }

                                if (match.Kind > 0 && tntKinds[(int)match.Kind])
                                {
                                    AddConsequence(GoalType.Tnt);
                                }
                            }

                            if (_dropItemPathCache.Contains(cell.Coords))
                            {
                                AddConsequence(GoalType.DropItems);
                            }
                        }

                        if (match.MatchType == MatchType.Square)
                        {
                            AddConsequence(GoalType.StrictGridGoal);
                            AddConsequence(GoalType.GridBasedTypes);
                        }
                        else
                        {
                            if (match.Length >= 4)
                            {
                                AddConsequence(GoalType.Pinata);
                            }
                            _adjacentCoords.Clear();
                            match.GetMatchAdjacentCoords(ref _adjacentCoords);
                            foreach (var coord in _adjacentCoords)
                            {
                                var overCoord = new Coords(coord.X, coord.Y + 1);
                                if (grid.TryGetCell(overCoord, out var overCell))
                                {
                                    AddAdjacentConequences(overCell);
                                }
                                var underCoord = new Coords(coord.X, coord.Y - 1);
                                if (grid.TryGetCell(underCoord, out var underCell))
                                {
                                    AddAdjacentConequences(underCell);
                                }
                            }
                        }
                    }
                }
            }

            private void AddAdjacentConequences(Cell cell)
            {
                if (ReferenceEquals(cell.Tile, null)) return;
                
                switch (cell.Tile.Speciality)
                {
                    case TileSpeciality.Litter:
                        AddConsequence(GoalType.Litters);
                        break;
                    case TileSpeciality.Sticker:
                        AddConsequence(GoalType.Stickers);
                        break;
                    case TileSpeciality.ColorCrate:
                        AddConsequence(GoalType.ColorCrate);
                        break;
                    case TileSpeciality.Watermelon:
                        AddConsequence(GoalType.Watermelon);
                        break;
                }

                if (cell.Tile.IsAnyOf(TileState.AnimalMod))
                {
                    AddConsequence(GoalType.Animal);
                }
            }

            private void AddConsequence(GoalType goalType)
            {
                _consequences.Add(goalType);
            }


            public void Clear()
            {
                _tileChanges.Clear();
                _consequences.Clear();
                _consequenceAutoMatches.Clear();
            }

            public float Score(Dictionary<GoalType, float> goalValueWeights)
            {
                if (goalValueWeights == null)
                    return TotalLength;
                
                var result = HasBoosters() ? TotalLength : 0f;
                foreach (var (goal, score) in goalValueWeights)
                {
                    foreach (var consequence in _consequences)
                    {
                        if ((goal & consequence) == goal && score > result)
                        {
                            result = score;
                        }
                    }
                }

                return result;
            }

            public bool HasBoosters()
            {
                foreach (var consequenceAutoMatch in _consequenceAutoMatches)
                {
                    foreach (var match in consequenceAutoMatch.Matches)
                    {
                        if (match.Length >= 4)
                        {
                            return true;
                        }
                    }
                }
                return false;
            }

            public bool Overlaps(PossiblePut otherPut)
            {
                foreach (var ownChange in _tileChanges)
                {
                    foreach (var otherChange in otherPut._tileChanges)
                    {
                        if (ownChange.Item1.Equals(otherChange.Item1))
                            return true;
                    }
                }

                return false;
            }

            public bool IsUnitDistanceFrom(Grid grid, PossiblePut otherPut, bool excludeSimpleMatch = true)
            {
                foreach (var autoMatch in _consequenceAutoMatches)
                {
                    foreach (var match in autoMatch.Matches)
                    {
                        if (excludeSimpleMatch && match.Length == 3)
                            continue;
                        
                        var spawnCoord = match.GetSpawnCoord(grid);
                        if (otherPut.IsUnitDistanceFrom(grid, spawnCoord))
                            return true;
                    }
                }

                return false;
            }
            
            public bool IsUnitDistanceFrom(Grid grid, Coords otherCoord, bool excludeSimpleMatch = true)
            {
                foreach (var automatch in _consequenceAutoMatches)
                {
                    foreach (var match in automatch.Matches)
                    {
                        if (excludeSimpleMatch && match.Length == 3)
                            continue;
                        
                        var spawnCoord = match.GetSpawnCoord(grid);

                        if (Mathf.Abs(spawnCoord.X - otherCoord.X) <= 1)
                            return true;
                    }
                }

                return false;
            }
        }

        private class PossiblePutsPool
        {
            private readonly Stack<PossiblePut> _internalStack = new Stack<PossiblePut>();

            public PossiblePutsPool(int count)
            {
                for(var i = 0; i < count; i++)
                {
                    var put = new PossiblePut();
                    _internalStack.Push(put);
                }
            }

            public PossiblePut Spawn() 
            {
                if (_internalStack.Count <= 0) return null;
                
                var result = _internalStack.Pop();
                return result;

            }

            public void Release(PossiblePut put)
            {
                put.Clear();
                _internalStack.Push(put);
            }
        }
    }
}