using BBB.Audio;
using BebopBee.Core;
using BebopBee.Core.Audio;
using BBB.MMVibrations.Plugins;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionItemShovel : Match3ActionBase
    {
        private const float ShovelTime = 0.75f;

        private readonly Coords _targetCoords;

        public ActionItemShovel(Coords targetCoords)
        {
            _targetCoords = targetCoords;

            AffectedCoords.Add(_targetCoords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var message = proxy.EventDispatcher.GetMessage<BoosterUsedEvent>();
            message.Set(BoosterItem.Shovel);
            proxy.EventDispatcher.TriggerEvent(message);

            proxy.FXRenderer.SpawnShovel(_targetCoords);
            AudioProxy.PlaySound(Match3SoundIds.ShovelStart);
            WaitingForCompletion = true;
            Rx.Invoke(ShovelTime, PlayHapticAction);

            void PlayHapticAction(long _)
            {
                proxy.Vibrations.PlayHaptic(ImpactPreset.MediumImpact);
                AudioProxy.PlaySound(Match3SoundIds.ShovelHit);
                ReleasedCoords.Add(_targetCoords);
                WaitingForCompletion = false;
            }
        }
    }
}
