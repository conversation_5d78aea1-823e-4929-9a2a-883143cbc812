using BBB.Match3.Systems.CreateSimulationSystems.Interfaces;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionScoreText : Match3ActionBase
    {

        public bool AssignRandomColor { get; }
        public Coords Coords { get; }
        public float ComboMultiplier { get; }
        public int Score { get; set; }
        public int Backgrounds { get; set; }
        public TileKinds TileKind { get; }

        public bool ShouldRenderScoresOnBoard { get; set; } 

        public ActionScoreText(IGoalAction groupOwner, float comboMult)
        {
            Coords = groupOwner.DisplayCoords;
            ComboMultiplier = comboMult;
            AssignRandomColor = groupOwner.AssignRandomColor;

            TileKind = groupOwner.TileKind;
            Score = groupOwner.Score;
            Backgrounds = groupOwner.Backgrounds;
            ShouldRenderScoresOnBoard = groupOwner.ShouldRenderScoresOnBoard;
            
            AffectedCoords.Add(Coords);
        }

        protected override string GetMembersString()
        {
            return $"coords = {Coords}";
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            if(ShouldRenderScoresOnBoard)
                proxy.ScoreRenderer.SpawnScore(this);
            
            proxy.GoalPanel.OnScoreChanged(Score);
            
            ReleasedCoords.Add(Coords);
        }
    }
}