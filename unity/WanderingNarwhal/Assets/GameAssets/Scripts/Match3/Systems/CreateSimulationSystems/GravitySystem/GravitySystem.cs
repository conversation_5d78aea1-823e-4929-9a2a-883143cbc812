using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using BBB.Wallet;
using BebopBee;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;
using UniRx;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public partial class GravitySystem : IRootSimulationHandler
    {
        private const string ReplayData = "Replay Data";

        public GravitySystem(SimulationInputParams inputParams, GoalsSystem goalSystem, TileResourceSelector tileResources, IAssistParamsProvider assistParamsProvider, ILevelAnalyticsReporter levelAnalyticsReporter)
        {
            _settleTileSystem = new SettleTilesSystem();
            _assistSystem = new AssistSystem();
            _debugSystem = new M3DebugInfoSystem();
            _goalSystem = goalSystem;
            _tileResources = tileResources;
            _inputParams = inputParams;
            _assistParamsProvider = assistParamsProvider;
            _levelAnalyticsReporter = levelAnalyticsReporter;
        }

        private readonly SimulationInputParams _inputParams;
        private readonly SettleTilesSystem _settleTileSystem;
        private readonly AssistSystem _assistSystem;
        private readonly M3DebugInfoSystem _debugSystem;

        private PopSystem _popSystem;
        private M3Settings _settings;
        private AssistParams _assistParams;
        private GoalsSystem _goalSystem;
        private TileResourceSelector _tileResources;
        private IAssistParamsProvider _assistParamsProvider;
        private ILevelAnalyticsReporter _levelAnalyticsReporter;

        private readonly TileHitReactionHandler _reactionHandler = new();
        private readonly Dictionary<Coords, Path> _cellsAndDirections = new();
        private MechanicTargetingManager _mechanicTargetingManager;

        private int _currentTurn;
        private int _matchesCount;
        private int _almostEmptyRemoveAllTilesCooldown = 3;
        private int _tilesCountAtStartOfTurn;
        private M3SpawnSystem _spawnSystem;
        public readonly FixedSizeQueue<int> _lastSettledCoordinatesQueue = new FixedSizeQueue<int>(50);

#if UNITY_EDITOR
        public Queue DamageQueueDEBUG => Queue;
        public Dictionary<Coords, Path> CellsAndDirectionsDEBUG;
#endif

        private static readonly Queue<EndGameActionBase> TempEndGameActionQueueCache = new(50);
        private HashSet<Coords> _coordsCache = new HashSet<Coords>();

        public int CurrentTurn => _currentTurn;

        public void ResetCurrentTurn()
        {
            _currentTurn = 0;
        }

        private void IncrementCurrentTurn(int steps = 1)
        {
            _goalSystem.PushGeneratedVisualActions(AddAction);
            _currentTurn += steps;
        }

        // List of cells and tiles that needed to be damaged:
        private readonly Queue Queue = new Queue();
        private GameSimulation _simulation;
        private List<AutoBoostInstance> _currentButlerBoosters = new();

        public GameSimulation CreateSimulationSync(Grid grid, IPlayerInput playerInput, int remainingMoves, AssistParams assistParams, M3SpawnSystem spawnSystem)
        {
            var result = CreateSimulationInternal(new GameSimulation(playerInput), _inputParams, grid, _goalSystem, _tileResources, playerInput, remainingMoves, assistParams, spawnSystem, false);
            return result;
        }

        public GameSimulation CreateSimulationAsync(Grid grid, IPlayerInput playerInput, int remainingMoves, AssistParams assistParams, M3SpawnSystem spawnSystem)
        {
            return CreateSimulationInternal(new GameSimulation(playerInput), _inputParams, grid, _goalSystem, _tileResources, playerInput, remainingMoves, assistParams, spawnSystem, true);
        }

        private GameSimulation CreateSimulationInternal(
            GameSimulation simulation,
            SimulationInputParams inputParams,
            Grid grid,
            GoalsSystem goalsSystem,
            TileResourceSelector tileResources,
            IPlayerInput playerInput,
            int remainingMoves,
            AssistParams assistParams,
            M3SpawnSystem spawnSystem,
            bool isAsync)
        {
#if M3_QUEUE_DEBUG
            PopQueueMonitor.RegisterCurrentPlayerInputType(playerInput.Type);
#endif

#if UNITY_EDITOR
            M3Editor.IntegrationTests.IntegrationTestTracker.RegisterPlayerInput(playerInput, out var isShouldSkipAssist);
            if (isShouldSkipAssist)
            {
                assistParams = null;
            }
#endif
            _spawnSystem = spawnSystem;
            _simulation = simulation;

            var initialLoop = playerInput.Type == PlayerInputType.None;
            _inputParams.InitialLoop = initialLoop;
            Init(inputParams, assistParams, grid, goalsSystem, tileResources);
            spawnSystem.IsPreStartSpawnStage = initialLoop;
            if (!isAsync)
            {
                goalsSystem.StartNewTurn();
            }

            var resultPlayerInput = true;
            switch (playerInput.Type)
            {
                case PlayerInputType.None or PlayerInputType.Empty:
                    break;

                case PlayerInputType.Tap:
                    var tap = (PlayerInputSingleTap)playerInput;
#if M3_QUEUE_DEBUG
                    PopQueueMonitor.RegisterNewSimulationProcessInvoked("Tap");
#endif
                    resultPlayerInput = TriggerBoosterAtCoordIfPossible(inputParams, grid, tap.TargetCoords);
                    break;
                case PlayerInputType.DTap:
                    var playerInputDoubleTap = (PlayerInputDoubleTap)playerInput;
#if M3_QUEUE_DEBUG
                    PopQueueMonitor.RegisterNewSimulationProcessInvoked("DTap");
#endif
                    resultPlayerInput =
                        TriggerBoosterAtCoordIfPossible(inputParams, grid, playerInputDoubleTap.TargetCoords);
                    break;

                case PlayerInputType.Swap:
                    var playerInputSwap = (PlayerInputSwap)playerInput;
#if M3_QUEUE_DEBUG
                    PopQueueMonitor.RegisterNewSimulationProcessInvoked("Swap");
#endif
                    resultPlayerInput = SwapTilesIfPossible(grid, _reactionHandler, inputParams, goalsSystem, tileResources, playerInputSwap.CoordsPair);
                    break;

                case PlayerInputType.Item:
                    var playerInputItem = (PlayerInputItemBase)playerInput;
#if M3_QUEUE_DEBUG
                    PopQueueMonitor.RegisterNewSimulationProcessInvoked("ApplyItem-" + playerInputItem.Item);
#endif
                    switch (playerInputItem.Item)
                    {
                        case BoosterItem.None:
                            throw new Exception("[GravitySystem]".Paint() + " Used None item type!!!");

                        case BoosterItem.Reshuffle:
                            resultPlayerInput = _assistSystem.Shuffle(_simulation, grid, this, true);
                            break;

                        case BoosterItem.Shovel:
                            resultPlayerInput = UseShovel(grid, (PlayerInputItemShovel)playerInputItem);
                            break;

                        case BoosterItem.CreateLineBreaker:
                        case BoosterItem.CreateLineBreakerButler:
                            resultPlayerInput = UseCreateLineBreaker(grid, (PlayerInputItemSingleCellWithDirections)playerInputItem);
                            break;

                        case BoosterItem.CreateBomb:
                        case BoosterItem.CreateBombButler:
                            resultPlayerInput = UseCreateBomb(grid, (PlayerInputItemSingleCell)playerInputItem);
                            break;

                        case BoosterItem.CreateColorBomb:
                        case BoosterItem.CreateColorBombButler:
                            resultPlayerInput = UseCreateColorBomb(grid, (PlayerInputItemSingleCell)playerInputItem);
                            break;

                        case BoosterItem.CreatePropeller:
                        case BoosterItem.CreatePropellerButler:
                            resultPlayerInput = UseCreatePropeller(grid, (PlayerInputItemSingleCell)playerInputItem);
                            break;

                        case BoosterItem.Balloon:
                            resultPlayerInput = UseBalloon(grid, (PlayerInputItemBallon)playerInputItem);
                            break;

                        case BoosterItem.Wind:
                            resultPlayerInput = UseWind(grid, (PlayerInputItemWind)playerInputItem);
                            break;

                        case BoosterItem.Rain:
                            resultPlayerInput = UseRain(grid, (PlayerInputItemRain)playerInputItem);
                            break;

                        case BoosterItem.Vertical:
                            resultPlayerInput =
                                UseVerticalBooster(grid, (PlayerInputItemVerticalBooster)playerInputItem);
                            break;

                        case BoosterItem.Horizontal:
                            resultPlayerInput =
                                UseHorizontalBooster(grid, (PlayerInputItemHorizontalBooster)playerInputItem);
                            break;

                        default:
                            throw new ArgumentOutOfRangeException();
                    }

                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            if (resultPlayerInput)
            {
#if M3_QUEUE_DEBUG
                PopQueueMonitor.RegisterNewSimulationProcessInvoked("MainSimulationLoop");
#endif
                var (remainingMovesTemp, simResult) = MainSimulationLoop(grid, inputParams, goalsSystem, tileResources,
                    remainingMoves, isAsync);
                remainingMoves = remainingMovesTemp;
                if (playerInput.Type is PlayerInputType.Swap or PlayerInputType.DTap or PlayerInputType.Tap)
                {
                    PostProcess(grid, _simulation, out var isSandGrow);
                    if (isSandGrow)
                    {
                        if (!SearchMatchesSystem.SearchForAnyPossibleMove(grid))
                        {
#if M3_QUEUE_DEBUG
                            PopQueueMonitor.RegisterNewSimulationProcessInvoked("Second MainSimulationLoop after sand grow");
#endif
                            // After sand grow we must recheck possible moves, and if no found, then redo simulation for shuffle.
                            (remainingMoves, simResult) = MainSimulationLoop(grid, inputParams, goalsSystem,
                                tileResources, remainingMoves, isAsync);
                        }
                    }
                }

                _simulation.Result = simResult;

#if UNITY_EDITOR
                M3Editor.IntegrationTests.IntegrationTestTracker.RegisterM3MoveEnded(grid, goalsSystem, playerInput);
#endif

                if (_matchesCount > 1)
                    AddAction(new ActionAward(_matchesCount));
            }
            else
            {
                _simulation.Result = SimulationResult.Fail;
            }

            if (_simulation.DebugInfo != null)
            {
                _simulation.DebugInfo.MatchesHappened = _matchesCount;
            }

            if (!isAsync)
            {
                Uninit();
            }

            return _simulation;
        }

        public bool ProcessPendingBoosters(SimulationInputParams inputParams, Grid grid)
        {
            if (inputParams.PendingBoosters != null &&
                !inputParams.PendingBoosters.Boosters.IsNullOrEmpty())
            {
                var canPlaceAllExtraBoosters =
                    AutoBoosterInputFactory.CanPlaceAllExtraBoosters(grid,
                        inputParams.PendingBoosters.Boosters.Count);
                if (canPlaceAllExtraBoosters)
                {
                    PostProcessAddPendingBoosters(grid, inputParams.PendingBoosters);
                    return true;
                }
            }

            return false;
        }

        private void PostProcessAddPendingBoosters(Grid grid, ExtraBoostersHelper.PendingBoosters pendingBoosters)
        {
            var boosters = pendingBoosters.Boosters;
            var appropriateCells = AutoBoosterInputFactory.GetAppropriateCells(grid, false, ordered: true);
            if (appropriateCells.Count == 0) return;

            AddAction(new ActionEndTurnCheckpoint());

            //Place all Non-Butler Boosters
            CheckAndAddPendingBooster(boosters, PredicateIsNotButler, appropriateCells, grid, pendingBoosters);

            var foundButler = false;
            foreach (var booster in boosters)
            {
                if (!PredicateIsButler(booster)) continue;

                foundButler = true;
                break;
            }

            if (!foundButler) return;

            //Play Butlers Intro and then Place all Butler Boosters
            _currentButlerBoosters.Clear();
            AddAction(new ActionButlerGiftIntro(appropriateCells, _currentButlerBoosters));
            CheckAndAddPendingBooster(boosters, PredicateIsButler, appropriateCells, grid, pendingBoosters, true, _currentButlerBoosters);
            return;

            bool PredicateIsNotButler(AutoBoostInstance x) => !x.Name.Contains("_butler");
            bool PredicateIsButler(AutoBoostInstance x) => x.Name.Contains("_butler");
        }

        private void CheckAndAddPendingBooster(List<AutoBoostInstance> boosters,
            Func<AutoBoostInstance, bool> predicate, ICollection<Cell> appropriateCells, Grid grid,
            ExtraBoostersHelper.PendingBoosters pendingBoosters, bool isButler = false,
            List<AutoBoostInstance> butlerBoosters = null)
        {
            for (var i = boosters.Count - 1; i >= 0; --i)
            {
                var booster = boosters[i];
                var boosterName = booster.Name;
                if (!predicate(booster)) continue;

                Cell cell = null;

                if (booster.CellPos != null)
                {
                    foreach (var boosterCell in grid.Cells)
                    {
                        if (boosterCell.Coords != booster.CellPos) continue;
                        cell = boosterCell;
                        break;
                    }
                }

                cell ??= appropriateCells.DeterministicRandomInSelf();

                var direction = booster.ExtraInfo != null ? (SimplifiedDirections)booster.ExtraInfo :
                    RandomSystem.Next() > 0.5f ? SimplifiedDirections.Horizontal : SimplifiedDirections.Vertical;

                var success = false;
                object extraInfo = null;

                if (cell == null)
                {
                    continue;
                }

                switch (boosterName)
                {
                    case InventoryBoosters.BombBooster:
                        success = UseCreateBomb(grid, new PlayerInputItemCreateBomb(cell.Coords));
                        break;
                    case "linebreaker"
                        : // This is by intent - do not remove this line until you do understand why it is HERE
                    case InventoryBoosters.LineCrushBooster:
                        success = UseCreateLineBreaker(grid,
                            new PlayerInputItemCreateLineBreaker(cell.Coords, direction));
                        extraInfo = direction;
                        break;
                    case InventoryBoosters.LightningStrikeBooster:
                        success = UseCreateColorBomb(grid, new PlayerInputItemCreateColorBomb(cell.Coords));
                        break;
                    case InventoryBoosters.PropellerBooster:
                        success = UseCreatePropeller(grid, new PlayerInputItemCreatePropeller(cell.Coords));
                        break;
                    //Butler Gift
                    case InventoryBoosters.PropellerBoosterButler:
                        success = UseCreatePropeller(grid, new PlayerInputItemCreatePropellerButler(cell.Coords));
                        break;
                    case InventoryBoosters.BombBoosterButler:
                        success = UseCreateBomb(grid, new PlayerInputItemCreateBombButler(cell.Coords));
                        break;
                    case InventoryBoosters.ColorBombBoosterButler:
                        success = UseCreateColorBomb(grid, new PlayerInputItemCreateColorBombButler(cell.Coords));
                        break;
                    case InventoryBoosters.LineBreakerBoosterButler:
                        success = UseCreateLineBreaker(grid,
                            new PlayerInputItemCreateLineBreakerButler(cell.Coords, direction));
                        extraInfo = direction;
                        break;
                }

                if (success)
                {
                    // Capture butler booster information
                    if (isButler && butlerBoosters != null)
                    {
                        butlerBoosters.Add(new AutoBoostInstance(boosterName, cell.Coords, extraInfo));
                    }

                    AddAction(new ActionPlaceBooster(cell.Coords, i, isButler, boosterName));
                    boosters.Remove(booster);
                    appropriateCells.Remove(cell);
                    Scheduler.MainThreadEndOfFrame.Schedule(() =>
                    {
                        pendingBoosters.BoosterAddedCallback?.Invoke(boosterName, cell.Coords, extraInfo);
                    });
                }
            }
        }

        private System.Tuple<int, SimulationResult> MainSimulationLoop(Grid grid, SimulationInputParams inputParams,
            GoalsSystem goalsSystem, TileResourceSelector tileResourceSelector,
            int remainingMoves, bool isAsync)
        {
            var simResult = SimulationResult.Success;

            //heal tiles
            var loopDetector = 0;
            var endGameActions = TempEndGameActionQueueCache;
            endGameActions.Clear();

            var shouldExitSimulation = false;
            var hasWon = false;

            try
            {
                while (true)
                {
                    // Running gravity:
                    bool result;
                    try
                    {
                        result = RunGravitySimulation(inputParams, grid, _reactionHandler, goalsSystem,
                            remainingMoves, hasWon, isAsync);
                    }
                    catch (InfiniteLoopException exception)
                    {
                        if (inputParams.SimulateLoopException)
                        {
                            UnityEngine.Debug.unityLogger.logEnabled = true;
                            UnityEngine.Debug.LogException(exception);
                            UnityEngine.Debug.unityLogger.logEnabled = false;
                            break;
                        }

                        throw;
                    }

                    //update assist params after running gravity
                    if (_assistParamsProvider != null && _assistParams != null)
                    {
                        var totalMoves = inputParams.TurnsLimit;
                        var originalGoals = goalsSystem.OriginalGoals;
                        var originalAssistState = new AssistState(originalGoals, inputParams.OriginalGrid);
                        var assistProgressAchieved =
                            originalAssistState - new AssistState(goalsSystem.GoalProgressLeft, grid);
                        _assistParams = _assistParamsProvider.GetAssistParams(remainingMoves - 1, totalMoves,
                            assistProgressAchieved, originalAssistState);
                    }

                    // Defining newly spawned tiles:
#if M3_PROFILE
                Profiler.BeginSample("DefineTiles");
#endif
                    _assistSystem.DefineTiles(grid, this, inputParams, _assistParams, goalsSystem, loopDetector);
#if M3_PROFILE
                Profiler.EndSample();
#endif
                    if (!isAsync)
                    {
                        var checkResult = CheckAndProcessNewMatches(grid, inputParams, goalsSystem);

                        if (checkResult)
                        {
                            if (loopDetector++ > M3Constants.LoopDetectorValue)
                            {
                                ThrowSimulationLoopException(false);
                            }

                            continue;
                        }
                    }

                    while (endGameActions.Count > 0)
                    {
                        var action = endGameActions.Peek();
                        action.Execute(this, grid, inputParams, goalsSystem, _settleTileSystem,
                            tileResourceSelector, Mathf.Max(0, remainingMoves));

                        if (action.Completed)
                            endGameActions.Dequeue();

                        if (endGameActions.Count == 0)
                        {
                            remainingMoves = 0;
                            shouldExitSimulation = true;
                        }

                        if (loopDetector++ > M3Constants.EndGameLoopDetectorValue)
                        {
                            ThrowSimulationLoopException(true);
                        }
                    }

                    if (shouldExitSimulation)
                    {
                        if (SearchMatchesSystem.AreAnyBoost(grid))
                        {
                            endGameActions.Enqueue(new EndGameDestroyBoostsAction(playStarsWave: false));
                            continue;
                        }

                        break;
                    }

                    // Checking if we have swaps. If not Reshuffle.
                    if (grid.HasAnyDefinedTile() && !SearchMatchesSystem.SearchForAnyPossibleMove(grid, true))
                    {
                        _simulation.ShuffleCount += 1;
                        var shuffleResult = _assistSystem.Shuffle(_simulation, grid, this);
                        if (!shuffleResult)
                        {
                            if (SearchMatchesSystem.AreAnyBoost(grid))
                            {
                                break;
                            }

                            simResult = SimulationResult.SuccessShuffleLose;
                            return new System.Tuple<int, SimulationResult>(remainingMoves, simResult);
                        }

                        continue;
                    }

                    hasWon = goalsSystem.HasCompletedGoals();

                    if (hasWon)
                    {
                        _goalSystem.OnLevelWin();
                        _spawnSystem.OnLevelWin();
                        _lastSettledCoordinatesQueue.Clear();
                        ClearHatsFromGrid(grid);
                        CalculateSpecialTilesSystem.Clear();
                        simResult = SimulationResult.SuccessWin;

                        AddAction(new ActionEndTurnCheckpoint());
                        endGameActions.Enqueue(new EndGameTellAboutVictoryAction());
                        endGameActions.Enqueue(new EndGameTryUsingSuperBoostAction());
                        endGameActions.Enqueue(new EndGameDestroyBoostsAction(playStarsWave: true));

                        endGameActions.Enqueue(new EndGameCometsAction());

                        if (result)
                        {
                            break;
                        }

                        if (loopDetector++ > M3Constants.EndGameLoopDetectorValue)
                        {
                            ThrowSimulationLoopException(true);
                        }

                        continue;
                    }

#if BBB_DEBUG
                    if (inputParams.IsBatchMode)
                    {
                        _debugSystem.DebugFindUndefinedTilesCheck(grid);
                    }
#endif

                    break;
                }
            }
            catch (EndGameInfiniteLoopException e)
            {
                BDebug.LogWarning(LogCat.Match3, "End Game Infinite Loop Detected: " + e.Message);
            }
            catch (Exception e)
            {
                BDebug.LogError(LogCat.Match3, "GravitySystem exception: " + e.Message);
                throw;
            }

            return new System.Tuple<int, SimulationResult>(remainingMoves, simResult);
        }

        /// <summary>
        /// Stealing monkey hats icons should be removed when bonus time starts,
        /// because they can't be collected.
        /// </summary>
        private void ClearHatsFromGrid(Grid grid)
        {
            foreach (var cell in grid.Cells)
            {
                if (cell.Tile is null) continue;
                if (!cell.Tile.Is(TileState.StealingHatLabel)) continue;

                cell.Tile.Remove(TileState.StealingHatLabel);
                AddAction(new ActionRemoveState(cell.Tile.Id, cell.Coords, TileState.StealingHatLabel,
                    hitWaitParams: null));
            }
        }

        /// <summary>
        /// Post process grid after end of turn by player's swap.
        /// </summary>
        private void PostProcess(Grid grid, GameSimulation simulation, out bool isSandGrow)
        {
            PostProcessHandleCellsRestoreHealth(grid, simulation);

            // Sand logic can also make game lose, but indirectly,
            // if no more moves left on the level after sand growth. -VK
            grid.SandHandler.CheckIfSandWasDestroyedThisTurn(simulation);
            var sandTargetCell = grid.SandHandler.PostProcessHandleGrowSandLogic(grid);
            if (sandTargetCell != null)
            {
                AddAction(new ActionEndTurnCheckpoint());

                // Found proper expansion direction.

                bool isTileAlreadyExist = false;
                if (!(sandTargetCell.Tile is null))
                {
                    isTileAlreadyExist = true;
                    AddAction(new ActionAddModifier(sandTargetCell.Coords, Coords.OutOfGrid,
                        sandTargetCell.Tile, TileState.SandMod));
                    _goalSystem.TryIncreaseGoalIfNeeded(GoalType.Sand);
                }

                grid.SandHandler.GrowSandOnCell(grid, sandTargetCell);
                isSandGrow = true;

                if (!isTileAlreadyExist)
                {
                    // sandTargetCell should contain newly created tile, which is generated in SandHandler.
                    // Add tile (created for cloned grid) to the original grid. -VK
                    AddAction(new ActionAddTile(sandTargetCell.Coords, sandTargetCell.Tile));
                    _goalSystem.TryIncreaseGoalIfNeeded(GoalType.Sand);
                }
            }
            else
            {
                isSandGrow = false;
            }
        }

        private void PostProcessHandleCellsRestoreHealth(Grid grid, GameSimulation simulation)
        {
            bool isAnyAutoHealTile = false;

            bool CheckExistingTilesAction(Cell cell)
            {
                if (cell.Tile.IsNull()) return false;
                if (cell.Tile.HasParam(TileParamEnum.PerTurnAutoHealHpValue))
                {
                    var defaultHp = cell.Tile.GetParam(TileParamEnum.PerTurnAutoHealHpValue);
                    var hp = cell.Tile.GetParam(TileParamEnum.AdjacentHp);
                    if (hp < defaultHp)
                    {
                        isAnyAutoHealTile = true;

                        return true; // break loop.
                    }
                }

                return false;
            }

            grid.ForEachCellWithBreak(CheckExistingTilesAction);

            if (!isAnyAutoHealTile) return;

            var damagedTiles = new List<int>();

            simulation.HasReduceAdjacentHpFor(damagedTiles);

            void RestoreHpAction(Cell cell)
            {
                if (cell.Tile.IsNull()) return;
                if (cell.Tile.HasParam(TileParamEnum.PerTurnAutoHealHpValue)
                    && !damagedTiles.Contains(cell.Tile.Id))
                {
                    var defaultHp = cell.Tile.GetParam(TileParamEnum.PerTurnAutoHealHpValue);
                    cell.Tile.SetParam(TileParamEnum.AdjacentHp, defaultHp);
                    simulation.AddLogicalAction(1,
                        new ActionChangeTileParam(cell.Tile.Id, cell.Coords,
                            new List<(TileParamEnum, int)> { new(TileParamEnum.AdjacentHp, defaultHp) }));
                }
            }

            grid.ForEachCell(RestoreHpAction);
        }

        public void ClearQueue()
        {
            Queue.Clear();
        }

        private void Init(SimulationInputParams inputParams, AssistParams assistParams, Grid grid,
            GoalsSystem goalSystem, TileResourceSelector tileResources)
        {
            _settings = inputParams.Settings;
            _assistParams = assistParams;
            _goalSystem = goalSystem;
            _tileResources = tileResources;

            _spawnSystem.UpdateSpawnerCache(grid, inputParams.InitialLoop);
            ResetCurrentTurn();
            _matchesCount = 0;
            _almostEmptyRemoveAllTilesCooldown = 3;
            grid.RefrehsAllCellsMultisizeCaches();
            _tilesCountAtStartOfTurn = 0;
            foreach (var cell in grid.Cells)
            {
                if (!(cell.Tile is null))
                {
                    _tilesCountAtStartOfTurn++;
                }
            }

            _mechanicTargetingManager ??= new MechanicTargetingManager(_settings.MechanicTargetingConfigs);
            _mechanicTargetingManager.Init(grid, goalSystem);
            
            _popSystem ??= new PopSystem();
            _popSystem.Init(this, goalSystem, _reactionHandler,
                _mechanicTargetingManager, _spawnSystem, _settleTileSystem, _levelAnalyticsReporter);
            SpecialTileSystemLogic.Init(_reactionHandler);
        }

        public void Uninit()
        {
            _assistSystem.CleanDuplicatePutSet();
            _settleTileSystem.Clear();
            _goalSystem.OnEndTurn();
            _assistParams = null;
            _goalSystem = null;
            CalculateSpecialTilesSystem.Uninit();
            SpecialTileSystemLogic.Uninit();
            _mechanicTargetingManager.UnInit();
#if UNITY_EDITOR
            CellsAndDirectionsDEBUG = new Dictionary<Coords, Path>(_cellsAndDirections);
#endif
            _cellsAndDirections.Clear();
        }

        /// <summary>
        /// Main simulation algorithm
        /// </summary>
        private bool RunGravitySimulation(SimulationInputParams inputParams, Grid grid,
            TileHitReactionHandler reactionHandler,
            GoalsSystem goalSystem, int remainingMoves, bool tweakSpecificSpawnersOnWin, bool isAsync)
        {
            int loopDetector = 0;

            bool isTileFrozen = true;
            bool isTileMoved = true;
            bool isTileSettled = true;
            bool isNewTileSpawned = true;
            bool isTilePopped = true;

#if M3_DEBUG
            var gridClone = grid.Clone();
#endif

#if M3_QUEUE_DEBUG
            PopQueueMonitor.RegisterNewGravitySimulationStep();
#endif

            // Restrict cells to the specific spawner:
            RoutingCellSystem.RoutePaths(grid, _cellsAndDirections);

            while (true)
            {
                //YA: Check for reaction cache since we can dynamically add goals, example : flower pot and petals
                if (!inputParams.GoalsCompleted && goalSystem.HasCompletedGoals() && _reactionHandler.IsReactionCacheEmpty)
                {
                    inputParams.GoalsCompleted = true;
                    AddAction(new ActionBonusTimeTitle(remainingMoves));
                }

                var isSimulationFinished = !Queue.HasSomething
                                           && !isTileMoved
                                           && !isTileFrozen
                                           && !isTileSettled
                                           && !isNewTileSpawned
                                           && !isTilePopped;

                if (isSimulationFinished && !Queue.HasEndTilesToBlow && _reactionHandler.IsReactionCacheEmpty)
                {
                    if (_popSystem.HasTargetingHits())
                    {
                        _mechanicTargetingManager.Init(grid, goalSystem);
                        _popSystem.ProcessTargetingHits(grid, _reactionHandler, inputParams, goalSystem, Queue);
                    }
                    else
                    {
#if BBB_DEBUG
                        if (inputParams.IsBatchMode)
                        {
                            _debugSystem.FindStuckColumns(grid);
                        }
#endif
                        if (isAsync)
                        {
                            return _simulation.IsNotEmpty;
                        }

                        break;
                    }
                }

                RoutingCellSystem.RoutePaths(grid, _cellsAndDirections);

                // Reducing damage delay and busy time:
                if (loopDetector > 0)
                    Queue.Shift();

                // Settling tiles:
                _debugSystem.Clear();
#if M3_PROFILE
                    Profiler.BeginSample("SettleOneTurn");
#endif
                isTileSettled = _settleTileSystem.SettleOneTurn(grid, _reactionHandler, _cellsAndDirections, this,
                    out isTileMoved, out isTileFrozen);
#if M3_PROFILE
                    Profiler.EndSample();
#endif

                // Spawning new tiles from spawners:
                isNewTileSpawned = _spawnSystem.TrySpawnNewTilesFromSpawners(this,
                    _reactionHandler, _simulation.DefinedTileKinds, tweakSpecificSpawnersOnWin);

                // Processing new matches:

#if M3_PROFILE
                Profiler.BeginSample("RunGravitySimulation::CheckAndProcessNewMatches");
#endif
                isTilePopped = !isTileFrozen && isTileSettled && !Queue.HasSomething;
                var shouldRunVisualSimulation = isTilePopped;
                if (!isTilePopped)
                {
                    isTilePopped =
                        CheckAndProcessNewMatches(grid, inputParams, goalSystem, filterUndefinedMatch: !isAsync);
                }

#if M3_PROFILE
                Profiler.EndSample();
                Profiler.BeginSample("RunGravitySimulation::HandleReactions");
#endif
                reactionHandler.HandleScheduledReactions(grid, this, inputParams, _goalSystem, _popSystem, Queue,
                    isSimulationFinished);

                reactionHandler.HandleDelayedReactions(grid, this, inputParams, _goalSystem, _popSystem, CurrentTurn);

                reactionHandler.HandleIceBar(grid, this);

                reactionHandler.HandleMetalBar(grid, this);

                if (_reactionHandler.NewBusyCells != null)
                {
                    foreach (var newBusyCell in _reactionHandler.NewBusyCells)
                    {
                        Queue.AddBusyCell(newBusyCell);
                    }

                    _reactionHandler.NewBusyCells.Clear();
                }

#if M3_PROFILE
                Profiler.EndSample();
#endif
                // Blowing up end tiles (example: Blinking tile)
                if (isSimulationFinished && _reactionHandler.IsReactionCacheEmpty)
                {
#if M3_PROFILE
                    Profiler.BeginSample("DamageEndTile");
#endif
                    isTilePopped = _popSystem.DamageEndTileAndCell(grid, _reactionHandler, inputParams, goalSystem,
                        Queue, Queue);
#if M3_PROFILE
                    Profiler.EndSample();
#endif
                }

                // Saving current simulation turn:
                IncrementCurrentTurn();

                if (isAsync && shouldRunVisualSimulation &&
                    !_simulation.HasAnyPendingAction() &&
                    !_popSystem.HasTargetingHits())
                {
                    return true;
                }

                if (loopDetector++ > M3Constants.LoopDetectorValue)
                {
#if M3_DEBUG
                    _debugSystem.SnapshotGrid(inputParams, gridClone, grid);
#endif
                    throw new InfiniteLoopException(
                        "[GravitySystem] Looped in RunGravitySimulation:"
                        + "\n" + grid
                        + "\n" + Queue.ToConsole()
                        + "\nIs Tile Moved " + isTileMoved
                        + "\nIs Tile Frozen " + isTileFrozen
                        + "\nIs Tile Settled " + isTileSettled
                        + "\nIs New Tile Spawned " + isNewTileSpawned
                        + "\nIs Tile Popped " + isTilePopped
                        + "\n");
                }
            } // while (true)

            if (!isAsync)
            {
                _simulation.RemoveLastEmptyTurnsFromLogicalActions();
            }

            return true;
        }

        private bool CheckAndProcessNewMatches(Grid grid, SimulationInputParams inputParams,
            GoalsSystem goalSystem, List<Coords> preferredCoords = null, bool filterUndefinedMatch = false)
        {
            var matches = SearchMatchesSystem.FindAllMatches(grid);

            if (matches != null)
            {
                if (filterUndefinedMatch)
                {
                    matches.RemoveWhere(match =>
                    {
                        _coordsCache.Clear();
                        match.GetMatchAdjacentCoords(ref _coordsCache);
                        foreach (var coord in _coordsCache)
                        {
                            if (grid.TryGetCell(coord, out Cell cell) && cell.Tile != null &&
                                cell.Tile.Kind == TileKinds.Undefined)
                                return true;
                        }

                        return false;
                    });
                }

                _matchesCount += matches.Count;
            }

            return _popSystem.ProcessNewMatches(grid, _reactionHandler, inputParams, goalSystem, Queue, matches,
                preferredCoords, this, _lastSettledCoordinatesQueue);
        }

        public void AddAction(Match3ActionBase newAction)
        {
            _simulation.AddLogicalAction(CurrentTurn, newAction);
        }

        public void AddActionWithDelay(Match3ActionBase newAction, int delay)
        {
            _simulation.AddLogicalAction(CurrentTurn + delay, newAction);
        }

        public void OnTileKindDefined(int tileId, TileKinds kind)
        {
            _simulation.DefinedTileKinds[tileId] = kind;
        }

        public void HandleMovementAction(Tile tile, Coords coords)
        {
            bool wasNotInTransition = tile.IsNoneOf(TileState.InTransition);

            tile.Add(TileState.InTransition);
            if (wasNotInTransition)
            {
                var action = new ActionPerformTileMovement(tile, coords);
                _simulation.AddLogicalAction(CurrentTurn, action);
            }
            else
            {
                _simulation.ModifyActionsSatisfyingCondition(action =>
                        (action is ActionPerformTileMovement transitionAction)
                        && transitionAction.TileId == tile.Id,
                    action =>
                    {
                        var movementAction = (ActionPerformTileMovement)action;
                        movementAction.AppendToPath(coords);
                    });
            }
        }

        public void FinalHandleMovementAction(Tile tile, Coords coords)
        {
            bool wasInTransition = tile.IsAnyOf(TileState.InTransition);
            tile.Remove(TileState.InTransition);
            _lastSettledCoordinatesQueue.Enqueue(tile.Id);

            if (wasInTransition)
            {
                _simulation.ModifyActionsSatisfyingCondition(action =>
                        (action is ActionPerformTileMovement transitionAction)
                        && transitionAction.TileId == tile.Id,
                    action =>
                    {
                        var transitionAction = (ActionPerformTileMovement)action;
                        transitionAction.SetNextSettleCoord(coords);
                        transitionAction.AppendToPath(coords);
                    });
            }
        }

        public void DeferredTargetSelected(int uniqueId, Cell targetCell)
        {
            _simulation.ModifyActionsSatisfyingCondition(action =>
                    (action is IDeferredTargetSelection targetSelectionAction)
                    && targetSelectionAction.UniqueId == uniqueId,
                action =>
                {
                    var targetSelectionAction = (IDeferredTargetSelection)action;
                    targetSelectionAction.SetTargetCell(targetCell);
                }, false);
        }

        public void HandleGondolaMovementAction(Coords startCoords, Coords nextCellCoords, Coords goalCellCoords,
            int tileId, int orientationAngle, bool collectGoal, HitWaitParams hitWaitParams,
            List<(TileParamEnum, int)> tileParamValues)
        {
            if (!_simulation.ModifyActionsSatisfyingCondition(action =>
                        action is ActionMoveGondola actionMoveGondola &&
                        actionMoveGondola.ShouldAppendToPath(hitWaitParams),
                    action =>
                    {
                        var actionMoveGondola = (ActionMoveGondola)action;

                        var isGondolaBreakPoint = false;

                        if (hitWaitParams != null)
                        {
                            isGondolaBreakPoint = _simulation.IsBreakPointForGondola(startCoords, hitWaitParams);
                        }

                        if (actionMoveGondola.TileId != tileId || actionMoveGondola.Path[^1] != startCoords ||
                            isGondolaBreakPoint)
                        {
                            AddAction(new ActionMoveGondola(startCoords, nextCellCoords, goalCellCoords, tileId,
                                orientationAngle, collectGoal, hitWaitParams, isGondolaBreakPoint, tileParamValues));
                        }
                        else
                        {
                            actionMoveGondola.AppendToPath(nextCellCoords, goalCellCoords, orientationAngle,
                                collectGoal, tileParamValues);
                        }
                    }))
            {
                AddAction(new ActionMoveGondola(startCoords, nextCellCoords, goalCellCoords, tileId,
                    orientationAngle, collectGoal, hitWaitParams, false, tileParamValues));
            }
        }

        private void ThrowSimulationLoopException(bool isEndGame)
        {
            var exception = isEndGame
                ? new EndGameInfiniteLoopException("[GravitySystem] Looped in MainSimulationLoop on new matches:\n")
                : new Exception("[GravitySystem] Looped in MainSimulationLoop on new matches:\n");
            exception.Data.Add(ReplayData, _levelAnalyticsReporter.GetReplayData());
            throw exception;
        }
    }
}