using System;
using System.Collections.Generic;
using BBB.Core;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public sealed class SpecialTileCalculationResult : PoolableObject<SpecialTileCalculationResult>, IPoolableObject
    {
        private readonly Dictionary<Coords, Tile> _tilesToSpawn = 
            new Dictionary<Coords, Tile>();

        private readonly Dictionary<Coords, List<Coords>> _boostFormationSourceCoords =
            new Dictionary<Coords, List<Coords>>();

        private readonly HashSet<Coords> _allFormationCoords = 
            new HashSet<Coords>();

        private readonly Dictionary<Match, HashSet<Tile>> _immuneTiles = 
            new Dictionary<Match, HashSet<Tile>>();

        private readonly HashSet<Match> _cornerMatchesHashes = new HashSet<Match>();

        public IEnumerable<Coords> CoordsToSpawn => _tilesToSpawn.Keys;


        private static readonly HashSet<Coords> TempFormationCoordsSet = new HashSet<Coords>();

        private void PrepareFormation(Tile specialTile, params Match[] matches)
        {
            TempFormationCoordsSet.Clear();
            foreach (var match in matches)
            {
                if (_immuneTiles.TryGetValue(match, out var tiles))
                {
                    tiles.Add(specialTile);
                }
                else
                {
                    _immuneTiles.Add(match, new HashSet<Tile> {specialTile});
                }

                if (!specialTile.IsAnyOf(TileState.SupermatchProduct))
                {
                    foreach (var coord in match.GetAllCoords())
                    {
                        TempFormationCoordsSet.Add(coord);
                    }
                }
            }
        }

        public void OnSpawn()
        {
            TempFormationCoordsSet.Clear();
        }

        public void Release()
        {
            _tilesToSpawn.Clear();
            foreach (var list in _boostFormationSourceCoords.Values)
            {
                list.Clear();
            }
            _immuneTiles.Clear();
            _allFormationCoords.Clear();
            _cornerMatchesHashes.Clear();
            
            StaticPool.Push(this);
        }

        public Tile GetOrCreateSpecialTile(Grid grid,
            Coords coords, TileSpeciality possibleNewSpeciality, params Match[] matches)
        {
            int tilesSpawnedCount;
            if (_tilesToSpawn.TryGetValue(coords, out var existingSpeialTile))
            {
                if (existingSpeialTile.Speciality.GetSpawnPriority() > possibleNewSpeciality.GetSpawnPriority())
                {
                    return existingSpeialTile;
                }

                tilesSpawnedCount = grid.TilesSpawnedCount;
            }
            else
            {
                tilesSpawnedCount = grid.TilesSpawnedCount++;
            }

            var tileOrigin = new TileOrigin(Creator.Match, grid.GetCell(coords));
            var tileAsset = possibleNewSpeciality switch
            {
                TileSpeciality.RowBreaker => TileAsset.RowBreaker,
                TileSpeciality.ColumnBreaker => TileAsset.ColumnBreaker,
                TileSpeciality.Bomb => TileAsset.Bomb,
                TileSpeciality.ColorBomb => TileAsset.ColorBomb,
                TileSpeciality.Propeller => TileAsset.Propeller,
                _ => throw new ArgumentOutOfRangeException(nameof(possibleNewSpeciality), possibleNewSpeciality, null)
            };

            var specialTile = TileFactory.CreateTile(tilesSpawnedCount, tileAsset, tileOrigin);

            PrepareFormation(specialTile, matches);

            return specialTile;
        }

        public HashSet<Tile> GetImmuneTilesForMatch(Match match)
        {
            if (_immuneTiles.TryGetValue(match, out var immuneTiles))
                return immuneTiles;

            return null;
        }

        public HashSet<Tile> GetTilesImmuneToSpecial(Grid grid)
        {
            var result = new HashSet<Tile>();
            if (_immuneTiles.Count == 0)
                return result;

            foreach (var kvp in _immuneTiles)
            {
                var match = kvp.Key;
                var tiles = kvp.Value;

                foreach (var coord in match.GetAllCoords())
                    if (grid.TryGetCell(coord, out var cell) && !ReferenceEquals(cell.Tile, null))
                        result.Add(cell.Tile);

                foreach (var immuneTile in tiles)
                {
                    result.Add(immuneTile);
                }
            }

            return result;
        }

        public void AddTileToSpawn(Tile tile, bool shouldVisualizeFormation)
        {
            if (!ReferenceEquals(tile, null))
            {
                var tileCoords = tile.Origin.Cell.Coords;
                if (!_tilesToSpawn.ContainsKey(tileCoords))
                {
                    _tilesToSpawn.Add(tileCoords, tile);

                    if (shouldVisualizeFormation)
                    {
                        if (_boostFormationSourceCoords.TryGetValue(tileCoords, out var list))
                        {
                            list.Clear();
                            list.AddRange(TempFormationCoordsSet);
                        }
                        else
                        {
                            _boostFormationSourceCoords.Add(tileCoords, new List<Coords>(TempFormationCoordsSet));
                        }

                        foreach (var coord in TempFormationCoordsSet)
                            _allFormationCoords.Add(coord);
                    }
                }
            }
        }

        public bool IsEmpty()
        {
            return _tilesToSpawn.Count == 0;
        }

        public Tile GetTileToSpawn(Coords coords)
        {
            return _tilesToSpawn[coords];
        }

        public Coords GetSourceCoordIfAny(Coords coords)
        {
            foreach (var kvp in _boostFormationSourceCoords)
            {
                var targetCoordsList = kvp.Value;
                foreach(var targetCoord in targetCoordsList)
                    if (targetCoord == coords)
                        return kvp.Key;
            }

            return coords;
        }

        public List<Coords> GetFormationCoords(Coords coord)
        {
            if (_boostFormationSourceCoords.TryGetValue(coord, out var list))
                return list;

            return null;
        }

        public bool HashFormationCoord(Coords coords)
        {
            return _allFormationCoords.Contains(coords);
        }

        public void AddCornerMatch(Match match)
        {
            _cornerMatchesHashes.Add(match);
        }

        public bool ContainsCornerMatch(Match match)
        {
            return _cornerMatchesHashes.Contains(match);
        }
    }
}