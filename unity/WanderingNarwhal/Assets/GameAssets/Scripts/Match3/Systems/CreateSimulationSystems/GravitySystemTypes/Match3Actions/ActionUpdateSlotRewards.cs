using GameAssets.Scripts.Match3.Logic.Tiles;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionUpdateSlotRewards : Match3ActionBase
    {
        private readonly SlotMachineRewardType _selectedOutcome;
        
        public override bool ShouldBlockOtherActions()
        {
            return false;
        }
        
        public ActionUpdateSlotRewards(SlotMachineRewardType slotMachineRewardType)
        {
            _selectedOutcome = slotMachineRewardType;
        }
        
        private void ModifyGrid(Grid grid)
        {
            grid.UpdateSlotMachineRewardHolder(_selectedOutcome);
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid);
        }
    }
}