using System.Collections.Generic;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionDoTweenShakeBoard : Match3ActionBase
    {
        private readonly List<Coords> _emptyCellCoords;
        private readonly int _sourceHashCode;
        private readonly ShakeSettingsType _shakeSettingsType;

        public ActionDoTweenShakeBoard(List<Coords> emptyCellCoords, int sourceHashCode,
            ShakeSettingsType shakeSettingsType)
        {
            _emptyCellCoords = emptyCellCoords;
            _sourceHashCode = sourceHashCode;
            _shakeSettingsType = shakeSettingsType;
            foreach (var coord in _emptyCellCoords)
                AffectedCoords.Add(coord);
        }

        public override bool ShouldBlockOtherActions()
        {
            return false;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            proxy.GridController.DoTweenShakeBoard(_sourceHashCode, shakeSettings:_shakeSettingsType);
            foreach (var coord in _emptyCellCoords)
                ReleasedCoords.Add(coord);
        }
    }
}