using System;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes.AdditionalHitInfos;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Logic.Tiles;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public static class TileHitReactionExtensions
    {
        public static int DefaultAdjacentHp(this TileState state)
        {
            return (state & TileState.SheepMod) != 0 ? 3 : 0;
        }
    }

    /// <summary>
    ///     Tile hit reaction logic system.
    /// </summary>
    /// <remarks>
    ///     This system is responsible for:
    ///     - making some tiles 'revive' after getting destroyed
    ///     - allows any type of tile to 'act' when it's getting hit or destroyed
    ///     'Acting' for tile (which is also may be called 'reaction') is one of this:
    ///     - Collect goal for tile,
    ///     - Hit random tile on grid,
    ///     - Spawn special tile in random cell.
    ///     All goals collection is going through this system as well
    ///     (collect goal reaction is default action for all non-special tiles). -VK
    /// </remarks>
    public class TileHitReactionHandler
    {
        private struct TileReactRecord
        {
            public HitWaitParams HitWaitParams;
            public int TileId;
            public Coords TileCoords;
            public TileSpeciality Speciality;
            public TileState State;
            public TileKinds Kind;
            public int? Skin;
            public Coords CoordsOffset;
            public Tile Tile;
        }

        private struct DelayedSpawnRecord
        {
            public Coords From;
            public Tile Tile;
            public TileAsset TileAsset;
            public int Delay;
            public int SourceTileId;
            public GoalType GoalType;
            public Tile SourceTile;
        }

        private readonly List<TileReactRecord> _dieReactionsCache = new(50);
        private readonly List<TileReactRecord> _adjacentHitReactionsCache = new(50);

        private readonly List<Coords> _allToads = new(5);
        private readonly List<Coords> _allBeeHives = new();
        private readonly List<Coords> _allMagicHats = new();
        private readonly List<Coords> _allScarabs = new();
        private readonly Dictionary<Cell, int> _tuktukHits = new();
        private readonly Dictionary<Cell, int> _tntHits = new();

        /// <summary>
        ///     Reactions that must occur after some delay in main simulation loop.
        /// </summary>
        /// <remarks>
        ///     Used for spawn tile-from-another-tile reaction type,
        ///     because when some tile is spawned, it should be placed into grid after all other actions on tile completed (such as
        ///     falling),
        ///     and this can't happen at current simulation step. -VK
        /// </remarks>
        private readonly List<DelayedSpawnRecord> _delayedSpawnReactions = new(20);

        private static List<GoalTypeTagPair> _tempGoals = new(5);

        /// <summary>
        ///     Cached delegates for passing into methods arguments.
        /// </summary>
        private static readonly Func<Cell, bool> IsCellWithRegularTileOrEmptyFunc = IsCellWithRegularTileOrEmpty;

        /// <summary>
        ///     Spawned tiles cumulative state, used to determine if specific mod state was spawned on current step.
        /// </summary>
        private TileState _spawnedTilesOnCurrentStep = TileState.None;

        private int _lastTurn;

        public bool IsReactionCacheEmpty => _dieReactionsCache.Count == 0 && _adjacentHitReactionsCache.Count == 0 &&
                                            _delayedSpawnReactions.Count == 0;

        public List<Cell> NewBusyCells { get; private set; }
        
        private static int _tukTukId;
        private const int MaxOverdueDelay = 50;
        private const int SpawnedTileBusyTime = 2;

        public bool RegisterTileShouldReactOnDie(Grid grid, IRootSimulationHandler events, HitWaitParams hitWaitParams,
            Coords coords, int tileId,
            TileAsset tileAsset, TileSpeciality tileSpeciality, TileState tileState, TileKinds kind, Tile tile = null,
            int? skin = null, Coords coordsOffset = default)
        {
           var isACollectableTile = NotifyCollectorsOnTileDie(grid, events, coords, tileAsset, kind, hitWaitParams);
            _dieReactionsCache.Add(new TileReactRecord
            {
                HitWaitParams = hitWaitParams,
                TileCoords = coords,
                TileId = tileId,
                Speciality = tileSpeciality,
                State = tileState,
                Kind = kind,
                Skin = skin,
                CoordsOffset = coordsOffset,
                Tile = tile,
            });
            return isACollectableTile;
        }

        public void RegisterTileShouldReactOnAdjacentHit(Coords coords, HitWaitParams hitWaitParams, int tileId,
            TileSpeciality tileSpeciality,
            TileState tileState, Tile tile, int? skin = null, Coords coordsOffset = default)
        {
            _adjacentHitReactionsCache.Add(new TileReactRecord
            {
                HitWaitParams = hitWaitParams,
                TileCoords = coords,
                TileId = tileId,
                Speciality = tileSpeciality,
                State = tileState,
                Kind = TileKinds.None,
                Skin = skin,
                CoordsOffset = coordsOffset,
                Tile =  tile
            });
        }

        /// <summary>
        ///     Handle all reactions for cached tiles, which can be executed 'inline' during simulation (it doesn't need to wait
        ///     until the end of sim).
        /// </summary>
        public void HandleScheduledReactions(Grid grid, IRootSimulationHandler events,
            SimulationInputParams inputParams,
            GoalsSystem goalSystem, PopSystem popSystem, Queue queue, bool allowHitReactions)
        {
            HandleOnDieReactions(grid, events, inputParams, goalSystem, popSystem, queue);

            /* Such reactions as Collection of tiles for TNT should be executed after simulation is finished,
             because otherwise target tile may fall down
             before it will get the damage hit from reaction.
             At same time we can't do all reactions delayed after simulation,
             because Collect goal reaction, for example, should never be delayed,
            /// so reactions handling separated into two stages. -VK */

            if (allowHitReactions)
                HandleOnDieHitReactions(grid, events, queue, goalSystem);

            HandleOnAdjacentHitReactions(grid, events, inputParams, goalSystem, popSystem);

            OnAfterHandleStepEnd(grid, events, goalSystem);
        }

        private void HandleTnTDieReaction(Cell cell, Grid grid, IRootSimulationHandler events,
            GoalsSystem goalsSystem)
        {
            if (!cell.IsAnyOf(CellState.Tnt) || cell.TntCount > 0) return;

            MarkSandAsDestroyedIfTntIsDestroyedThisTurn(cell, grid);

            var sizeX = cell.SizeX;
            var sizeY = cell.SizeY;
            
            cell.TntCount = 0;
            cell.Remove(CellState.Tnt);
            cell.SizeX = 0;
            cell.SizeY = 0;
            grid.RefrehsAllCellsMultisizeCaches();
            goalsSystem.TryReduceGoalIfNeeded(GoalType.Tnt);

            NewBusyCells ??= new List<Cell>(1);

            cell.IsBusy += 1;
            NewBusyCells.Add(cell);

            events.AddAction(new ActionReduceTntCount(cell.Coords, cell.Coords, sizeX, sizeY));
        }

        //This prevents the Sand expand on the same turn as TNT is destroyed
        //the Sand should wait at least until next movement to start expanding,
        //as player could’t have possibly interacted with Sand while Sand is hidden under TNT
        private static void MarkSandAsDestroyedIfTntIsDestroyedThisTurn(Cell cell, Grid grid)
        {
            var sizeX = Mathf.Max(1, cell.SizeX);
            var sizeY = Mathf.Max(1, cell.SizeY);

            for (var x = 0; x < sizeX; x++)
            {
                for (var y = 0; y < sizeY; y++)
                {
                    var coords = new Coords(cell.Coords.X + x, cell.Coords.Y + y);
                    if (!grid.TryGetCell(coords, out var affectedCell)) continue;

                    if (!affectedCell.HasTile() || !affectedCell.Tile.IsAnyOf(TileState.SandMod)) continue;
                    grid.SandHandler.MarkSandWasDestroyedThisTurn();
                    return;
                }
            }
        }

        public void HandleDelayedReactions(Grid grid, IRootSimulationHandler events, SimulationInputParams inputParams,
            GoalsSystem goalSystem, PopSystem popSystem, int currentTurn)
        {
            if (currentTurn <= _lastTurn) return;

            _lastTurn = currentTurn;

            HandleDelaySpawnedReactions(grid, events, inputParams, goalSystem, popSystem);
           
        }
        
        public void HandleIceBar(Grid grid, IRootSimulationHandler events)
        {
            foreach (var c in grid.Cells)
            {
                var mainCell = c.GetMainCellReference(out _);
                if (!mainCell.Tile.IsNull() && mainCell.Tile.Speciality == TileSpeciality.IceBar) continue;

                if (!c.IsAnyOf(CellState.IceBar) && !c.IceBarStatus) continue;
                c.IceBarStatus = false;
                c.Remove(CellState.IceBar);
                events.AddAction(new ActionFreeUpIceBarCells(c.Coords));
            }
        }

        public void HandleMetalBar(Grid grid, IRootSimulationHandler events)
        {
            foreach (var c in grid.Cells)
            {
                var mainCell = c.GetMainCellReference(out _);
                if (!mainCell.Tile.IsNull() && mainCell.Tile.Speciality == TileSpeciality.MetalBar) continue;

                if (!c.IsAnyOf(CellState.MetalBar) && !c.MetalBarStatus) continue;
                c.MetalBarStatus = false;
                c.Remove(CellState.MetalBar);
                events.AddAction(new ActionFreeUpMetalBarCells(c.Coords));
            }
        }

        private void HandleDelaySpawnedReactions(Grid grid, IRootSimulationHandler events,
            SimulationInputParams inputParams, GoalsSystem goalSystem, PopSystem popSystem)
        {
            if (_delayedSpawnReactions.Count == 0) return;

            var potentialCellList = grid.GetPrioritizedMatchingCells(IsCellWithRegularTileOrEmptyFunc);

            for (var i = _delayedSpawnReactions.Count - 1; i >= 0; i--)
            {
                var item = _delayedSpawnReactions[i];
                var delay = item.Delay - 1;

                if (delay <= 0)
                {
                    if (potentialCellList.Count > 0)
                    {
                        var potentialCell = potentialCellList[0];
                        potentialCellList.RemoveAt(0);

                        if (TryDoSpawnTileAtPosition(grid, events, inputParams, item.Tile, item.SourceTileId, item.From,
                                item.TileAsset, item.GoalType, goalSystem, potentialCell))
                        {
                            _delayedSpawnReactions.RemoveAt(i);
                            continue;
                        }
                    }

                    if (delay > -MaxOverdueDelay)
                    {
                        item.Delay = delay;
                        _delayedSpawnReactions[i] = item;
                    }
                    else
                    {
                        _delayedSpawnReactions.RemoveAt(i);
                        HandleCollectGoal(grid, goalSystem, popSystem, inputParams, events, null, item.From,
                            item.Tile.Id, item.Tile.Speciality, item.Tile.State, item.Tile.Kind, null);
                    }
                }
                else
                {
                    item.Delay = delay;
                    _delayedSpawnReactions[i] = item;
                }
            }
        }

        private void OnAfterHandleStepEnd(Grid grid, IRootSimulationHandler events, GoalsSystem goalSystem)
        {
            if ((_spawnedTilesOnCurrentStep & TileState.HiveMod) != 0)
                MarkHivesOnGridOutOfBeesIfNeeded(grid, events, goalSystem);

            _spawnedTilesOnCurrentStep = TileState.None;
        }

        private void MarkHivesOnGridOutOfBeesIfNeeded(Grid grid, IRootSimulationHandler events, GoalsSystem goalSystem)
        {
            var isOutOfBees = false;
            var targetLeft = goalSystem.GetLeftGoalCount(GoalType.Bee);

            if (targetLeft > 0)
            {
                var spawnedCount = 0;

                foreach (var m in _delayedSpawnReactions)
                {
                    if (m.Tile.IsAnyOf(TileState.BeeMod))
                    {
                        spawnedCount++;      
                    }
                }

                foreach (var cell in grid.Cells)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.BeeMod))
                    {
                        spawnedCount++;
                    }
                }

                if (spawnedCount >= targetLeft)
                {
                    isOutOfBees = true;
                }
            }
            else
            {
                isOutOfBees = true;
            }

            if (isOutOfBees)
            {
                foreach (var cell in grid.Cells)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.HiveMod))
                    {
                        _allBeeHives.Add(cell.Coords);
                    }
                }
            }
            
            if (_allBeeHives.Count == 0) return;
            NewBusyCells ??= new List<Cell>(_allBeeHives.Count);
            events.AddAction(new ActionSyncCoords(_allBeeHives));

            foreach (var beeHiveCoords in _allBeeHives)
            {
                var cell = grid.GetCell(beeHiveCoords);
                cell.Tile.SetParam(TileParamEnum.BeeHiveOutOfBeesFlag, 1);
                events.AddAction(new ActionChangeTileParam(cell.Tile.Id, cell.Coords,
                    new List<(TileParamEnum, int)> { new(TileParamEnum.BeeHiveOutOfBeesFlag, 1) }));
                NewBusyCells.Add(cell);
            }

            _allBeeHives.Clear();
        }

        private void MarkMagicHatsOnGridOutOfRabbitsIfNeeded(Grid grid, IRootSimulationHandler events,
            GoalsSystem goalSystem)
        {
            var targetLeft = goalSystem.GetLeftGoalCount(GoalType.MagicHat);
            var isOutOfRabbits = targetLeft <= 0;

            if (isOutOfRabbits)
            {
                foreach (var cell in grid.Cells)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.MagicHatMod))
                    {
                        _allMagicHats.Add(cell.Coords);
                    }
                }
            }

            if (_allMagicHats.Count == 0) return;

            NewBusyCells ??= new List<Cell>(_allMagicHats.Count);
            events.AddAction(new ActionSyncCoords(_allMagicHats));

            foreach (var magicHatCoords in _allMagicHats)
            {
                var cell = grid.GetCell(magicHatCoords);
                cell.Tile.SetParam(TileParamEnum.MagicHatOutOfRabbitsFlag, 1);
                events.AddAction(new ActionChangeTileParam(cell.Tile.Id, cell.Coords,
                    new List<(TileParamEnum, int)> { new(TileParamEnum.MagicHatOutOfRabbitsFlag, 1) }));
                NewBusyCells.Add(cell);
            }

            _allMagicHats.Clear();
        }

        private void MarkToadsOnGridOutOfFliesIfNeeded(Grid grid, IRootSimulationHandler events, GoalsSystem goalSystem,
            PopSystem popSystem, SimulationInputParams inputParams)
        {
            var targetLeft = goalSystem.GetLeftGoalCount(GoalType.Toad);
            var isOutOfFlies = targetLeft <= 0;

            if (!isOutOfFlies) return;

            foreach (var cell in grid.Cells)
            {
                if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.ToadMod))
                {
                    _allToads.Add(cell.Coords);
                }
            }

            if (_allToads.Count == 0) return;

            NewBusyCells ??= new List<Cell>(3);

            events.AddAction(new ActionSyncCoords(_allToads));
            foreach (var toadCoords in _allToads)
            {
                var cell = grid.GetCell(toadCoords);
                popSystem.KillTile(grid, this, inputParams, goalSystem, null,
                    null, cell, new HitWaitParams(), false);

                NewBusyCells.Add(cell);
            }

            _allToads.Clear();
        }

        public void RemoveAllScarabs(Grid grid, IRootSimulationHandler events, GoalsSystem goalSystem,
            PopSystem popSystem, SimulationInputParams inputParams)
        {
            var targetLeft = goalSystem.GetLeftGoalCount(GoalType.GoldenScarab);
            var isOutOfScarabs = targetLeft <= 0;

            if (!isOutOfScarabs) return;

            foreach (var cell in grid.Cells)
                if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.GoldenScarabMod))
                    _allScarabs.Add(cell.Coords);

            if (_allScarabs.Count == 0) return;

            NewBusyCells ??= new List<Cell>(_allScarabs.Count);

            events.AddAction(new ActionSyncCoords(_allScarabs));
            foreach (var scarabCoords in _allScarabs)
            {
                var cell = grid.GetCell(scarabCoords);
                popSystem.KillTile(grid, this, inputParams, goalSystem, null,
                    null, cell, new HitWaitParams(), false);

                NewBusyCells.Add(cell);
            }

            _allScarabs.Clear();
        }

        private void HandleOnDieHitReactions(Grid grid, IRootSimulationHandler events, Queue queue, GoalsSystem goalSystem)
        {
            for (var i = _dieReactionsCache.Count - 1; i >= 0; i--)
            {
                var item = _dieReactionsCache[i];
                var from = item.TileCoords;
                if (!grid.TryGetCell(from, out var cell)) continue;

                if (cell.IsAnyOf(CellState.Tnt))
                {
                    HandleTnTDieReaction(cell, grid, events, goalSystem);
                    _dieReactionsCache.RemoveAt(i);
                }

                if (!cell.HasTile() || cell.Tile.Speciality != TileSpeciality.TukTuk) continue;
                HandleTuKTukDieReaction(cell, grid, events, queue, item.HitWaitParams);
                _dieReactionsCache.RemoveAt(i);
            }
        }

        private void HandleOnDieReactions(Grid grid, IRootSimulationHandler events, SimulationInputParams inputParams,
            GoalsSystem goalSystem, PopSystem popSystem, Queue queue)
        {
            for (var i = _dieReactionsCache.Count - 1; i >= 0; i--)
            {
                var item = _dieReactionsCache[i];
                var hitParams = item.HitWaitParams;
                var tileId = item.TileId;
                var from = item.TileCoords;
                var sourceState = item.State;
                var speciality = item.Speciality;
                var kind = item.Kind;

                var isTnt = grid.TryGetCell(from, out var cell) && cell.IsAnyOf(CellState.Tnt);
                if (isTnt) continue;

                var isTukTuk = cell.HasTile() && cell.Tile.Speciality == TileSpeciality.TukTuk;
                if (isTukTuk) continue;

                var reactionType = GetOnDieReactionFromSourceTile(sourceState, speciality);

                HandleReaction(reactionType, grid, events, inputParams, goalSystem, popSystem, from, hitParams, tileId, speciality,
                    item.Tile, sourceState, kind, item.Skin, item.CoordsOffset);

                _dieReactionsCache.RemoveAt(i);
            }
        }

        private void HandleOnAdjacentHitReactions(Grid grid, IRootSimulationHandler events,
            SimulationInputParams inputParams,
            GoalsSystem goalSystem, PopSystem popSystem)
        {
            for (var i = _adjacentHitReactionsCache.Count - 1; i >= 0; i--)
            {
                var item = _adjacentHitReactionsCache[i];
                var hitParams = item.HitWaitParams;
                var tileId = item.TileId;
                var from = item.TileCoords;
                var sourceState = item.State;
                var speciality = item.Speciality;
                var kind = item.Kind;
                var reactionType = GetOnAdjacentHitReactionFromSourceTile(speciality);
                HandleReaction(reactionType, grid, events, inputParams, goalSystem, popSystem, from, hitParams, tileId, speciality,
                    item.Tile, sourceState, kind, item.Skin, item.CoordsOffset);
                _adjacentHitReactionsCache.RemoveAt(i);
            }
        }

        /// <summary>
        ///     Count how many tiles pending collecting goal
        ///     (which means they already destroyed but goal collection phase have not passed yet).
        /// </summary>
        public int GetPendingGoalCollectionReactionsForGoal(GoalType goal)
        {
            var result = 0;
            foreach (var item in _dieReactionsCache)
            {
                var reaction = GetOnDieReactionFromSourceTile(item.State, item.Speciality);
                if (reaction != ReactionType.CollectGoal) continue;
                if (GoalState.IsTileGridBasedGoalRelatedItem(item.Speciality, item.State, item.Kind, goal)) result++;
            }

            return result;
        }

        private void HandleReaction(ReactionType reaction, Grid grid, IRootSimulationHandler events,
            SimulationInputParams inputParams,
            GoalsSystem goalSystem, PopSystem popSystem,
            Coords from, HitWaitParams hitWaitParams, int tileId,TileSpeciality tileSpeciality ,Tile tile, TileState state,
            TileKinds kind, int? skin, Coords coordsOffset)
        {
            switch (reaction)
            {
                case ReactionType.SpawnTileAtRandomPosition:
                {
                    HandleSpawnTileAtRandomPosition(grid, goalSystem, from, tileId, state, tile);
                    break;
                }

                case ReactionType.SpawnSquareTiles:
                {
                    HandleSpawnSquareTile(goalSystem, grid, events, from, tileSpeciality, hitWaitParams);
                    break;
                }

                case ReactionType.CollectGoal:
                {
                    HandleCollectGoal(grid, goalSystem, popSystem, inputParams, events, hitWaitParams, from, tileId,
                        tileSpeciality, state, kind, skin,
                        coordsOffset);
                    break;
                }
            }
        }
        
        
        private static void HandleSpawnSquareTile(GoalsSystem goalsSystem, Grid grid, IRootSimulationHandler events,
            Coords from, TileSpeciality tileSpeciality, HitWaitParams hitWaitParams)
        {
            var cellsToSpawn = GetSpawnSquareData(grid, from, tileSpeciality, out var cellState, out var goalType,
                out var fxType);
            foreach (var variable in cellsToSpawn)
            {
                if (!grid.TryGetCell(variable.coords, out var cell)) continue;
                cell.BackgroundCount = 1;
                cell.Add(cellState);
                goalsSystem.TryAddGoalIfNeeded(goalType);
            }

            var sortedCells = new List<(Coords coords, int position)>(cellsToSpawn);

            sortedCells.Sort((cell1, cell2) => cell1.position.CompareTo(cell2.position));

            var sortedList = new List<Coords>();

            foreach (var cell in sortedCells)
            {
                sortedList.Add(cell.coords);
            }
            
            events.AddAction(new ActionSpawnInCells(from, sortedList, cellState, goalType, fxType, hitWaitParams));
        }

        private static List<(Coords coords, int position)> GetSpawnSquareData(Grid grid, Coords fromCoords, TileSpeciality tileSpeciality,
            out CellState cellState,
            out GoalType goalType, out FxType fxType)
        {
            var positionList = new int[] {};
            var spawnSuitableList = new List<(Coords coords, int position)>();

            var min = new Coords();
            var max = new Coords();

            switch (tileSpeciality)
            {
                case TileSpeciality.FlowerPot:
                {
                    min = new Coords(fromCoords.X - 1, fromCoords.Y - 1);
                    max = new Coords(fromCoords.X + 1, fromCoords.Y + 1);

                    cellState = CellState.Petal;
                    goalType = GoalType.Petal;
                    fxType = FxType.PetalAnticipation;
                    positionList = M3Constants.FlowerPotSpreadPositions;
                    const int positionCount = 9;
                    if (positionList is not { Length: positionCount })
                    {
                        positionList = new int[positionCount];
                        for (var i = 0; i < positionCount; i++)
                        {
                            positionList[i] = i;
                        }
                    }
                    
                    break;
                }
                case TileSpeciality.Bush:
                {
                    min = new Coords(fromCoords.X - 1, fromCoords.Y - 1);
                    max = new Coords(fromCoords.X + 2, fromCoords.Y + 2);

                    cellState = CellState.BackOne;
                    goalType = GoalType.Backgrounds;
                    fxType = FxType.GrassAnticipation;
                    positionList = M3Constants.BushSpreadPositions;
                    
                    const int positionCount = 16;
                    if (positionList is not { Length: positionCount })
                    {
                        positionList = new int[positionCount];
                        for (var i = 0; i < positionCount; i++)
                        {
                            positionList[i] = i;
                        }
                    }
                    
                    break;
                }
                default:
                    cellState = CellState.None;
                    goalType = GoalType.None;
                    fxType = FxType.GrassAnticipation;
                    break;
            }

            var positionIndex = 0;
            
            for (var x = min.X; x <= max.X; x++)
            for (var y = min.Y; y <= max.Y; y++)
            {
                var coords = new Coords(x, y);

                var index = positionList[positionIndex++];
                //checks if coords are within the grid
                if (!grid.TryGetCell(coords, out var cell)) continue;
                if (IsCellSuitableForSpawn(cell)) spawnSuitableList.Add((coords, index));
            }

            return spawnSuitableList;
        }

        /// <summary>
        ///     Handle Spawn triggered reaction.
        /// </summary>
        private void HandleSpawnTileAtRandomPosition(Grid grid, GoalsSystem goalSystem, Coords from, int sourceTileId,
            TileState sourceState, Tile tile)
        {
            if ((sourceState & TileState.HiveMod) != 0)
            {
                if(CanHitBeehive(from, grid, goalSystem) <= 0) return;
            }

            // All spawn reactions must be delayed, since target cell may take some steps to be available
            // Actual add new tile to the grid is happening in TryDoSpawnTileAtPosition.
            _spawnedTilesOnCurrentStep |= sourceState;
            var count = tile.GetParam(TileParamEnum.TileCreateCountForReaction);
            for (var i = 0; i < count; i++)
            {
                // At this stage we don't have exact target cell for spawning, so we assign what default cell[0].
                var originCell = grid.Cells[0];
                var (newTile, tileAsset, goalType) = tile.CreateTileFromReaction(grid.TilesSpawnedCount++, originCell, i);

                if(tileAsset == TileAsset.Undefined) return;
                //Adding delay 1 for tiles with ShouldDelaySimulationOnReaction(SlotMachine and Fireworks) true to prevent targeting falling tiles 
                var delay = tile.ShouldDelaySimulationOnReaction ? 1 : 0;
                _delayedSpawnReactions.Add(new DelayedSpawnRecord
                {
                    From = from,
                    SourceTileId = sourceTileId,
                    Tile = newTile,
                    Delay = delay,
                    TileAsset = tileAsset,
                    GoalType = goalType,
                    SourceTile =  tile,
                });
            }
        }

        /// <summary>
        ///     Spawn delayed tile at the end of wait period.
        /// </summary>
        /// <remarks>May return false if spawn failed due to missing proper cell on grid.</remarks>
        /// <remarks>
        ///     This method sometimes may fail and return false when, for example, Bolt+Bolt combo was used to trigger spawn
        ///     action,
        ///     because in this scenario all cells on grid are marked as 'busy' and can't accept new spawned tile.
        ///     In this case this method will be invoked again on the next cycle of m3 simulation. There is a constant that limits
        ///     max quantity of failed searches in a row. -VK
        /// </remarks>
        private bool TryDoSpawnTileAtPosition(Grid grid, IRootSimulationHandler events,
            SimulationInputParams inputParams, Tile tile, int sourceTileId, Coords from,
            TileAsset tileAsset, GoalType goalType, GoalsSystem goalsSystem, Cell randomTileCell)
        {
            if (randomTileCell == null || !randomTileCell.Tile.IsNull() && (randomTileCell.Tile.State & TileState.InTransition) != 0)
                return false;

            var spawnTileAction =
                new ActionSpawnTileFromAnotherTile(sourceTileId, from, randomTileCell.Coords, tile, tileAsset);
            events.AddAction(spawnTileAction);

            if (randomTileCell.Tile != null)
            {
                if (randomTileCell.Tile.Kind == TileKinds.Undefined)
                    DefineTileKindOnDie(events, randomTileCell.Tile, inputParams.UsedKinds, randomTileCell, grid);

                RegisterTileShouldReactOnDie(grid, events, null, randomTileCell.Coords, randomTileCell.Tile.Id, randomTileCell.Tile.Asset,
                    randomTileCell.Tile.Speciality, randomTileCell.Tile.State, randomTileCell.Tile.Kind, randomTileCell.Tile);
                
                randomTileCell.ReplaceTile(tile);
                events.AddAction(new ActionReplace(randomTileCell.Coords, tile, goalType));
            }
            else
            {
                randomTileCell.AddTile(tile);
                events.AddAction(new ActionSpawn(randomTileCell.Coords, tile, null, goalType));
            }

            if (goalType != GoalType.None)
            {
                goalsSystem.TryAddGoalIfNeeded(goalType);
            }

            randomTileCell.IsBusy += SpawnedTileBusyTime;

            NewBusyCells ??= new List<Cell>(3);
            NewBusyCells.Add(randomTileCell);

            return true;
        }

        private static void DefineTileKindOnDie(IRootSimulationHandler events, Tile tile, List<TileKinds> usedKinds,
            Cell cell, Grid grid)
        {
            tile.SetKind(usedKinds.DeterministicRandomInSelf(), grid, cell);
            events.OnTileKindDefined(tile.Id, tile.Kind);
        }

        private void HandleCollectGoal(Grid grid, GoalsSystem goalSystem, PopSystem popSystem,
            SimulationInputParams inputParams, IRootSimulationHandler events, HitWaitParams hitWaitParams,
            Coords coords, int tileId,
            TileSpeciality speciality, TileState state, TileKinds kind, int? skin,
            Coords coordsOffset = default)
        {
            _tempGoals.Clear();
            goalSystem.ReduceGoalsOnTileDestroyIfNeeded(speciality, state, kind, ref _tempGoals);
            var isGoalCollected = false;
            foreach (var affectedGoal in _tempGoals)
            {
                if (affectedGoal.GoalType == GoalType.None) continue;

                isGoalCollected = true;
                events.AddAction(new ActionCollectGoal(affectedGoal, coords, tileId, skin, coordsOffset, hitWaitParams,
                    kind));
            }

            if (!isGoalCollected) return;

            if (ShouldTileSkipDestroyAnimOnGoalCollect(speciality, hitWaitParams?.DamageSource ?? DamageSource.None))
                goalSystem.AddTileSkipDestroyAnim(tileId);

            MarkMagicHatsOnGridOutOfRabbitsIfNeeded(grid, events, goalSystem);
            MarkToadsOnGridOutOfFliesIfNeeded(grid, events, goalSystem, popSystem, inputParams);
        }

        private static bool ShouldTileSkipDestroyAnimOnGoalCollect(TileSpeciality speciality, DamageSource damageSource)
        {
            return speciality is TileSpeciality.DropItem or TileSpeciality.Propeller ||
                   damageSource == DamageSource.PropellerCombo;
        }

        public int CanHitBeehive(Coords coords, Grid grid, GoalsSystem goalSystem)
        {
            var beeHiveCell = grid.GetCell(coords);
            
            if (beeHiveCell != null && beeHiveCell.Tile != null && beeHiveCell.Tile.GetParam(TileParamEnum.BeeHiveOutOfBeesFlag) == 1)
            {
                return 0;
            }
                
            var count = goalSystem.GetLeftGoalCount(GoalType.Bee);
            if (count <= 0) return 0;

            foreach (var action in _delayedSpawnReactions)
                if (action.Tile.Speciality == TileSpeciality.Bee)
                    count--;

            foreach (var action in _dieReactionsCache)
                if (action.Speciality == TileSpeciality.Bee)
                    count--;

            foreach (var cell in grid.Cells)
                if (!cell.Tile.IsNull() && cell.Tile.Speciality == TileSpeciality.Bee)
                {
                    count--;
                }
            return count;
        }

        private static bool IsCellWithRegularTileOrEmpty(Cell cell)
        {
            if (cell == null || cell.IsAnyOf(CellState.Ivy | CellState.Water | CellState.FlagEnd) ||
                cell.HasMultiSizeCellReference())
            {
                return false;
            }

            if (cell.Tile.IsNull())
            {
                return cell.CanAcceptTile();
            }

            return cell.Tile.Asset == TileAsset.Simple && cell.Tile.State == TileState.None;
        }

        private static bool IsCellSuitableForSpawn(Cell cell)
        {
            if (cell.IsAnyOf(CellState.BackOne | CellState.BackDouble | CellState.Petal | CellState.Water |
                             CellState.FlagEnd))
                return false;

            cell = cell.GetMainCellReference(out _);

            if (cell.Tile.IsNull()) return true;

            return !cell.Tile.Speciality.GrassSpawnDisallowedAMod();
        }

        private static ReactionType GetOnDieReactionFromSourceTile(TileState sourceTileState,
            TileSpeciality sourceSpeciality)
        {
            if (sourceSpeciality.ShouldSpawnTileAtRandomPositionMod()) return ReactionType.SpawnTileAtRandomPosition;

            if (sourceSpeciality.ShouldSpawnSquareTiles()) return ReactionType.SpawnSquareTiles;

            if ((sourceTileState & TileState.AnimalMod) != 0) return ReactionType.CollectGoal;
            
            if (sourceSpeciality.NoDieReactionMod() || sourceSpeciality == TileSpeciality.Frame)
                return ReactionType.None;

            return ReactionType.CollectGoal;
        }

        private static ReactionType GetOnAdjacentHitReactionFromSourceTile(TileSpeciality sourceTile)
        {
            return sourceTile.AdjacentHitReactionMod() ? ReactionType.CollectGoal : ReactionType.None;
        }

        private enum ReactionType
        {
            None,

            /// <summary>
            ///     Spawn tile and place it in random cell.
            /// </summary>
            SpawnTileAtRandomPosition,

            /// <summary>
            ///     Collect goal from tile, regardless of it's hp state (goal can be collected multiple tiles from single tile).
            /// </summary>
            CollectGoal,

            SpawnSquareTiles,
        }

        public void NotifyCollectorsOnCellOverlayDie(Grid grid, IRootSimulationHandler handler, Coords coords,
            CellState cellState, HitWaitParams hitWaitParams)
        {
            foreach (var cell in grid.Cells)
            {
                if (!cell.IsAnyOf(CellState.Tnt) || cell.TntCount <= 0) continue;

                switch (cell.TntTarget)
                {
                    case TntTargetType.Grass:
                    {
                        if ((cellState & (CellState.BackOne | CellState.BackDouble | CellState.Petal)) == 0) continue;

                        break;
                    }
                    case TntTargetType.Ivy:
                    {
                        if ((cellState & CellState.Ivy) == 0) continue;

                        break;
                    }
                    default:
                        continue;
                }

                cell.TntCount--;
                if (cell.TntCount <= 0)
                    _dieReactionsCache.Add(new TileReactRecord
                    {
                        TileCoords = cell.Coords
                    });

                handler.AddAction(new ActionReduceTntCount(cell.Coords, coords, cell.SizeX, cell.SizeY, hitWaitParams));
            }
        }

        /// <summary>
        ///     Collectors should change state when specific tile dies.
        ///     Example: TNT overlay.
        /// </summary>
        private bool NotifyCollectorsOnTileDie(Grid grid, IRootSimulationHandler events, Coords coords,
            TileAsset tileAsset, TileKinds tileKinds, HitWaitParams hitWaitParams)
        {
            _tuktukHits.Clear();
            _tntHits.Clear();
            
            var isACollectableTile = false;
            
            var sortedCells = new List<Cell>(grid.Cells);

            sortedCells.Sort((cell1, cell2) =>
            {
                var compareY = cell2.Coords.Y.CompareTo(cell1.Coords.Y);
                return compareY == 0 ? cell1.Coords.X.CompareTo(cell2.Coords.X) : compareY;
            });
            
            foreach (var cell in sortedCells)
            {
                if (cell.HasTile() && cell.Tile.Speciality == TileSpeciality.TukTuk
                                   && tileAsset == TileAsset.Simple
                                   && cell.Tile.GetParam(TileParamEnum.TukTukColor) == (int)tileKinds)
                {
                    var count = cell.Tile.GetParam(TileParamEnum.TukTukCount);
                    if(count <= 0) continue;
                    isACollectableTile = true;
                    _tuktukHits.Add(cell, count);
                }

                if (!cell.IsAnyOf(CellState.Tnt) || cell.TntCount <= 0 || !IsTileMatchTargetType(cell.TntTarget, cell.TntKind, tileAsset, tileKinds)) 
                    continue;
                
                isACollectableTile = true;
                _tntHits.Add(cell, cell.TntCount);
            }
            
            if (_tntHits.Count > 0)
            {
                var tntCell = _tntHits.MinBy(kvp => kvp.Value).Key;
                NotifyTnt(events, tntCell, coords, hitWaitParams);
            }
            
            if (_tuktukHits.Count > 0)
            {
                var tuktukCell = _tuktukHits.MinBy(kvp => kvp.Value).Key;
                NotifyTukTuk(events, tuktukCell, coords, hitWaitParams);
            }

            return isACollectableTile;
        }
        
        public static int GetTilesLeftToCollect(Grid grid, TileKinds tileKinds)
        {
            const TileAsset tileAsset = TileAsset.Simple;
            var tntCount = 0;
            var tuktukCount = 0;
            foreach (var cell in grid.Cells)
            {
                if (cell.HasTile() && cell.Tile.Speciality == TileSpeciality.TukTuk
                                   && cell.Tile.GetParam(TileParamEnum.TukTukColor) == (int)tileKinds)
                {
                    var count = cell.Tile.GetParam(TileParamEnum.TukTukCount);
                    tuktukCount += count;
                }

                if (!cell.IsAnyOf(CellState.Tnt) || cell.TntCount <= 0) continue;

                if (!IsTileMatchTargetType(cell.TntTarget, cell.TntKind, tileAsset, tileKinds)) continue;
                tntCount += cell.TntCount;
            }

            return Math.Max(tntCount, tuktukCount);
        }


        public static bool IsTileMatchTargetType(TntTargetType targetType, TileKinds tntKind, TileAsset tileAsset,
            TileKinds tileKind)
        {
            switch (targetType)
            {
                case TntTargetType.Simple:
                    if (tileAsset != TileAsset.Simple) return false;
                    if (tntKind is TileKinds.None or TileKinds.Undefined) return true;

                    return tntKind == tileKind;

                case TntTargetType.BoosterAny:
                    return tileAsset is TileAsset.Bomb or TileAsset.ColorBomb or TileAsset.RowBreaker or TileAsset
                        .ColumnBreaker or TileAsset.Propeller;
                case TntTargetType.BoosterLineBreaker:
                    return tileAsset is TileAsset.RowBreaker or TileAsset.ColumnBreaker;
                case TntTargetType.BoosterBomb:
                    return tileAsset == TileAsset.Bomb;
                case TntTargetType.BoosterBolt:
                    return tileAsset == TileAsset.ColorBomb;
                case TntTargetType.DropItem:
                    return tileAsset == TileAsset.DropItem;
                case TntTargetType.Litter:
                    return tileAsset == TileAsset.Litter;
                case TntTargetType.Pinata:
                    return tileAsset == TileAsset.Pinata;
                case TntTargetType.Sticker:
                    return tileAsset == TileAsset.Sticker;
                case TntTargetType.ColorCrate:
                    return tileAsset == TileAsset.ColorCrate;
                case TntTargetType.Watermelon:
                    return tileAsset == TileAsset.Watermelon;
                case TntTargetType.MoneyBag:
                    return tileAsset == TileAsset.MoneyBag;
                case TntTargetType.Egg:
                    return tileAsset == TileAsset.Egg;
                case TntTargetType.FlowerPot:
                    return tileAsset == TileAsset.FlowerPot;
                case TntTargetType.Bird:
                    return tileAsset == TileAsset.Bird;
                case TntTargetType.Sheep:
                    return tileAsset == TileAsset.Sheep;
                case TntTargetType.Banana:
                    return tileAsset == TileAsset.Banana;
                case TntTargetType.Monkey:
                    return tileAsset == TileAsset.Monkey;
                case TntTargetType.BigMonkey:
                    return tileAsset == TileAsset.BigMonkey;
                case TntTargetType.Hen:
                    return tileAsset == TileAsset.Hen;
                case TntTargetType.Chicken:
                    return tileAsset == TileAsset.Chicken;
                case TntTargetType.Bee:
                    return tileAsset == TileAsset.Bee;
                case TntTargetType.Mole:
                    return tileAsset == TileAsset.Mole;
                case TntTargetType.Squid:
                    return tileAsset == TileAsset.Squid;
            }

            return false;
        }

        private void NotifyTnt(IRootSimulationHandler events, Cell tntCell, Coords sourceCoords, HitWaitParams hitWaitParams)
        {
            tntCell.TntCount--;
            if (tntCell.TntCount <= 0)
            {
                _dieReactionsCache.Add(new TileReactRecord
                {
                    TileCoords = tntCell.Coords,
                    HitWaitParams = hitWaitParams
                });
            }
            events.AddAction(new ActionReduceTntCount(tntCell.Coords, sourceCoords, tntCell.SizeX, tntCell.SizeY, hitWaitParams));
        }
        
        private void NotifyTukTuk(IRootSimulationHandler events, Cell tuktuk, Coords sourceCoords, HitWaitParams hitWaitParams)
        {
            var currentCount = tuktuk.Tile.GetParam(TileParamEnum.TukTukCount);
            if(currentCount <= 0) return;
            var reducedCount = --currentCount;
            tuktuk.Tile.SetParam(TileParamEnum.TukTukCount, reducedCount);

            if (reducedCount <= 0)
            {
                _dieReactionsCache.Add(new TileReactRecord
                {
                    TileCoords = tuktuk.Coords
                });
            }
            events.AddAction(new ActionReduceTukTukCount(tuktuk.Coords, sourceCoords, hitWaitParams));
        }

        private void HandleTuKTukDieReaction(Cell cell, Grid grid, IRootSimulationHandler events, 
            Queue queue, HitWaitParams hitWaitParams)
        {
            if (!cell.HasTile() || cell.Tile.Speciality != TileSpeciality.TukTuk) return;
            var currentCount = cell.Tile.GetParam(TileParamEnum.TukTukCount);
            if (currentCount > 0) return;

            var currentColor = cell.Tile.GetParam(TileParamEnum.TukTukColor);

            var currentDirection = cell.Tile.GetParam(TileParamEnum.TukTukOrientation);

            var cardinalDirection = currentDirection switch
            {
                0 => CardinalDirections.E,
                90 => CardinalDirections.N,
                270 => CardinalDirections.S,
                180 => CardinalDirections.W,
                _ => CardinalDirections.E
            };

            NewBusyCells ??= new List<Cell>();

            var allCoords = CardinalDirectionsHelper.GetCoordsInDirection(cell.Coords.X, cell.Coords.Y, cardinalDirection, grid);
            _tukTukId++;
            
            events.AddAction(new ActionMoveTukTuk(cell.Coords, currentColor, cardinalDirection, _tukTukId, allCoords, hitWaitParams));
               
            foreach (var coords in allCoords)
            {
                if (!grid.TryGetCell(coords, out var damageCell)) continue;
                    
                var damageMainCell = damageCell.GetMainCellReference(out _);
                
                var currentHp = 0;

                if (damageMainCell.HasTile())
                {
                    // Skip other TukTuk tiles in the path
                    if (cell != damageMainCell && damageMainCell.HasTile() &&
                        damageMainCell.Tile.Speciality == TileSpeciality.TukTuk)
                    {
                        continue;
                    }
                    
                    currentHp = damageMainCell.Tile.GetParam(TileParamEnum.AdjacentHp);

                    var isArmouredTile = damageMainCell.Tile.HasAnyArmor();
                    if (isArmouredTile)
                    {
                        foreach (var tileParam in Tile.ArmorParams)
                        {
                            if (damageMainCell.Tile.HasParam(tileParam))
                            {
                                currentHp += damageMainCell.Tile.GetParam(tileParam);
                            }

                            if (tileParam == TileParamEnum.VaseLayerCount)
                            {
                                currentHp++;
                            }
                        }
                    }

                    if (damageMainCell.Tile.HasParam(TileParamEnum.BowlingOpened) &&
                        damageMainCell.Tile.GetParam(TileParamEnum.BowlingOpened) == 0)
                    {
                        currentHp++;
                    }

                    if (damageMainCell.Tile.IsBoost)
                    {
                        currentHp++;
                    }

                    if (damageMainCell.Tile.Speciality is TileSpeciality.Monkey or TileSpeciality.BigMonkey)
                    {
                        currentHp += damageMainCell.Tile.GetParam(TileParamEnum.RestoresCount);
                    }

                    if (damageMainCell.Tile.Speciality == TileSpeciality.Squid)
                    {
                        currentHp += damageMainCell.Tile.GetParam(TileParamEnum.SquidsCount);
                    }

                    if (currentHp == 0 && damageMainCell.Tile.Speciality != TileSpeciality.DropItem)
                    {
                        currentHp++;
                    }
                }

                if (damageMainCell.IvyCount > 0)
                {
                    currentHp += damageMainCell.IvyCount;
                }

                if (damageMainCell.BackgroundCount > 0)
                {
                    currentHp += damageMainCell.BackgroundCount;
                }

                var hitSourceUid = 0;
                
                for (var i = 0; i < currentHp; i++)
                {
                    var hit = new Hit<Cell>(
                        null,
                        damageMainCell,
                        1,
                        DamageSource.TukTuk,
                        coords: damageMainCell.Coords,
                        boostInfo: new TukTukInfo(_tukTukId, cardinalDirection),
                        hitSourceUid: ++hitSourceUid);
                    queue.AppendHit(1, hit);
                    NewBusyCells.Add(damageMainCell);
                }

                if (damageCell == damageMainCell) continue;
                if(damageMainCell.HasTile() && damageMainCell.Tile.Speciality.ShouldSpawnSquareTiles()) continue;
                {
                    var damageCellHp = 0;

                    if (damageCell.IvyCount > 0)
                    {
                        damageCellHp += damageCell.IvyCount;
                    }

                    if (damageCell.BackgroundCount > 0)
                    {
                        damageCellHp += damageCell.BackgroundCount;
                    }

                    var damageCellHitSourceId = 0;
                    for (var i = 0; i < damageCellHp; i++)
                    {
                        var hit = new Hit<Cell>(
                            null,
                            damageCell,
                            1,
                            DamageSource.TukTuk,
                            coords: damageCell.Coords,
                            boostInfo: new TukTukInfo(_tukTukId, cardinalDirection),
                            hitSourceUid: ++damageCellHitSourceId);
                        queue.AppendHit(2, hit);
                        NewBusyCells.Add(damageCell);
                    }
                }
            }
        }
    }
}