using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.GoalsService;
using BebopBee.Core;
using DG.Tweening;
using BBB.MMVibrations.Plugins;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionAnimateMatches : Match3ActionBase
    {
        public struct BoostConvergeEvent
        {
            public Coords TargetCoords;
            public Coords[] SourceCoords;

            public override string ToString()
            {
                return $"{string.Join(",", SourceCoords)}=>{TargetCoords}";
            }
        }

        private readonly List<BoostConvergeEvent> _boostConvergeEvents;
        private readonly HashSet<Coords> _simpleMatchCoords = new ();
        private readonly HashSet<Coords> _flyingCoords = new ();
        private readonly HashSet<Coords> _adjacentCoords = new ();

        private readonly List<Tweener> _tweeners = new ();
        private readonly bool _inputMatches;

        public ActionAnimateMatches(Grid grid, List<BoostConvergeEvent> boostConvergeEvents, HashSet<Coords> formationCoordsFilter,
            ICollection<Match> matches, GoalsSystem goalsSystem, TileHitReactionHandler tileHitReactionHandler, bool inputMatches)
        {
            _boostConvergeEvents = boostConvergeEvents;
            _inputMatches = inputMatches;
            
            if (matches != null)
            {
                foreach (var match in matches)
                {
                    var tilesLeftToFly = goalsSystem.GetTilesLeftToFly(match.Kind);
                    var tilesLeftToCollect = TileHitReactionHandler.GetTilesLeftToCollect(grid, match.Kind);
                    var leftToFly = Math.Max(tilesLeftToFly, tilesLeftToCollect);

                    var allCoords = match.GetAllCoords();
                    IEnumerable<Coords> matchCoords;

                    if (tilesLeftToCollect > 0)
                    {
                        matchCoords = allCoords;
                    }
                    else
                    {
                        var reversedCoords = new List<Coords>();

                        foreach (var coord in allCoords)
                        {
                            reversedCoords.Insert(0, coord);
                        }

                        matchCoords = reversedCoords;
                    }
                    
                    var index = 0;
                    foreach (var coord in matchCoords)
                    {
                        if (formationCoordsFilter != null && formationCoordsFilter.Contains(coord))
                            continue;

                        if (index >= leftToFly)
                        {
                            _simpleMatchCoords.Add(coord);
                        }
                        else
                        {
                            _flyingCoords.Add(coord);
                        }

                        index++;
                    }
                    
                    // TODO: Should only block adjacent tiles that are affected by adjacent damage, so we don't block
                    // other tiles when we don't need to
                    match.GetMatchAdjacentCoords(ref _adjacentCoords);
                }
            }

            AffectedCoords.UnionWith(_simpleMatchCoords);
            AffectedCoords.UnionWith(_flyingCoords);

            if (_boostConvergeEvents != null)
            {
                foreach (var convergeEvent in _boostConvergeEvents)
                {
                    var sourceCoords = convergeEvent.SourceCoords;
                    foreach (var sourceCoord in sourceCoords)
                    {
                        AffectedCoords.Add(sourceCoord);
                    }
                }   
            }

            var affectedCoordsArray = new Coords[AffectedCoords.Count];
            var i = 0;

            foreach (var coord in AffectedCoords)
            {
                affectedCoordsArray[i++] = coord;
            }

            WaitCondition = new NotFrozenWaitCondition(affectedCoordsArray);
            
            _adjacentCoords.RemoveWhere(coords => AffectedCoords.Contains(coords));
            AffectedCoords.UnionWith(_adjacentCoords);
        }

        protected override bool CanExecute(Grid grid, PlaySimulationActionProxy proxy)
        {
            var hasFutureTargetLocks = false;
            foreach (var coord in AffectedCoords)
            {
                hasFutureTargetLocks |= proxy.TileTickPlayer.BoardObjectFactory.AreThereFutureTargetLocks(coord, Priority);
            }
            return base.CanExecute(grid, proxy) && !hasFutureTargetLocks;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var m3BoardSettings = proxy.Settings;
            
            var convergeTweenParams = m3BoardSettings.GetMatchSetting(_inputMatches, true);
            var tileShrinkParams = m3BoardSettings.GetMatchSetting(_inputMatches, false);
           

            if (_boostConvergeEvents != null)
            {
                _tweeners.Clear();
                var delayedReleasedCoords = new HashSet<Coords>();
                foreach (var convergeEvent in _boostConvergeEvents)
                {
                    var sourceCoords = convergeEvent.SourceCoords;
                    var sortOrderIncrement = sourceCoords.Length - 1; 

                    var targetCoord = convergeEvent.TargetCoords;

                    foreach (var sourceCoord in sourceCoords)
                    {
                        if (sourceCoord == targetCoord)
                        {
                            delayedReleasedCoords.Add(sourceCoord);
                            continue;
                        }
                        
                        if (grid.TryGetCell(sourceCoord, out var cell) && ShouldCellParticipateInFormation(cell))
                        {
                            var view = proxy.TileController.GetTileViewByCoord(sourceCoord, false);
                            
                            if (view == null) continue;
                            
                            view.AddToSortOrder(sortOrderIncrement);

                            // Move tile to target before destroying view
                            proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(convergeTweenParams.BusyTime, sourceCoord.ToUnityVector2());
                            var tweener = view.TweenLocalPosition(startCoord:sourceCoord, targetCoords: targetCoord, 
                                duration: convergeTweenParams.Duration,
                                curve: convergeTweenParams.MergeCurve);
                                
                            var preDestroyTweener = new PreDestroyTweener
                            {
                                Tweener = tweener,
                                OnTweenerComplete = () =>
                                {
                                    try
                                    {
                                        OnConvergeMoveDone((view, sourceCoord, targetCoord));
                                    }
                                    catch (Exception e)
                                    {
                                        BDebug.LogError(LogCat.General,
                                            "Exception: " + e.Message + "Stack: " + e.StackTrace);
                                    }
                                }
                            };
                            proxy.TileController.VisualizeTileDestruction(cell, TileLayerViewAnimParams.None | TileLayerViewAnimParams.SkipDestroy, preDestroyTweener);
                            _tweeners.Add(tweener);
                        }
                        else
                        {
                            delayedReleasedCoords.Add(sourceCoord);
                        }
                    }
                }

                if (_tweeners.Count > 0)
                {
                    Rx.Invoke(convergeTweenParams.Duration, _ =>
                    {
                        foreach (var tweener in _tweeners)
                        {
                            tweener.Kill(true);
                        }

                        ReleasedCoords.UnionWith(delayedReleasedCoords);
                        // foreach (var convergeEvent in _boostConvergeEvents)
                        // {
                        //     var sourceCoords = convergeEvent.SourceCoords;
                        //     foreach (var coord in sourceCoords)
                        //     {
                        //         proxy.Vibrations.EnqueueHaptic(ImpactPreset.MediumImpact, coord.GetHashCode());
                        //     }
                        // }
                        
                        _tweeners.Clear();
                    });
                }
                else
                {
                    ReleasedCoords.UnionWith(delayedReleasedCoords);
                }
            }

            if (_simpleMatchCoords != null)
            {
                foreach (var coord in _simpleMatchCoords)
                {
                    if (grid.TryGetCell(coord, out var cell) && ShouldCellParticipateInFormation(cell))
                    {
                        var view = proxy.TileController.GetTileViewByCoord(coord, false);
                        if (view != null)
                        {
                            // Do regular shrink+destroy animation before destroying view
                            
                            proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(
                                tileShrinkParams.BusyTime, coord.ToUnityVector2());
                            var tweener = view.ShrinkScaleTo(tileShrinkParams.Duration, tileShrinkParams.TargetScale, tileShrinkParams.ScaleCurve);
                            proxy.TileController.VisualizeTileDestruction(cell, TileLayerViewAnimParams.None, new PreDestroyTweener
                            {
                                Tweener = tweener,
                                OnTweenerComplete = () =>
                                {
                                    OnMoveDone(view, coord);
                                }
                            });
                        }
                    }
                    else
                    {
                        ReleasedCoords.Add(coord);
                    }
                }
            }

            ReleasedCoords.UnionWith(_flyingCoords);
            ReleasedCoords.UnionWith(_adjacentCoords);

            void OnConvergeMoveDone((TileView view, Coords sourceCoord, Coords targetCoord) viewCoordTuple)
            {
                viewCoordTuple.view.RemoveFromSortOrder();
                OnMoveDone(viewCoordTuple.view, viewCoordTuple.sourceCoord);
            }
            
            void OnMoveDone(TileView tileView, Coords coords)
            {
                ReleasedCoords.Add(coords);
                tileView.Hide();
            }
        }

        /// <summary>
        /// If tile is not going to die after the match has happened (for example, in the case 
        /// where it's under chain or ice), then it should not be part of the formation animation
        /// </summary>
        private static bool ShouldCellParticipateInFormation(Cell cell)
        {
            if (!ReferenceEquals(cell.Tile, null))
            {
                if (!MatchHelper.WillTileDieIfHit(cell.Tile))
                    return false;
            }

            return true;
        }
        
        protected override string GetMembersString()
        {
            return $"flyingCoords={string.Join(",", _flyingCoords)} simpleMatchCoords={string.Join(",", _simpleMatchCoords)} boostConvergeEvents={string.Join(",", _boostConvergeEvents)} adjacentCoords={string.Join(",", _adjacentCoords)}";
        }
    }
}