using BebopBee.Core;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionBoltBoltCombine : Match3ActionBase
    {
        private readonly Coords _first;
        private readonly Coords _second;

        public ActionBoltBoltCombine(<PERSON><PERSON><PERSON> first, <PERSON><PERSON><PERSON> second)
        {
            _first = first;
            _second = second;
            
            AffectedCoords.Add(_first);
            AffectedCoords.Add(_second);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var tc = proxy.TileController;
            var firstTileView = tc.GetTileViewByCoord(_first);
            var secondTileView = tc.GetTileViewByCoord(_second);
            proxy.FXRenderer.SpawnBoltBoltCombine(proxy.Settings, _first, firstTileView, secondTileView);

            var time = proxy.Settings.BoltBoltPause;
            Rx.Invoke(time, _ =>
            {
                ReleasedCoords.Add(_first);
                ReleasedCoords.Add(_second);
            });
        }
    }
}