using System;
using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core;
using BebopBee.Core.Audio;
using UniRx;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionLinebreakers : Match3ActionBase
    {
        private Coords _coords;
        private readonly Dictionary<CardinalDirections, int> _distances;
        private readonly int _rocketId;
        private readonly List<Coords> _allCoords;
        private readonly HitWaitParams _hitWaitParams;
        private readonly TileSpeciality _tileSpeciality;

        public ActionLinebreakers(Coords coords, Dictionary<CardinalDirections, int> distances,
            TileSpeciality tileSpeciality, int rocketId, HitWaitParams hitWaitParams)
        {
            _coords = coords;
            _distances = distances;
            _rocketId = rocketId;
            AffectedCoords.Add(_coords);
            _hitWaitParams = hitWaitParams;
            _tileSpeciality = tileSpeciality;
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            List<Action> rocketCreationActions = new List<Action>(_distances.Count);
            
            // Lock all targets for this Rocket
            foreach (var distance in _distances)
            {
                var factory = proxy.TileTickPlayer.BoardObjectFactory;
                var direction = distance.Key;
                
                var workingCoord = _coords;
                var maxDimension = Mathf.Max(Grid.MaxDimensionX, Grid.MaxDimensionY);
                var rocketTargets = new List<RocketTarget>();

                for(int i = 0; i < maxDimension; i++)
                {
                    if (grid.Contains(workingCoord))
                    {
                        if (!(grid.TryGetCell(workingCoord, out var cell) && cell.HasTile() &&
                              cell.Tile.Origin.CreatorType == Creator.ColorBombCombo))
                        {
                            var target = factory.CreateRocketTarget(_rocketId, workingCoord, OnRocketTargetHit);
                            rocketTargets.Add(target);
                        }
                    }

                    workingCoord = workingCoord.GoSingleCardinalDirection(direction);
                }

                rocketCreationActions.Add(() =>
                {
                    factory.CreateRocket(_rocketId, rocketTargets, direction.ToVector2(), _coords.ToUnityVector2());
                });
            }

            var waitUntil = new Func<bool>(() => true);
            
            if (_hitWaitParams != null)
            {
                var waitCondition = ActionWaitConditionFactory.Create(_hitWaitParams);
                waitUntil = () => !waitCondition.WaitForExpectedState(grid, proxy);
            }

            if (waitUntil())
            {
                SpawnRocket();
            }
            else
            {
                // // Do not spawn rocket until waiting condition is met
                MainThreadDispatcher.StartUpdateMicroCoroutine(Rx.WaitUntil(
                    waitUntil,
                    SpawnRocket
                ));
            }

            void OnRocketTargetHit(Coords coords)
            {
            }

            void SpawnRocket()
            {
                ReleasedCoords.Add(_coords);

                foreach (var rocketCreation in rocketCreationActions)
                {
                    rocketCreation.Invoke();
                }

                if (_tileSpeciality != TileSpeciality.Bomb)
                {
                    AudioProxy.PlaySound(_distances.Count > 2
                        ? Match3SoundIds.CrossBreaker
                        : Match3SoundIds.LineBreaker);
                }

                proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(proxy.Settings.TileDestroyByBooster.BusyTime, _coords.ToUnityVector2());
            }
        }
        
        protected override string GetMembersString()
        {
            return $"coords={_coords}";
        }
    }
}