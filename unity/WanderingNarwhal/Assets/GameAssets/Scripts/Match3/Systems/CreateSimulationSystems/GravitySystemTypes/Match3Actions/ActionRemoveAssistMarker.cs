using BBB.CellTypes;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionRemoveAssistMarker : Match3ActionBase
    {
        private readonly Coords _start;

        public ActionRemoveAssistMarker(Coords start)
        {
            _start = start;
            
            AffectedCoords.Add(_start);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var startCell = grid.GetCell(_start);

#if BBB_DEBUG
            if (startCell.IsAnyOf(CellState.AssistMarker))
                proxy.AssistPanel.AppendToLog("ASSIST MARKER REMOVED AT " + startCell.Coords);
#endif

            startCell.Remove(CellState.AssistMarker);
            
            ReleasedCoords.Add(_start);
        }
    }
}