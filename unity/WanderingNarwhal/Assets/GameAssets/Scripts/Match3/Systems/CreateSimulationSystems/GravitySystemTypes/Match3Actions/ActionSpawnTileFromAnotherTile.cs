using System.Collections.Generic;
using BBB.Core;
using BebopBee.Core;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Match3.Logic;
using UniRx;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    /// <summary>
    /// Spawn tile from another tile visual action.
    /// </summary>
    /// <remarks>
    /// This visual action plays tile fly animation to target coord.
    /// Note: at the moment of logical action creation it is not yet know what end coord will be, so
    /// there is a special system for deferred delivery of the coord from the TileHitReactionHandler.
    ///
    /// The overview of steps for tile from tile spawn:
    /// - first some tile (egg, or chicken, or monkey, or beehive) receives hit and triggers reaction;
    /// - tileHitReactionHandler takes the reaction into queue and executes it in m3 simulation loop;
    /// - spawn tile from tile reaction is managing creation of this logical action instance and registers it in the view actions stack.
    /// -- at this point only new tile is created and target cell is not yet found
    /// - next the tileHitReactionHandler register delayed reaction for spawn event with stored new tile reference (this tile was not placed on grid yet),
    /// - simulation passes few steps until delayed reaction time comes;
    /// - delayed spawn reaction searches for the random cell on grid and assigns the coords of the cell to the logical action instance (that was already created few simulation steps before),
    /// - the end of simulation flow (all other m3 mechanics related processes in simulation happens at same time);
    /// - after simulation is complete it is displayed on screen via playback of logical actions one by one, including the created instance of spawn tile from tile action.
    ///
    /// The tricky part here is that we create and register Logical action instance without all required parameters, and then, after few simulation steps, fill missing parameters in it. -VK 
    /// </remarks>

    public sealed class ActionSpawnTileFromAnotherTile : Match3ActionBase, IFreeFallTargetPriority
    {
        private readonly int _sourceTileId;
        private readonly Coords _start;
        private readonly Tile _tile;
        private readonly TileAsset _tileAsset;
        private Coords _end;

        private static readonly List<ActionSpawnTileFromAnotherTile> _instances = new List<ActionSpawnTileFromAnotherTile>(20);

        public ActionSpawnTileFromAnotherTile(int sourceTileId, Coords start, Coords end, 
            Tile tile,
            TileAsset tileAsset)
        {
            _sourceTileId = sourceTileId;
            _start = start;
            _end = end;
            _tile = tile;
            _tileAsset = tileAsset;
            for (int i = _instances.Count - 1; i >= 0; i--)
            {
                if (_instances[i]._tile.Id == tile.Id)
                {
                    // Remove old records from previous runs;
                    _instances.RemoveAt(i);
                }
            }

            _instances.Add(this);
            WaitCondition = ActionWaitConditionFactory.Create(null, sourceTileId, _start);
            
            AffectedCoords.Add(_start);
            AffectedCoords.Add(_end);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            bool WaitUntil() => proxy.TileTickPlayer.TryMoveTo(_tile.Id, _end);

            if (WaitUntil())
            {
                SpawnBee();
            }
            else
            {
                MainThreadDispatcher.StartUpdateMicroCoroutine(Rx.WaitUntil(
                    WaitUntil, SpawnBee));
            }

            void SpawnBee()
            {
                var destinationCell = grid.GetCell(_end);
                var destinationTile = destinationCell?.Tile;
                
                grid.TilesSpawnedCount++; //why ?????

                proxy.TileTickPlayer.BoardObjectFactory.RemoveFutureTargetLock(_end, _tile.Id);
                
                _instances.Remove(this);

                // TileView will spawn with disabled rendering, and will wait the Appear FX to play.
                // also the tile view will be detached from the grid, because the _tile is not placed in target cell yet,
                // which will happen after few steps with Replace logical action. (or Spawn logical action if target cell is empty).
                // see DoSpawnTileAtPosition in TileHitReactionHandler
                //
                // Logic of disabling and enabling of rendering is located inside LayerRenderer component of the tile. -VK
                var staticOccupier =
                    proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(float.MaxValue, _end.ToUnityVector2());
                var toPos = proxy.GridController.ToDisplacedWorldPosition(_end.ToUnityVector2());
                var startCellView = proxy.CellController.GetCellView(_start);
                proxy.TilesUnderLongSpawningAnimation[_tile.Id] = _end;
                proxy.TileResourceSelector.GetAppearFlySettings(_tileAsset, out var fx, out var settings);
                var from = startCellView.GetPosition();
                
                if (settings.DurationFirstPart < 0 || settings.DurationSecondPart < 0)
                {
                    BDebug.LogError(LogCat.Match3, "Goal fly duration has negative value! " + fx);
                }

                var flySettings = new GoalIconFlySettings()
                {
                    fromPos = from,
                    midPos = settings.IsMiddlePointInCenterOfScreen
                        ? proxy.GridController.GetCenterPosition()
                        : from + settings.RelateiveOffset,
                    toPos = toPos,
                    flightDuration = settings.DurationFirstPart,
                    flightDurationFirstSegment = settings.DurationFirstPart,
                    flightDurationSecondSegment = settings.DurationSecondPart,
                    motionFirstSegment = settings.StartAnimCurve,
                    motionSecondSegment = settings.EndAnimCurve,
                    motionSecondaryX = settings.ParalelMotionXCurve,
                    motionSecondaryY = settings.ParalelMotionYCurve,
                    motionSecondSecondaryX = settings.ParalelSecondMotionXCurve,
                    motionSecondSecondaryY = settings.ParalelSecondMotionYCurve,
                    startSoundDelay = settings.SoundDelay,
                    startSoundUid = settings.StartSoundUid,
                    startDelay = settings.StartDelay,
                    factoryMethod = FactoryMethod,
                    onTargetReachedCallback = OnTargetReachedCallback,
                    releaseMethod = ReleaseMethod,
                };

                GoalIconFlyAnimator.Instance.Launch(flySettings);
                
                Rx.Invoke(settings.StartDelay, _ =>
                {
                    proxy.FXRenderer.SpawnAnticipationEffect(_end);
                });

                if (_start != _end)
                {
                    ReleasedCoords.Add(_start);
                }

                GameObject FactoryMethod()
                {
                    var effect = proxy.FXRenderer.SpawnFx(fx);

                    var configurableEffect = effect.GetComponent<ConfigurableEffectBase>();
                    if (configurableEffect != null)
                    {
                        var skin = _tile.GetParam(TileParamEnum.Skin);
                        configurableEffect.ApplyParameters(new FxOptionalParameters {skin = skin});
                    }

                    var trail = effect.GetComponent<TrailRenderer>();
                    if (trail != null)
                    {
                        trail.enabled = settings.UseTrail;
                    }
                    else
                    {
                        if (settings.UseTrail)
                        {
                            BDebug.Log(LogCat.Match3, $"Effect '{fx}' config has trail enabled flag but the effect prefab missing trail renderer on root object.");
                        }
                    }

                    var tf = effect.transform;

                    if (settings.ScaleType == GoalIconFlySettingsScriptableObject.ScaleCurveType.Relative)
                    {
                        if (settings.UseSeparateScaleCurves)
                        {
                            GoalIconFlySettingsScriptableObject.TweenScaleRelativeOnSeparateAxis(tf,
                                settings.ScaleCurveX, settings.ScaleCurveY, settings.TotalDuration);
                        }
                        else
                        {
                            GoalIconFlySettingsScriptableObject.TweenScaleRelative(tf, settings.ScaleCurve,
                                settings.TotalDuration);
                        }
                    }
                    else
                    {
                        GoalIconFlySettingsScriptableObject.TweenScaleAbsolute(tf, settings.ScaleCurve,
                            settings.TotalDuration);
                    }

                    if (settings.UseShadow)
                    {
                        proxy.FXRenderer.SpawnFlyShadow(tf as RectTransform, settings.TotalDuration, settings.ShadowOffset, settings.ScaleCurve);
                    }

                    return effect.gameObject;
                }

                void OnTargetReachedCallback(int index)
                {
                    if (destinationTile == null)
                    {
                        proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(-1, _end.ToUnityVector2());
                    }
                    ReleasedCoords.Add(_end);
                    staticOccupier.Delete(proxy);
                    AudioProxy.PlaySound(settings.EndSoundUid);
                    proxy.TilesUnderLongSpawningAnimation.Remove(_tile.Id);
                }

                void ReleaseMethod(GameObject go) => go.Release();
            }
        }

        protected override string GetMembersString()
        {
            return $"tileId={_tile.Id} start={_start} end={_end} specialty={_tile.Speciality}";
        }

        public int TileId => _tile.Id;
        public Coords Coords => _end;
    }
}