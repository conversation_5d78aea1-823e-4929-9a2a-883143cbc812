using System;
using System.Collections.Generic;
using BBB.Core;
using BebopBee.Core.Audio;
using BebopBee.UnityEngineExtensions;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace BBB.Match3.Renderer
{
    public class SquidLayerRenderer : DelayedAppearLayerRenderer
    {
        [Serializable]
        public struct Row
        {
            public List<RectTransform> Columns;
        }

        [SerializeField] private Sprite[] _damageSpritesHoles;
        [SerializeField] private Sprite[] _damageSpritesCracks;
        [SerializeField] private Image _damageImageTemplate;
        [SerializeField] private RectTransform _damagePivotTemplate;
        [SerializeField] private Animator _animator;
        [SerializeField] private Vector3 _previewScale;
        [SerializeField] private Vector2 _previewPivot;
        [SerializeField] private GameObject _fgImageDefault;
        [SerializeField] private GameObject _fgImagePreview;
        [SerializeField] private SquidLayerSubItemRenderer _squidLayerSubItemRenderer;
        [SerializeField] private RectTransform _placementPivotTemplate;
        [SerializeField] private RectTransform _scaledContainer;
        [SerializeField] private string _hitSfxUid = "SquidHit";

        private readonly List<RectTransform> _damageCells = new(SquidTileLayer.MAX_SUBITEMS_COUNT);

        private List<Row> _damagePivotsRuntime;
        private float _defaultCellSize;
        private bool _isSingle;

        private static readonly int ShakeSquid = Animator.StringToHash("ShakeSquid");

#if UNITY_EDITOR
        private void Start()
        {
            if (M3Editor.M3Editor.IsCurrentSceneLevelEditorScene())
            {
                gameObject.AddComponent<GraphicRaycaster>();
            }
        }
#endif

        public override void Init()
        {
            base.ResetToDefault();
            ResetAnimator();
        }
        
        private void ResetDamageCells()
        {
            foreach (var damageCell in _damageCells)
            {
                if (damageCell != null)
                {
                    damageCell.gameObject.SetActive(false);
                }
            }
        }

        private void ResetSubItems()
        {
            if (_squidLayerSubItemRenderer != null)
            {
                _squidLayerSubItemRenderer.ResetToDefault();
            }
        }

        private void ResetAnimator()
        {
            if (_animator != null)
            {
                _animator.ResetAllParameters();
                _animator.PlayMainOrRebind();
            }
        }

        private void ToggleFgVisibility(bool isPreview)
        {
            if (_fgImagePreview != null)
            {
                _fgImagePreview.SetActive(isPreview);
            }

            if (_fgImageDefault != null)
            {
                _fgImageDefault.SetActive(!isPreview);
            }
        }

        public void Setup(int sizeX, int sizeY, TileKinds[] kinds, bool isSingle, bool isPreview = false)
        {
            _isSingle = isSingle;
            ToggleFgVisibility(isPreview);
            InitializeDefaultCellSizeIfNeeded();
            ConfigureContainer(sizeX, sizeY, isPreview);
            SetupDamageGrid(sizeX, sizeY);
            SetupActiveItems(kinds);
            OnRefresh();
        }

        private void InitializeDefaultCellSizeIfNeeded()
        {
            if (_defaultCellSize == 0)
            {
                _defaultCellSize = _scaledContainer.sizeDelta.x;
#if UNITY_EDITOR
                if (M3Editor.M3Editor.IsCurrentSceneLevelEditorScene())
                {
                    _defaultCellSize *= 1.05f;
                }
#endif
            }

            FitBaseRectToExactCellSize();
        }

        private void ConfigureContainer(int sizeX, int sizeY, bool isPreview)
        {
            _animator.enabled = !isPreview;

            // Set container properties
            _scaledContainer.pivot = new Vector2(0, 0);
            _scaledContainer.anchorMin = Vector2.zero;
            _scaledContainer.anchorMax = new Vector2(sizeX, sizeY);
            _scaledContainer.offsetMin = Vector2.zero;
            _scaledContainer.offsetMax = Vector2.zero;

            if (isPreview)
            {
                ConfigureForPreview();
            }
        }

        private void ConfigureForPreview()
        {
            transform.RectTransform().pivot = new Vector2(0.5f, 0.5f);
            transform.RectTransform().localPosition = Vector3.zero;
            _scaledContainer.pivot = _previewPivot;
            _scaledContainer.localScale = _previewScale;
        }

        private void SetupActiveItems(TileKinds[] kinds)
        {
            if (_squidLayerSubItemRenderer == null || kinds == null || kinds.Length == 0)
            {
                return;
            }

            var itemTransform = _squidLayerSubItemRenderer.transform;
            itemTransform.localPosition = Vector3.zero;

            var rectTransform = _squidLayerSubItemRenderer.GetComponent<RectTransform>();
            rectTransform.offsetMin = new Vector2(5f, 5f);
            rectTransform.offsetMax = new Vector2(-5f, -5f);

            // Use first item in kinds array directly
            _squidLayerSubItemRenderer.Setup(kinds[0]);
        }

        private void SetupDamageGrid(int sizeX, int sizeY)
        {
            SetupGridBySize(sizeX, sizeY, _damagePivotTemplate, ref _damagePivotsRuntime);
            ConfigureDamageCells(sizeX, sizeY);
        }

        private void ConfigureDamageCells(int sizeX, int sizeY)
        {
            var childCellRectRatioX = sizeX <= 0 ? 1f : 1f / sizeX;
            var childCellRectRatioY = sizeY <= 0 ? 1f : 1f / sizeY;

            _damageCells.Clear();

            var rowIndex = 0;
            foreach (var row in _damagePivotsRuntime)
            {
                var colIndex = 0;
                foreach (var col in row.Columns)
                {
                    var isActiveItem = colIndex < sizeX && rowIndex < sizeY;
                    if (isActiveItem)
                    {
                        ConfigureActiveCell(col, colIndex, rowIndex, childCellRectRatioX, childCellRectRatioY);
                    }
                    else
                    {
                        col.gameObject.SetActive(false);
                        _damageCells.Add(null);
                    }

                    colIndex++;
                }
                rowIndex++;
            }
        }

        private void ConfigureActiveCell(RectTransform col, int colIndex, int rowIndex, float ratioX, float ratioY)
        {
            col.gameObject.SetActive(true);
            col.anchorMin = new Vector2(colIndex * ratioX, rowIndex * ratioY);
            col.anchorMax = new Vector2((colIndex + 1f) * ratioX, (rowIndex + 1f) * ratioY);
            col.offsetMin = Vector2.zero;
            col.offsetMax = Vector2.zero;

            var damageImage = GetOrCreateDamageImage(col);
            _damageCells.Add(damageImage.GetComponent<RectTransform>());
        }

        private Image GetOrCreateDamageImage(RectTransform parent)
        {
            var child = parent.childCount > 0 ? parent.GetChild(0).GetComponent<Image>() : null;

            if (child == null)
            {
                child = Instantiate(_damageImageTemplate, parent);
                var childRect = child.GetComponent<RectTransform>();
                childRect.anchoredPosition = Vector2.zero;
                childRect.offsetMin = Vector2.zero;
                childRect.offsetMax = Vector2.zero;
            }

            child.sprite = GetRandomDamageSprite(_isSingle);
            child.gameObject.SetActive(false); // Initially hidden
            return child;
        }

        private Sprite GetRandomDamageSprite(bool isSingle)
        {
            var sprites = isSingle ? _damageSpritesCracks : _damageSpritesHoles;
            return sprites[UnityEngine.Random.Range(0, sprites.Length)];
        }

        private static void SetupGridBySize(int sizeX, int sizeY, RectTransform template, ref List<Row> grid)
        {
            template.gameObject.SetActive(false);
            InitializeGridIfNeeded(sizeY, ref grid);
            AdjustGridToSize(sizeX, sizeY, template, grid);
        }

        private static void InitializeGridIfNeeded(int sizeY, ref List<Row> grid)
        {
            if (grid == null)
            {
                grid = new List<Row>(sizeY);
            }
        }

        private static void AdjustGridToSize(int sizeX, int sizeY, RectTransform template, List<Row> grid)
        {
            RemoveExcessRows(sizeY, grid);
            AddNeededRows(sizeY, sizeX, grid);
            ConfigureRows(sizeX, grid, template);
        }

        private static void RemoveExcessRows(int sizeY, List<Row> grid)
        {
            while (grid.Count > sizeY)
            {
                var last = grid.Count - 1;
                foreach (var col in grid[last].Columns)
                {
                    if (col != null)
                    {
                        Destroy(col.gameObject);
                    }
                }
                grid.RemoveAt(last);
            }
        }

        private static void AddNeededRows(int sizeY, int sizeX, List<Row> grid)
        {
            while (grid.Count < sizeY)
            {
                grid.Add(new Row { Columns = new List<RectTransform>(sizeX) });
            }
        }

        private static void ConfigureRows(int sizeX, List<Row> grid, RectTransform template)
        {
            for (var y = 0; y < grid.Count; y++)
            {
                var row = grid[y];
                AdjustRowColumns(sizeX, row, template, y);
            }
        }

        private static void AdjustRowColumns(int sizeX, Row row, RectTransform template, int rowIndex)
        {
            RemoveExcessColumns(sizeX, row);
            AddNeededColumns(sizeX, row, template);
            ConfigureColumns(row, rowIndex);
        }

        private static void RemoveExcessColumns(int sizeX, Row row)
        {
            while (row.Columns.Count > sizeX)
            {
                var last = row.Columns.Count - 1;
                var item = row.Columns[last];
                if (item != null)
                {
                    Destroy(item.gameObject);
                }
                row.Columns.RemoveAt(last);
            }
        }

        private static void AddNeededColumns(int sizeX, Row row, RectTransform template)
        {
            while (row.Columns.Count < sizeX)
            {
                var item = Instantiate(template, template.parent);
                row.Columns.Add(item);
            }
        }

        private static void ConfigureColumns(Row row, int rowIndex)
        {
            for (var x = 0; x < row.Columns.Count; x++)
            {
                var column = row.Columns[x];
                column.gameObject.SetActive(true);
#if UNITY_EDITOR
                column.name = $"{x}_{rowIndex}";
#endif
            }
        }

        public void SetVisibleDamageAt(int index)
        {
            if (index < 0 || index >= _damageCells.Count)
                return;

            var damageCell = _damageCells[index];
            if (damageCell == null)
            {
                BDebug.LogError(LogCat.Match3, $"Damage cell view at index {index} is null");
                return;
            }

            damageCell.gameObject.SetActive(true);
        }

        public void PlayDamageReactionTo()
        {
            _animator.SetTrigger(ShakeSquid);

            if (_squidLayerSubItemRenderer != null)
            {
                _squidLayerSubItemRenderer.PlayHitReaction();
            }

            AudioProxy.PlaySound(_hitSfxUid);
        }

        public void PlayDestroy()
        {
            ResetDamageCells();
            ResetSubItems();
        }

        public override void PlayPreview()
        {
            IsPlayedAppear = true;
            Show();
            Setup(1, 1, new[] { TileKinds.Blue }, isSingle: true, isPreview: true);
            _squidLayerSubItemRenderer.PlayPreview();
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            _squidLayerSubItemRenderer.PlayHitReaction();
        }

        protected override void OnAppearAfterDelayAfterBaseCall()
        {
            _squidLayerSubItemRenderer.PlayAppear();
        }

        protected override void Hide()
        {
            ResetDamageCells();
            ResetSubItems();
        }
    }
}