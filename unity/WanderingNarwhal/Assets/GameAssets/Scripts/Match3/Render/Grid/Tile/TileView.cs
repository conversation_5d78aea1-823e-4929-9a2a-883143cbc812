using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.Match3.Systems;
using BBB.MMVibrations;
using BBB.UI;
using DG.Tweening;
using BBB.MMVibrations.Plugins;
using GameAssets.Scripts.Match3.Settings;
using GameAssets.Scripts.Wallet.Visualizing;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public struct PreDestroyTweener
    {
        public Tweener Tweener;
        public Action OnTweenerComplete;
    }

    public class TileView : ActivePoolItem, IPoolItemReleaseNotifier,
        IPositionUpdatable, IFadable
    {
        private const string TileContainerName = "TileContainer_";
        private const string TileContainerInPoolName = "TileContainerInPool";
        private const int TempRenderedTileLayersCount = 10;
        private const int ViewsCount = 10;
        public event Action OnReleaseEvent;

        private static readonly List<ITileLayer> TempRenderedTileLayers = new (TempRenderedTileLayersCount);

        [SerializeField] private RectTransform _layersContainer;
        [SerializeField] private GameObject _tileDebugViewGo;

        private TileDebugView _tileDebugView;

        private IList<ITileLayer> _allLayers;
        private readonly List<ITileLayerView> _viewsList = new (ViewsCount);
        private IContext _context;
        private IGridController _gridController;
        private FxRenderer _fxRenderer;
        private RectTransform _transform;
        private ulong _prevLayerState;
        private Vector3 _defaultScale;
        private int _addedIncrement;
        private RendererContainers _rendererContainers;
        private ContainerType _currentContainer;
        private Tweener _layersAnimationDelayTween;
        private Tweener _releaseDelayTween;
        private IVibrationsWrapper _vibrations;

        private FadeInControlPair _fadeInControlPair = new()
        {
            FadeInStartY = -1f,
            FadeInEndY = -1f
        };

        private Coroutine _animateDestroyRoutine;
        private Coroutine _releaseRoutine;
        private ICoroutineExecutor _coroutineExecutor;
        private bool _playingPreDestroyAnimation;
        private Tweener _destroyTween;

        public bool RunningBoltBoltAnimation { get; set; }
        public TileLayerState LayerState => (TileLayerState)_prevLayerState;

        /// <summary>
        /// Is this tile view created before actual tile.
        /// Prevent auto-release process of view for this turn.
        /// </summary>
        /// <remarks>
        /// Used in situation when tile is spawned from another tile (Bird, Banana, Chicken, Bee),
        /// because in this case tile is created few m3 simulation steps before it is placed on the grid.
        /// tile view for such tiles must be created detached from grid because we need to play fly animation to target grid. -VK
        /// </remarks>
        public bool IsPendingTileCreation { get; set; }

        public RectTransform LocalAnchor => _layersContainer;

        public TileAnimator Animator { get; private set; }

        public TileMotionController TileMotionController { get; private set; }

        public Vector3 LocalPosition => _gridController.ToLocalPosition(TileMotionController.Position);

        public Vector3 GlobalPosition => _gridController.ToWorldPosition(TileMotionController.Position);

        public Coords Coords => TileMotionController.Coords;

        public void Init(IContext context)
        {
            _context = context;
            _fxRenderer = context.Resolve<FxRenderer>();
            _gridController = context.Resolve<IGridController>();
            _allLayers = context.Resolve<IList<ITileLayer>>();
            _rendererContainers = context.Resolve<RendererContainers>();
            _vibrations = context.Resolve<IVibrationsWrapper>();
            var settings = context.Resolve<M3Settings>();
            TileMotionController.ApplySettings(settings);
        }

        public override void OnInstantiate()
        {
            gameObject.name = TileContainerInPoolName;
            base.OnInstantiate();
            _transform = GetComponent<RectTransform>();
            Animator = GetComponent<TileAnimator>();
            TileMotionController = new TileMotionController(this, this);
            _transform.localScale = Vector3.one;
            _defaultScale = _layersContainer.localScale;
            Animator.Init();
        }

        public void UpdatePositionDependentProperties()
        {
            if (TileMotionController.Mode == TileTransformMode.ZeroGravity)
                return;

            _transform.localPosition = _gridController.ToLocalPosition(TileMotionController.Position);
            SetGlow(false);
            foreach (var view in _viewsList)
            {
                view.OnCoordsChanged(TileMotionController.Position);
            }

            if (FadeInModeActive())
            {
                FadeIn(ratio =>
                {
                    _transform.localScale = ratio * Vector3.one;
                    SetAlpha(ratio);
                });
            }
        }

        public void ShakeWithSiblings()
        {
            foreach (var view in _viewsList)
            {
                view.ShakeWithSiblings();
            }
        }

        public void ActivateFadeIn(float start, float end)
        {
            _fadeInControlPair = new FadeInControlPair
            {
                FadeInStartY = start,
                FadeInEndY = end
            };

            _transform.localScale = Vector3.zero;
            SetAlpha(0f);
        }

        public void ResetScale()
        {
            _transform.localScale = Vector3.one;
        }

        public bool FadeInModeActive()
        {
            return _fadeInControlPair.FadeInEndY > 0f && TileMotionController.Mode == TileTransformMode.FreeFall;
        }

        private void FadeIn(Action<float> fadeInHandler)
        {
            var y = TileMotionController.Position.y;
            var ratio = (y - _fadeInControlPair.FadeInStartY) / _fadeInControlPair.Delta;
            ratio = Mathf.Clamp01(ratio);

            if (ratio >= 1f)
            {
                _fadeInControlPair.FadeInStartY = -1f;
                _fadeInControlPair.FadeInEndY = -1f;
            }

            fadeInHandler(ratio);
        }

        public void CleanTileMotionController()
        {
            TileMotionController?.Clean();
        }

        public void Show()
        {
            OnSpawn();
        }

        public void Hide()
        {
            base.OnRelease();
            _layersContainer.gameObject.SetActive(false); //TODO: remove when child objects support Enable/Disable
        }

        protected override void OnDisable()
        {
            TileMotionController?.Clean();
            if (_animateDestroyRoutine != null)
            {
                _coroutineExecutor.StopCoroutine(_animateDestroyRoutine);
                _animateDestroyRoutine = null;
            }

            if (_releaseRoutine == null)
                return;
            
            _coroutineExecutor.StopCoroutine(_releaseRoutine);
            _releaseRoutine = null;
        }

        public override void OnSpawn()
        {
            base.OnSpawn();
            _layersContainer.gameObject.SetActive(true); //TODO: remove when child objects support Enable/Disable
        }

        public override void OnRelease()
        {
            OnReleaseEvent?.Invoke();
            OnReleaseEvent = null;

            IsPendingTileCreation = false;

            _fadeInControlPair = new FadeInControlPair
            {
                FadeInStartY = -1f,
                FadeInEndY = -1f
            };

            gameObject.name = TileContainerInPoolName;

            if (_layersAnimationDelayTween != null)
            {
                _layersAnimationDelayTween.Kill();
                _layersAnimationDelayTween = null;
            }


            MoveLayersToBottom_Internal();
            SetGlow(false);

            _transform = GetComponent<RectTransform>();

            _transform.localRotation = Quaternion.identity;
            _transform.localPosition = Vector3.zero;
            _transform.SetSiblingIndex(0);
            // _spawnAnimating = false;

            // Scale needs reset because it gets messed up after activation of Combine Effects on tiles,
            // which results in incorrect rendering of affected tiles after they return from pool via grid spawners.
            // For most combine effects the scale reset was fixed inside their script controllers, but it was reported that
            // bug still occurs sometimes after WhirlpoolEffect, so additional scale reset was added here to solve this globally. -VK
            _destroyTween?.Complete();
            _transform.localScale =  Vector3.one;
            TileMotionController.Clean();
            _prevLayerState = 0;
            _layersContainer.anchoredPosition = Vector3.zero;
            _layersContainer.localScale = _defaultScale;
            _layersContainer.localRotation = Quaternion.identity;

            RemoveFromSortOrder();
            MoveToDefaultContainer();

            _viewsList.ForEach(UnApplyAction);
            _viewsList.Clear();

            Animator.OnRelease();
            Hide();


            if (_animateDestroyRoutine != null)
            {
                _coroutineExecutor.StopCoroutine(_animateDestroyRoutine);
                _animateDestroyRoutine = null;
            }

            if (_releaseRoutine != null)
            {
                _coroutineExecutor.StopCoroutine(_releaseRoutine);
                _releaseRoutine = null;
            }

            _coroutineExecutor = null;
            _playingPreDestroyAnimation = false;
            return;

            void UnApplyAction(ITileLayerView tileLayerView)
            {
                tileLayerView.UnApply();
            }
        }

        public void ReleaseItem()
        {
            if (gameObject != null)
                gameObject.Release();
        }

        public void UpdateView(Cell cell, Tile tile, Coords coords, bool force = false, bool isVisible = true, bool isReveal = false)
        {
            gameObject.name = TileContainerName + tile.Id;
            var layerState = tile.ToLayerState();
            if (force || (TileLayerState)_prevLayerState != layerState)
            {
                OnLayerStateChange(layerState, cell, tile, coords, isVisible, isReveal);
                _prevLayerState = (ulong)layerState;

                if (coords != Coords.OutOfGrid)
                    _transform.SetSiblingIndex(coords.Y * 10 + coords.X);
            }
        }

        private static int CompareLayers(ITileLayer a, ITileLayer b)
        {
            return a.CurrentOrder.CompareTo(b.CurrentOrder);
        }

        private void OnLayerStateChange(TileLayerState layerState, Cell cell, Tile tile, Coords coords, bool isVisible = true, bool isReveal = false)
        {
#if BBB_LOG_M3_SIMULATION
            if (_tileDebugView == null)
            {
                var go = Instantiate(_tileDebugViewGo, _transform);
                _tileDebugView = go.GetComponent<TileDebugView>();
            }
            _tileDebugView.Init(tile);
#endif

            if (tile.Asset == TileAsset.Simple && tile.Kind == 0)
            {
                BDebug.LogErrorFormat(LogCat.Match3, "TileView: trying to apply none tile");
            }

            TempRenderedTileLayers.Clear();

            int len = 0;
            foreach (var layer in _allLayers)
            {
                if (layer.IsRenderer(layerState))
                {
                    TempRenderedTileLayers.Add(layer);
                    len++;
                }
            }

            if (len > 1)
            {
                TempRenderedTileLayers.Sort(CompareLayers);
            }

            var n = 0;
            foreach (var layer in TempRenderedTileLayers)
            {
                ITileLayerView layerView = null;
                foreach (var view in _viewsList)
                {
                    if (view.State == layer.State)
                    {
                        layerView = view;
                        break;
                    }
                }

                if (layerView == null)
                {
                    layerView = _context.Resolve<ITileLayerView, ITileLayer>(layer);
                    _viewsList.Add(layerView);
                }

                bool justApplied = false;
                if (!layerView.Applied || layerView is IAlwaysApplicableTileView)
                {
                    layerView.Apply(tile, coords, _layersContainer, _viewsList, isVisible);
                    justApplied = true;
                }

                layerView.Update(tile, layer, n++);

                if (justApplied)
                    layerView.AfterApply();

                if (cell != null && layerView is ICounteredTile counteredTile)
                {
                    counteredTile.ChangeCount(cell);
                    if (!isReveal)
                    {
                        Vibrate(cell.GetHashCode());
                    }
                }
            }

            for (var i = _viewsList.Count - 1; i >= 0; i--)
            {
                var view = _viewsList[i];
                bool isBeingRendered = false;
                foreach (var layer in TempRenderedTileLayers)
                {
                    if (layer.State == view.State)
                    {
                        isBeingRendered = true;
                        break;
                    }
                }

                if (!isBeingRendered)
                {
                    view.Animate(coords, TileLayerViewAnims.Unapply);
                    view.UnApply();
                    _viewsList.RemoveAt(i);
                }
            }

            TempRenderedTileLayers.Clear();
        }

        public void UpdateCounters(Cell cell)
        {
            foreach (var view in _viewsList)
            {
                if (view is ICounteredTile countTile)
                {
                    countTile.ChangeCount(cell);
                    Vibrate(cell.GetHashCode());
                }
            }
        }

        public void TriggerSkunkHitTileAtPos(Coords pos, Action callback)
        {
            foreach (var view in _viewsList)
            {
                if (view is SkunkLayerView skunk)
                {
                    skunk.PlayHitTileAtPos(TileMotionController.Coords, pos, callback);
                    break;
                }
            }
        }
        
        public void TriggerShelfItemShake()
        {
            foreach (var view in _viewsList)
            {
                if (view is ShelfLayerView shelf)
                {
                    shelf.PlayShake();
                    break;
                }
            }
        }

        public void TriggerGondolaMovement(List<Coords> coordsList, List<int> orientation, bool goalCollected, Tile tile, bool fastSpeed, Coords goalCoords, Action callback)
        {
            foreach (var view in _viewsList)
            {
                if (view is not GondolaLayerView gondolaLayerView) continue;
                UpdatePositionDependentProperties();
                var path = new List<Vector3>();
                foreach (var coords in coordsList)
                {
                    path.Add(_gridController.ToLocalPosition(coords));
                }

                gondolaLayerView.UpdateGondolaMovement(_transform, path, orientation, goalCollected, tile, fastSpeed, goalCoords, callback);
                break;
            }
        }

        public void TriggerTapFeedbackAnimation()
        {
            foreach (var view in _viewsList)
            {
                view.Animate(TileMotionController.Coords, TileLayerViewAnims.TapFeedback);
            }
        }

        public void TriggerPreviewAnimation()
        {
            foreach (var view in _viewsList)
            {
                view.Animate(TileMotionController.Coords, TileLayerViewAnims.Preview);
            }
        }


        public void TriggerSwapAnimation()
        {
            foreach (var view in _viewsList)
            {
                view.Animate(TileMotionController.Coords, TileLayerViewAnims.Swap);
            }
        }

        public void TriggerBoltComboAnimation()
        {
            foreach (var view in _viewsList)
            {
                view.Animate(TileMotionController.Coords, TileLayerViewAnims.BoltCombo);
            }
        }

        public void TriggerFormationIntroAnimation()
        {
            foreach (var view in _viewsList)
            {
                view.Animate(TileMotionController.Coords, TileLayerViewAnims.FormationIntro);
            }
        }

        public Tweener ShrinkScaleTo(float duration, float targetScale, AnimationCurve curve)
        {
            return _transform.DOScale(targetScale, duration)
                .SetEase(curve);
        }

        public Tweener TweenLocalPosition(Coords startCoord, Coords targetCoords, float duration,
            Vector3[] path = null, AnimationCurve curve = null, Action callback = null)
        {
            UpdatePositionDependentProperties();
            var startPos = _gridController.ToLocalPosition(startCoord);
            var targetPos = _gridController.ToLocalPosition(targetCoords);

            Tweener tweener;

            if (path != null)
            {
                tweener = _transform.DOLocalPath(path, duration, PathType.CatmullRom);
            }
            else
            {
                _transform.localPosition = startPos;
                tweener = _transform.DOLocalMove(targetPos, duration);
            }

            if (curve != null)
                tweener.SetEase(curve);
            else
                tweener.SetEase(Ease.InOutCubic);

            tweener.OnKill(CompleteAction).OnComplete(CompleteAction);

            return tweener;

            void CompleteAction()
            {
                try
                {
                    callback.SafeInvoke();
                }
                catch (Exception e)
                {
                    BDebug.LogError(LogCat.General, "Exception in TweenLocalPosition: " + e.Message + " Stack: " + e.StackTrace);
                }
            }
        }

        public void TweenAddRotation(float angle, float duration)
        {
            _transform.DOLocalRotate(Vector3.forward * angle, duration, RotateMode.FastBeyond360).SetRelative()
                .SetEase(Ease.InOutCubic);
        }

        public void AnimateLayerViews(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            _layersAnimationDelayTween = null;
            foreach (var view in _viewsList)
            {
                view.Animate(coords, anim, animParams);
            }
        }

        private bool ReleaseProcessBlocked()
        {
            if (_playingPreDestroyAnimation || RunningBoltBoltAnimation)
                return true;

            if (_layersAnimationDelayTween != null)
                return true;

            if (Animator != null && Animator.IsAnyPlaying(StateType.Destroy))
                return true;

            foreach (var view in _viewsList)
            {
                if (view.IsPlayingLayerViewAnimation)
                {
                    return true;
                }
            }

            return false;
        }

        public void SetAlpha(float alpha)
        {
            _viewsList.ForEach(FadeAction);
            return;

            void FadeAction(ITileLayerView layerView)
            {
                layerView.Alpha = alpha;
            }
        }

        public void SpawnFromPosition(Vector3 endPos, float duration, FollowingTransform followingTransform, float fxTrailDuration)
        {
            followingTransform.Start(duration);
            _transform.position = followingTransform.Transform.position;
            _fxRenderer.SpawnBoosterTrail(_transform, fxTrailDuration);

            DOTween
                .To(() => 0f, _ => { _transform.position = followingTransform.Transform.position; }, 1f, duration)
                .OnComplete(() =>
                {
                    followingTransform.Finish();
                    _transform.localPosition = endPos;
                });
        }

        public void FadeInAppear(float startScale, float duration, float alphaFadeInTime, AnimationCurve movementCurve)
        {
            _viewsList.ForEach(FadeAction);
            _transform.localScale = Vector3.one * startScale;
            Show();

            _viewsList.ForEach(UnFadeAction);

            _transform.DOScale(Vector3.one, duration)
                .SetEase(movementCurve)
                .OnStart(MoveLayersOnTop_Internal)
                .OnComplete(CompleteAction);
            return;

            void FadeAction(ITileLayerView layerView)
            {
                layerView.Alpha = 0f;
            }

            void UnFadeAction(ITileLayerView layerView)
            {
                DOTween.To(GetAlpha, SetAlphaLocal, 1f, alphaFadeInTime)
                    .SetEase(Ease.OutCubic);
                return;

                float GetAlpha()
                {
                    return layerView.Alpha;
                }

                void SetAlphaLocal(float x)
                {
                    layerView.Alpha = x;
                }
            }

            void CompleteAction()
            {
                MoveLayersToBottom_Internal();
                _fxRenderer.SpawnBoostRevealExplosion(TileMotionController.Coords);
                _vibrations.PlayHaptic(ImpactPreset.MediumImpact);
            }
        }

        public void CustomAnimatedAppear()
        {
            foreach (var view in _viewsList)
            {
                view.Animate(TileMotionController.Coords, TileLayerViewAnims.CustomAppear);
            }
        }

        public void MoveLayersOnTop()
        {
            MoveLayersOnTop_Internal();
        }

        public void MoveLayersToBottom()
        {
            MoveLayersToBottom_Internal();
        }

        private void MoveLayersOnTop_Internal()
        {
            const string layerName = "Overlay";

            _viewsList.ForEach(SortAction);
            return;

            void SortAction(ITileLayerView view)
            {
                view.SortingLayer = SortingLayer.NameToID(layerName);
            }
        }

        private void MoveLayersToBottom_Internal()
        {
            const string layerName = "Default";

            _viewsList.ForEach(SortAction);
            return;

            void SortAction(ITileLayerView view)
            {
                view.SortingLayer = SortingLayer.NameToID(layerName);
            }
        }

        /// <summary>
        /// Check if this tile doesn't contain any View,
        /// that block animated reactions (such as explosion shakes or fall shake).
        /// </summary>
        public bool CanTileHaveAnimatedReactions()
        {
            const ulong excludedStates =
                (ulong)TileLayerState.Sand
                | (ulong)TileLayerState.Sheep
                | (ulong)TileLayerState.Skunk
                | (ulong)TileLayerState.Monkey
                | (ulong)TileLayerState.Mole
                | (ulong)TileLayerState.Squid
                | (ulong)TileLayerState.Toad
                | (ulong)TileLayerState.Bowling
                | (ulong)TileLayerState.Bush
                | (ulong)TileLayerState.Soda
                | (ulong)TileLayerState.MagicHat
                | (ulong)TileLayerState.Safe
                | (ulong)TileLayerState.IceBar
                | (ulong)TileLayerState.DynamiteBox
                | (ulong)TileLayerState.GiantPinata
                | (ulong)TileLayerState.MetalBar
                | (ulong)TileLayerState.Shelf
                | (ulong)TileLayerState.JellyFish
                | (ulong)TileLayerState.GoldenScarab
                | (ulong)TileLayerState.SlotMachine
                | (ulong)TileLayerState.BigMonkey;
            return (_prevLayerState & excludedStates) == 0;
        }

        public bool HasPowerUpLayers()
        {
            const TileLayerState powerUpLayers =
                TileLayerState.HorizontalLb
                | TileLayerState.VerticalLb
                | TileLayerState.Bomb
                | TileLayerState.ColorBomb;

            return (_prevLayerState & (ulong)powerUpLayers) != 0;
        }

        /// <summary>
        /// Is allowed to use default TileAnimator for destroy animation of the tile.
        /// </summary>
        public bool CanTileHaveDestroyAnimation()
        {
            return (_prevLayerState & ((ulong)TileLayerState.Monkey | (ulong)TileLayerState.BigMonkey | (ulong)TileLayerState.Hen | (ulong)TileLayerState.Gondola)) == 0;
        }

        /// <summary>
        /// Try get skin index from any tile layer that currently exist on this tile.
        /// </summary>
        /// <remarks>
        /// Some tile can have skin index, which only affect visual representation of tile.
        /// This index needs to be accessible for other systems to unify visualization of FXs for this tile.
        /// </remarks>
        public bool TryGetTileViewSkinIndex(ref int skin, Coords coords)
        {
            foreach (var view in _viewsList)
            {
                var layerSkin = view as ITileLayerViewSkin;
                if (layerSkin != null)
                {
                    skin = layerSkin.SkinIndex;
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Fx offset is used for multi-size tiles,
        /// when we need to display fx not in Main cell,
        /// but in some other cell with relative cell coords.
        /// The value of offset is determined by tile layer class when it receives hit event.
        /// </summary>
        public bool TryGetFxSpawnOffset(ref Vector2 offset)
        {
            foreach (var view in _viewsList)
            {
                var layerSkin = view as ITileLayerEffectSpawnOffset;
                if (layerSkin != null)
                {
                    offset = layerSkin.FxSpawnOffset;
                    return true;
                }
            }

            return false;
        }

        public Vector3 GetSpecialRendererGlobalScale()
        {
            foreach (var view in _viewsList)
                if (view is SpecialTileLayerView specialLayerView)
                {
                    return specialLayerView.SpecialRendererGlobalScale;
                }

            return Vector3.zero;
        }

        public void AddToSortOrder(int increment)
        {
            foreach (var view in _viewsList)
            {
                view.AddToSortOrder(increment);
            }

            _addedIncrement = increment;
        }

        public void SetGlow(bool value)
        {
            foreach (var view in _viewsList)
            {
                view.Glow = value;
            }
        }

        public void RemoveFromSortOrder()
        {
            if (_addedIncrement > 0)
            {
                foreach (var view in _viewsList)
                {
                    view.RemoveFromSortOrder(_addedIncrement);
                }

                _addedIncrement = 0;
            }
        }

        private void MoveToContainer(ContainerType containerType)
        {
            _rendererContainers.ReparantTo(this, containerType);
            _currentContainer = containerType;
        }

        private void MoveToDefaultContainer()
        {
            if (_currentContainer != ContainerType.Tiles)
            {
                _rendererContainers.ReparantTo(this, ContainerType.Tiles);
                _currentContainer = ContainerType.Tiles;
            }
        }

        public void MoveToBoostFormationContainerForIntro()
        {
            foreach (var view in _viewsList)
            {
                if (view is SpecialTileLayerView specialTileLayerView)
                {
                    specialTileLayerView.SetIntroCallback(MoveToDefaultContainer);
                    MoveToContainer(ContainerType.FormingBoosts);
                    break;
                }
            }
        }

        public void StartReleaseWhenCan(ICoroutineExecutor executor, Coords coords)
        {
            _coroutineExecutor = executor;
            if (_releaseRoutine != null)
                executor.StopCoroutine(_releaseRoutine);
            _releaseRoutine = executor.StartCoroutine(ReleaseWhenCan(coords));
        }

        private IEnumerator ReleaseWhenCan(Coords coord)
        {
            while (ReleaseProcessBlocked())
            {
                yield return null;
            }

            yield return null;

            if (coord != Coords.OutOfGrid)
                _vibrations?.EnqueueHaptic(ImpactPreset.MediumImpact, coord.GetHashCode());

            ReleaseItem();

            _releaseRoutine = null;
        }

        public void Vibrate(int hash)
        {
            _vibrations?.EnqueueHaptic(ImpactPreset.MediumImpact, hash);
        }

        public void StartAnimateDestroy(Coords cellCoords, TileLayerViewAnimParams animParams,
            ICoroutineExecutor coroutineExecutor, PreDestroyTweener preDestroyTweener)
        {
            _coroutineExecutor = coroutineExecutor;

            if (preDestroyTweener.Tweener != null)
            {
                _destroyTween = preDestroyTweener.Tweener;
                _playingPreDestroyAnimation = true;
                preDestroyTweener.Tweener.OnComplete(() =>
                {
                    _destroyTween = null;
                    preDestroyTweener.OnTweenerComplete?.Invoke();
                    _playingPreDestroyAnimation = false;
                    if ((animParams & TileLayerViewAnimParams.SkipDestroy) == 0)
                        DoStartAnimateDestroy();
                });
            }
            else
            {
                DoStartAnimateDestroy();
            }

            void DoStartAnimateDestroy()
            {
                AnimateLayerViews(cellCoords, TileLayerViewAnims.Destroy, animParams);
                _animateDestroyRoutine = coroutineExecutor.StartCoroutine(AnimateDestroy(cellCoords));
            }
        }

        private IEnumerator AnimateDestroy(Coords cellCoords)
        {
            yield return Animator.AnimateDestroy(cellCoords);
            _animateDestroyRoutine = null;
        }
    }

    [Flags]
    public enum TileLayerViewAnimParams : ulong
    {
        None = 0,
        EndGame = 1L << 0,
        ByWhirlpool = 1L << 1,
        ByRemoveColorTiles = 1L << 2,
        SkipDestroy = 1L << 3
    }

    public enum TileLayerViewAnims
    {
        /// <summary>
        /// Tile is destroyed.
        /// </summary>
        Destroy = 0,

        /// <summary>
        /// Layer on tile is un-applied (for example, if chain or sand is destroyed).
        /// </summary>
        Unapply = 1,

        /// <summary>
        /// Custom animation at game start.
        /// </summary>
        CustomAppear = 3,

        /// <summary>
        /// Animation effect when tile is tapped.
        /// </summary>
        TapFeedback = 4,

        /// <summary>
        /// Tile in preview state (outside of the grid).
        /// </summary>
        /// <remarks>
        /// Used for Match3 Help panel.
        /// </remarks>
        Preview = 5,

        /// <summary>
        /// Animation effect before tile is activated (i.e. exploded).
        /// </summary>
        Preactivated = 6,

        /// <summary>
        /// Animation effect when tile is formed as a result of a match
        /// </summary>
        FormationIntro = 7,

        /// <summary>
        /// Animation effect when tile is formed as a result of a bolt swap with boost tile
        /// </summary>
        FormationByBoltIntro = 8,
        BoltCombo = 9,
        Swap = 10
    }
}