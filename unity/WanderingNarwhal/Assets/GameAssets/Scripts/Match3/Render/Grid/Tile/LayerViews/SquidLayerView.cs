using System.Collections.Generic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class SquidLayerView : SheetTileLayerViewBase, IAlwaysApplicableTileView, ITileLayerEffectSpawnOffset
    {
        private SquidLayerRenderer _renderer;

        private int _currentState;
        private int _currentCount;
        private int _sizeX;
        private int _sizeY;
        private int _initialCount;
        private TileKinds[] _currentColors;
        private bool _isSingle;
        private bool _isRefreshing;
        private readonly List<Vector2> _lastDestroyedOffsets = new(0);
        private Vector3 _cellSize;
        private Coords _mainCellCoords;

        /// <summary>
        /// Fx spawn relative coords (from Main cell).
        /// </summary>
        /// <remarks>
        /// Fx offsets are stored as a stack,
        /// because tile offset may receive multiple hits in a row,
        /// and visualization happens only after simulation is finished.
        /// </remarks>
        public Vector2 FxSpawnOffset
        {
            get
            {
                var count = _lastDestroyedOffsets.Count;
                var result = count > 0 ? _lastDestroyedOffsets[count - 1] : new Vector2();
                if (count > 1)
                {
                    _lastDestroyedOffsets.RemoveAt(count - 1);
                }

                return result;
            }
            private set => _lastDestroyedOffsets.Add(value);
        }

        public SquidLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<SquidLayerRenderer>();
            _cellSize = cellSize;
            _renderer.Init();
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.AdjacentHp);
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            Refresh(tile);
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            if (_isRefreshing) return;

            _isRefreshing = true;
            if (!Applied)
            {
                base.Apply(tile, coords, container, viewsList, isVisible);
                _sizeX = tile.GetParam(TileParamEnum.SizeX);
                _sizeY = tile.GetParam(TileParamEnum.SizeY);
                _currentCount = tile.GetParam(TileParamEnum.SquidsCount);
                _isSingle = tile.GetParam(TileParamEnum.AdjacentHp) > 0;
                _initialCount = _currentCount;

                if (_lastDestroyedOffsets.Capacity < _initialCount)
                {
                    _lastDestroyedOffsets.Capacity = _initialCount;
                }

                _currentState = tile.GetParam(TileParamEnum.SquidsState);
                _currentColors = new TileKinds[Mathf.Min(_sizeX * _sizeY, _initialCount)];
                SquidTileLayer.ExtractTileKindsFromInt(_currentState, _currentColors, _currentCount);
                _renderer.CellSize = _cellSize;
                _renderer.Setup(_sizeX, _sizeY, _currentColors, _isSingle);
                if (_isSingle)
                {
                    //SkinIndex = SquidTileLayer.GetColorNumFromState(_currentState, index: 0);
                    FxSpawnOffset = SquidTileLayer.ConvertNumIndexToCoordsOffset(index: 0, sizeX: _sizeX, sizeY: _sizeY, count: 1);
                }
            }
            else
            {
                Refresh(tile);
            }
            _isRefreshing = false;
        }

        private void Refresh(Tile tile)
        {
            if (_currentState == 0) return;

            var count = tile.GetParam(_isSingle ? TileParamEnum.AdjacentHp : TileParamEnum.SquidsCount);
            var state = tile.GetParam(TileParamEnum.SquidsState);

            if (state != _currentState)
            {
                for (var i = 0; i < SquidTileLayer.MAX_SUBITEMS_COUNT; i++)
                {
                    var currentSubColor = SquidTileLayer.GetColorNumFromState(_currentState, i);
                    var newSubColor = SquidTileLayer.GetColorNumFromState(state, i);
                    if (currentSubColor != newSubColor)
                    {
                        _renderer.SetVisibleDamageAt(i);
                        FxRenderer.PlaySquidHit(_mainCellCoords, FxSpawnOffset);
                        _renderer.PlayDamageReactionTo();
                    }
                }

                _currentCount = count;
                _currentState = state;
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);

            if (_renderer == null) return;

            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                {
                    if ((animParams & TileLayerViewAnimParams.EndGame) == 0)
                    {
                        // Explosion size is passed to the FX as the Skin index.
                        var destroySizeIndex = Mathf.Min(_sizeX, _sizeY);
                        var prm = new FxOptionalParameters { skin = destroySizeIndex };
                        FxRenderer.SpawnConfigurableEffectWithCustomParameters(coords, FxType.SquidDestroy, prm, releaseTime: 3);
                        _renderer.PlayDestroy();
                    }
                }
                    break;
                case TileLayerViewAnims.Unapply:
                    break;
                case TileLayerViewAnims.CustomAppear:
                    _mainCellCoords = coords;
                    _renderer.PlayAppear();
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }
    }
}
