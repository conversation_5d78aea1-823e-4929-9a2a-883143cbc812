using System;
using System.Collections;
using System.Collections.Generic;
using GameAssets.Scripts.Utils;
using UnityEngine;
using DG.Tweening;

namespace BBB.Match3.Renderer
{
    public class MetalBarLayerRenderer : DelayedAppearLayerRenderer
    {
        [Header("Core Components")]
        [SerializeField] private Animator _animator;
        [SerializeField] private RectTransform _tileRectTransform;
        [SerializeField] private RectTransform _content;
        [SerializeField] private RectTransform _bucket;
        [SerializeField] private GameObject _metalBarFirePreview;
        [SerializeField] private RectTransform _metalBar;

        [Header("Segment Configuration")]
        [SerializeField] private GameObject _metalBarEndSegmentPrefab;
        [SerializeField] private Transform _segmentsParent;
        [SerializeField] private int _initialPoolSize = 10;

        [Header("Animation Settings")]
        [SerializeField] private float _destroyDuration = 1f;
        [SerializeField] private float _metalBarShrinkTime = 1f;
        [SerializeField] private float _metalSegmentsMoveTime = 1f;
        [SerializeField] private Vector2 _helpPanelOffset = new(-7f, 0f);
        [SerializeField] private int _helpPanelAngle = 180;

        [Header("Metal Bar Configuration")]
        [SerializeField] private float _metalBarLengthMultiplier = 0.96f;
        [SerializeField] private float _metalBarPerTileOffset = 2f;
        [SerializeField] private float _segmentPositionMultiplier = 2f;
        [SerializeField] private float _lastSegmentOffset = 5f;

        private const float DefaultPivotValue = 0.5f;
        private const float SegmentRemovalAnimationProgress = 0.55f;
        private const int MinimumSegmentsForBar = 1;
        private const int SegmentsOffsetFromBarSize = 2;

        private static readonly int Shake = Animator.StringToHash("Shake");

        private int _orientation;
        private GoPool _segmentsPool;
        private readonly List<GameObject> _activeSegments = new();
        private Coroutine _currentAnimationCoroutine;
        private Vector3 _cachedSegmentPosition = Vector3.zero;
        private bool _currentAnimationRemovedSegment;

        /// <summary>
        /// Gets the destroy duration for external timing calculations
        /// </summary>
        public float DestroyDuration => _destroyDuration;

        private enum MetalBarOrientation
        {
            None,
            Horizontal,
            Vertical
        }

        protected override void Awake()
        {
            base.Awake();
            InitializePool();
        }

        protected override void OnDestroy()
        {
            CleanupResources();
            base.OnDestroy();
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            ClearActiveSegments();
        }

        /// <summary>
        /// Updates the metal bar size and segments based on the tile dimensions
        /// </summary>
        public void UpdateSize(int sizeX, int sizeY, int orientation, bool animate, bool showClip = true)
        {
            if (!ValidateComponents()) return;

            _orientation = orientation;
            SetupContentPivot();

            var startSize = _metalBar.sizeDelta;
            var multiplier = startSize;
            var metalBarDefaultLength = CellSize.x * _metalBarLengthMultiplier;
            var metalBarPerTileLength = metalBarDefaultLength + _metalBarPerTileOffset;
            
            ConfigureFirePreview(showClip);
            EnsurePoolInitialized();

            if (showClip)
            {
                UpdateSegments(sizeX, sizeY, metalBarDefaultLength, metalBarPerTileLength, ref multiplier, animate);
            }
            else
            {
                ClearActiveSegments();
            }

            ConfigureTransforms(orientation, showClip);

            if (sizeX <= 0 || sizeY <= 0) return;

            ApplyBarSizeChange(startSize, multiplier, animate);
        }

        /// <summary>
        /// Plays the destruction animation for the metal bar
        /// </summary>
        public void PlayDestroy(Action onDone)
        {
            UpdateSize(1, 1, _orientation, true);
            StartCoroutine(AutoReleaseOnDestroy(onDone));
        }

        /// <summary>
        /// Plays the preview animation for the metal bar in help panel
        /// </summary>
        public override void PlayPreview()
        {
            UpdateSize(1, 1, _helpPanelAngle, false);
            base.PlayPreview();
        }

        private void UpdateSegments(int sizeX, int sizeY, float metalBarDefaultLength, float metalBarPerTileLength, ref Vector2 multiplier, bool animate)
        {
            if (sizeX <= 0 && sizeY <= 0) return;

            var (metalBarOrientation, count) = GetBarOrientationAndCount(sizeX, sizeY);
            var barSize = CalculateBarSize(metalBarOrientation, count, metalBarDefaultLength, metalBarPerTileLength);
            multiplier.x = barSize;
            var segmentsToCreate = count > MinimumSegmentsForBar ? count - 1 : 0;
            AdjustSegmentCount(segmentsToCreate, animate);
        }

        private void AdjustSegmentCount(int targetSegmentCount, bool animate)
        {
            if (_segmentsPool == null) return;

            if (!animate)
            {
                RemoveExcessSegments(targetSegmentCount);
            }

            AddRequiredSegments(targetSegmentCount);

            if (!animate)
            {
                UpdateSegmentPositions();
            }
        }

        private void RemoveExcessSegments(int targetCount)
        {
            while (_activeSegments.Count > targetCount)
            {
                var lastIndex = _activeSegments.Count - 1;
                var segmentToRemove = _activeSegments[lastIndex];
                if (segmentToRemove != null)
                {
                    segmentToRemove.SetActive(false);
                    _segmentsPool.Release(segmentToRemove);
                }
                _activeSegments.RemoveAt(lastIndex);
            }
        }

        private void AddRequiredSegments(int targetCount)
        {
            while (_activeSegments.Count < targetCount)
            {
                var segment = _segmentsPool.Spawn();
                if (segment != null)
                {
                    _activeSegments.Add(segment);
                    segment.SetActive(true);
                }
            }
        }

        private void UpdateSegmentPositions()
        {
            var totalSegments = _activeSegments.Count;
            for (var i = 0; i < totalSegments; i++)
            {
                var segment = _activeSegments[i];
                if (segment != null)
                {
                    var segmentPosition = CalculateSegmentPosition(i, totalSegments);
                    _cachedSegmentPosition.Set(segmentPosition, 0, 0);
                    segment.transform.localPosition = _cachedSegmentPosition;
                }
            }
        }

        private void ClearActiveSegments()
        {
            if (_segmentsPool == null) return;

            foreach (var segment in _activeSegments)
            {
                if (segment != null)
                {
                    segment.SetActive(false);
                    _segmentsPool.Release(segment);
                }
            }
            _activeSegments.Clear();
        }

        /// <summary>
        /// Removes the first segment during animation when the bar shrinks
        /// </summary>
        private void OnRemoveSegment()
        {
            if (_activeSegments.Count > 0 && _segmentsPool != null)
            {
                var segmentToRemove = _activeSegments[0];
                if (segmentToRemove != null)
                {
                    segmentToRemove.transform.DOKill();
                    segmentToRemove.SetActive(false);
                    _segmentsPool.Release(segmentToRemove);
                    _activeSegments.RemoveAt(0);
                }
            }
        }

        private void ApplyBarSizeChange(Vector2 startSize, Vector2 endSize, bool animate)
        {
            if (animate && _animator != null)
            {
                if (_currentAnimationCoroutine != null)
                {
                    StopCoroutine(_currentAnimationCoroutine);
                    _currentAnimationCoroutine = null;

                    // Only remove a segment if the previous animation didn't already remove one
                    if (_activeSegments.Count > 0 && !_currentAnimationRemovedSegment)
                    {
                        OnRemoveSegment();
                    }
                }

                // Reset the segment removal flag for the new animation
                _currentAnimationRemovedSegment = false;

                _animator.enabled = true;
                _animator.SetTrigger(Shake);
                _currentAnimationCoroutine = StartCoroutine(AnimateBarSizeChange(_metalBar, startSize, endSize, _metalBarShrinkTime));
            }
            else
            {
                _metalBar.sizeDelta = endSize;
            }
        }

        private IEnumerator AnimateBarSizeChange(RectTransform targetObject, Vector2 startSize, Vector2 endSize, float duration)
        {
            if (targetObject == null || duration <= 0f) yield break;

            var elapsedTime = 0f;
            var segmentRemoved = false;
            var totalSegments = _activeSegments.Count;
            
            // Cache positions to prevent hiccups during rapid damage
            var initialSegmentPositions = new Vector3[totalSegments];
            var targetSegmentPositions = new Vector3[totalSegments];
            
            for (var i = 0; i < totalSegments; i++)
            {
                initialSegmentPositions[i] = _activeSegments[i].transform.localPosition;
                var finalSegmentCount = totalSegments - 1;
                var targetIndex = i - 1;
                targetSegmentPositions[i] = new Vector3(CalculateSegmentPosition(targetIndex, finalSegmentCount), 0, 0);
            }

            while (elapsedTime < duration)
            {
                elapsedTime += Time.deltaTime;
                var progress = Mathf.Clamp01(elapsedTime / duration);
                targetObject.sizeDelta = Vector2.Lerp(startSize, endSize, progress);
                
                var currentSegments = _activeSegments.Count;
                for (var i = 0; i < currentSegments; i++)
                {
                    var segment = _activeSegments[i];
                    if (segment != null)
                    {
                        var sourceIndex = segmentRemoved ? i + 1 : i;
                        if (sourceIndex < initialSegmentPositions.Length && sourceIndex < targetSegmentPositions.Length)
                        {
                            var currentPosition = Vector3.Lerp(
                                initialSegmentPositions[sourceIndex], 
                                targetSegmentPositions[sourceIndex], 
                                progress);
                            segment.transform.localPosition = currentPosition;
                        }
                    }
                }

                if (!segmentRemoved && progress >= SegmentRemovalAnimationProgress && totalSegments > 0)
                {
                    OnRemoveSegment();
                    segmentRemoved = true;
                    _currentAnimationRemovedSegment = true;
                }

                yield return null;
            }
            
            targetObject.sizeDelta = endSize;
            _currentAnimationCoroutine = null;
            _currentAnimationRemovedSegment = false;
        }

        private IEnumerator AutoReleaseOnDestroy(Action onDone)
        {
            yield return WaitCache.Seconds(_metalBarShrinkTime);
            onDone.SafeInvoke();
        }

        private static (MetalBarOrientation orientation, int count) GetBarOrientationAndCount(int sizeX, int sizeY)
        {
            if (sizeY == 1 && sizeX > 0) return (MetalBarOrientation.Horizontal, sizeX);
            if (sizeX == 1 && sizeY > 0) return (MetalBarOrientation.Vertical, sizeY);
            return (MetalBarOrientation.None, 0);
        }

        private static float CalculateBarSize(MetalBarOrientation orientation, int count, float metalBarDefaultLength, float metalBarPerTileLength)
        {
            return orientation is MetalBarOrientation.Horizontal or MetalBarOrientation.Vertical
                ? metalBarDefaultLength + (count - SegmentsOffsetFromBarSize) * metalBarPerTileLength
                : metalBarDefaultLength;
        }

        private float CalculateSegmentPosition(int segmentIndex, int totalSegments)
        {
            if (totalSegments <= 0) return 0f;

            var segmentPosition = (segmentIndex + _segmentPositionMultiplier) * CellSize.x;

            if (segmentIndex == totalSegments - 1 && totalSegments > 1)
            {
                segmentPosition -= _lastSegmentOffset;
            }

            return segmentPosition;
        }

        private bool ValidateComponents()
        {
            return _metalBar != null && _content != null && _tileRectTransform != null;
        }

        private void SetupContentPivot()
        {
            _content.pivot = new Vector2(DefaultPivotValue, DefaultPivotValue);
        }

        private void ConfigureFirePreview(bool showClip)
        {
            if (_metalBarFirePreview != null)
            {
                _metalBarFirePreview.SetActive(!showClip);
            }
        }

        private void ConfigureTransforms(int orientation, bool showClip)
        {
            _tileRectTransform.anchoredPosition = showClip ? Vector2.zero : _helpPanelOffset;
            _content.rotation = Quaternion.Euler(0, 0, orientation);

            if (_bucket != null)
            {
                var rotation = _bucket.rotation;
                _bucket.rotation = Quaternion.Euler(rotation.x, rotation.y, 0);
            }
        }

        private void InitializePool()
        {
            if (_segmentsPool == null && _metalBarEndSegmentPrefab != null && _segmentsParent != null)
            {
                _segmentsPool = new GoPool(_metalBarEndSegmentPrefab, _segmentsParent, _initialPoolSize, OnSegmentCreated);
            }
        }

        private void OnSegmentCreated(GameObject segment)
        {
            if (segment != null)
            {
                segment.SetActive(false);
            }
        }

        private void EnsurePoolInitialized()
        {
            if (_segmentsPool == null)
            {
                InitializePool();
            }
        }

        private void CleanupResources()
        {
            ClearActiveSegments();
            _segmentsPool?.Cleanup();
            _segmentsPool = null;
        }
    }
}
