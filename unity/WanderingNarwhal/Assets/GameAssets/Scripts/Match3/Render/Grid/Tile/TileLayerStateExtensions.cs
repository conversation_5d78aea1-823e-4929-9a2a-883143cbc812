using System;
using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public static class TileLayerStateExtensions
    {
        public static int ToPropellerHitPointsCount(this Cell cell)
        {
            int tileHitPoints = cell.HasTile() ? cell.Tile.ToPropellerHitPointsCount() : 0;
            int cellHitPoints = 0;

            cellHitPoints = cell.BackgroundCount;

            return Mathf.Max(tileHitPoints, cellHitPoints);
        }

        public static int ToPropellerHitPointsCount(this Tile tile)
        {
            var layerState = tile.ToLayerState();
            switch (layerState)
            {
                case TileLayerState.Sticker:
                case TileLayerState.Frame:
                case TileLayerState.ColorCrate:
                case TileLayerState.Watermelon:
                case TileLayerState.Sheep:
                case TileLayerState.Hen:
                case TileLayerState.Mole:
                case TileLayerState.Squid:
                case TileLayerState.Bowling:
                case TileLayerState.Bush:
                case TileLayerState.Soda:
                case TileLayerState.Safe:
                case TileLayerState.IceBar:
                case TileLayerState.DynamiteBox:
                case TileLayerState.GiantPinata:
                case TileLayerState.MetalBar:
                case TileLayerState.Shelf:
                case TileLayerState.JellyFish:
                case TileLayerState.GoldenScarab:
                case TileLayerState.TukTuk:
                case TileLayerState.FireWorks:
                case TileLayerState.SlotMachine:
                    return tile.GetParam(TileParamEnum.AdjacentHp);
                case TileLayerState.IceCube:
                    return tile.GetParam(TileParamEnum.IceLayerCount);
                case TileLayerState.Vase:
                    return tile.GetParam(TileParamEnum.VaseLayerCount);
                case TileLayerState.Chained:
                    return tile.GetParam(TileParamEnum.ChainLayerCount);
                case TileLayerState.Egg:
                    return tile.GetParam(TileParamEnum.EggLayerCount);
                case TileLayerState.FlowerPot:
                    return tile.GetParam(TileParamEnum.FlowerPotLayerCount);
                // case TileLayerState.Monkey:
                // case TileLayerState.Toad:
                //     return tile.GetParam(TileParamEnum.RestoresCount);
                // case TileLayerState.Hive:
                //     return tile.GetParam(TileParamEnum.BeeHiveOutOfBeesFlag) != 0 :;
            }

            return tile.Kind.IsColored() ? 0 : 1;
        }

        public static int ToSortOrder(this TileLayerState layerState)
        {
            switch (layerState)
            {
                case TileLayerState.Normal:
                case TileLayerState.Undefined:
                case TileLayerState.Blinking:
                    return 10;
                case TileLayerState.HorizontalLb:
                case TileLayerState.VerticalLb:
                case TileLayerState.MoneyBag:
                case TileLayerState.Penguin:
                case TileLayerState.Egg:
                case TileLayerState.Banana:
                case TileLayerState.Chicken:
                case TileLayerState.Bee:
                case TileLayerState.FlowerPot:
                case TileLayerState.TukTuk:    
                    return 20;
                case TileLayerState.Sticker:
                case TileLayerState.Litter:
                case TileLayerState.Pinata:
                case TileLayerState.Frame:
                case TileLayerState.ColorCrate:
                case TileLayerState.Watermelon:
                case TileLayerState.Mole:
                    return 30;
                case TileLayerState.Skunk:
                case TileLayerState.Hen:
                case TileLayerState.Hive:
                case TileLayerState.Bomb:
                case TileLayerState.Propeller:
                case TileLayerState.ColorBomb:
                case TileLayerState.Squid:
                    return 40;
                case TileLayerState.DropItem:
                    return 50;
                case TileLayerState.StealingHatLabel:
                    return 55;
                case TileLayerState.Vase:
                    return 80;
                case TileLayerState.Bird:
                    return 101;
                case TileLayerState.Sheep:
                case TileLayerState.Monkey:
                case TileLayerState.BigMonkey:
                    // Animals should be higher than Walls, and walls have 100'th order
                    // (walls order, as well as other cell overlays order, is defined in TileResourcesExtra asset). -VK
                    return 102;
                case TileLayerState.GameEventLabel:
                    return 105;
                case TileLayerState.Bowling:
                case TileLayerState.Bush:
                case TileLayerState.Soda:
                case TileLayerState.MagicHat:
                case TileLayerState.Safe:
                case TileLayerState.IceBar:
                case TileLayerState.MetalBar:
                case TileLayerState.DynamiteBox:
                case TileLayerState.GiantPinata:
                case TileLayerState.Shelf:
                case TileLayerState.JellyFish:
                case TileLayerState.GoldenScarab:
                case TileLayerState.FireWorks:
                case TileLayerState.SlotMachine:
                    return 109;
                case TileLayerState.Animal:
                    return 110;
                //Armour Mods should be rendered on top of almost all elements
                case TileLayerState.Chained:
                    return 120;
                case TileLayerState.IceCube:
                    return 121;
                case TileLayerState.Sand:
                    return 122;
                //Toad is the highest layered since the toad animation jumps on board
                //This will prevent the use of Ivy on top of the Toad.
                case TileLayerState.Toad:
                    return 123;
                //Gondola Flag is at layer 126,
                case TileLayerState.Gondola:
                    return 127;
            }

            UnityEngine.Debug.LogError($"Order value not found for {layerState}");

            return 0;
        }

        public static string ToFullAssetPath(this TileLayerState layerState)
        {
            const string prefix = "Prefabs/TileComponentPrefabs/";
            return prefix + ToPrefabName(layerState, noErrors: true);
        }

        public static TileLayerState GetTileLayerFromInt(this int val)
        {
            return (TileLayerState) ((ulong) 1 << val);
        }

        public static string ToPrefabName(this TileLayerState layerState, bool noErrors = false)
        {
            switch (layerState)
            {
                case TileLayerState.Normal:
                case TileLayerState.Undefined:
                    return "TileInstance";
                case TileLayerState.HorizontalLb:
                case TileLayerState.VerticalLb:
                    return "LineBreakerBoost";
                case TileLayerState.Bomb:
                    return "BombBoost";
                case TileLayerState.Propeller:
                    return "PropellerBoost";
                case TileLayerState.ColorBomb:
                    return "ColorBombBoost";
                case TileLayerState.Sticker:
                    return "StickerBlocker";
                case TileLayerState.ColorCrate:
                    return "ColorCrate";
                case TileLayerState.DropItem:
                    return "DropItem";
                case TileLayerState.Litter:
                    return "Litter";
                case TileLayerState.Chained:
                    return "Chain";
                case TileLayerState.Blinking:
                    return "BlinkingTile";
                case TileLayerState.IceCube:
                    return "IceCube";
                case TileLayerState.Sand:
                    return "Sand";
                case TileLayerState.Pinata:
                    return "Pinata";
                case TileLayerState.Frame:
                    return "Frame";
                case TileLayerState.Animal:
                    return "Animal";
                case TileLayerState.Watermelon:
                    return "Watermelon";
                case TileLayerState.Vase:
                    return "Vase";
                case TileLayerState.MoneyBag:
                    return "MoneyBag";
                case TileLayerState.Penguin:
                    return "Penguin";
                case TileLayerState.Egg:
                    return "Egg";
                case TileLayerState.FlowerPot:
                    return "FlowerPot";
                case TileLayerState.Bird:
                    return "BirdTile";
                case TileLayerState.Sheep:
                    return "Sheep";
                case TileLayerState.Banana:
                    return "Banana";
                case TileLayerState.Monkey:
                    return "Monkey";
                case TileLayerState.BigMonkey:
                    return "BigMonkey";
                case TileLayerState.Skunk:
                    return "Skunk";
                case TileLayerState.GameEventLabel:
                    return "GameEventLabel";
                case TileLayerState.Hen:
                    return "HenTile";
                case TileLayerState.Chicken:
                    return "ChickenTile";
                case TileLayerState.Hive:
                    return "HiveTile";
                case TileLayerState.Bee:
                    return "BeeTile";
                case TileLayerState.Mole:
                    return "MoleTile";
                case TileLayerState.Squid:
                    return "SquidTile";
                case TileLayerState.StealingHatLabel:
                    return "DidiHat";
                case TileLayerState.Toad:
                    return "ToadTile";
                case TileLayerState.MagicHat:
                    return "MagicHatTile";
                case TileLayerState.Bowling:
                    return "BowlingTile";
                case TileLayerState.Bush:
                    return "BushTile";
                case TileLayerState.Soda:
                    return "SodaTile";
                case TileLayerState.Safe:
                    return "SafeTile";
                case TileLayerState.IceBar:
                    return "IceBarTile";
                case TileLayerState.DynamiteBox:
                    return "DynamiteBoxTile";
                case TileLayerState.GiantPinata:
                    return "GiantPinataTile";
                case TileLayerState.MetalBar:
                    return "MetalBarTile";
                case TileLayerState.Shelf:
                    return "ShelfTile";
                case TileLayerState.JellyFish:
                    return "JellyFishTile";
                case TileLayerState.GoldenScarab:
                    return "GoldenScarabTile";
                case TileLayerState.Gondola:
                    return "GondolaTile";
                case TileLayerState.TukTuk:
                    return "TukTukTile";
                case TileLayerState.FireWorks:
                    return "FireWorksTile";
                case TileLayerState.SlotMachine:
                    return "SlotMachineTile";
            }

            if (noErrors)
            {
                return "";
            }

            UnityEngine.Debug.LogError($"Prefab name not found for {layerState}");

            return "DummyTile";
        }

        public static IEnumerable<FxType> ToFxTypes(this TileLayerState tileLayerState)
        {
            if ((tileLayerState & TileLayerState.ColorBomb) != 0)
                yield return FxType.LightningBolt;

            if ((tileLayerState & TileLayerState.Sticker) != 0)
            {
                yield return FxType.StickerRemove;
                yield return FxType.StickerSecondLayerRemove;
                yield return FxType.StickerThirdLayerRemove;
            }

            if ((tileLayerState & TileLayerState.ColorCrate) != 0)
            {
                yield return FxType.ColorCrateDestroy;
                yield return FxType.ColorCrateSecondLayerRemove;
                yield return FxType.ColorCrateThirdLayerRemove;
            }

            if ((tileLayerState & TileLayerState.Chained) != 0)
                yield return FxType.ChainRemove;

            if ((tileLayerState & TileLayerState.IceCube) != 0)
                yield return FxType.IceCubeRemoval;

            if ((tileLayerState & TileLayerState.Pinata) != 0)
                yield return FxType.PinataRemoval;

            if ((tileLayerState & TileLayerState.Animal) != 0)
                yield return FxType.AnimalRelease;

            if ((tileLayerState & TileLayerState.Frame) != 0)
            {
                yield return FxType.FrameRemoval;
                yield return FxType.FrameLayerRemoval;
            }

            if ((tileLayerState & TileLayerState.Sand) != 0)
                yield return FxType.SandRemove;

            if ((tileLayerState & TileLayerState.Watermelon) != 0)
                yield return FxType.WatermelonDestroy;

            if ((tileLayerState & TileLayerState.Vase) != 0)
                yield return FxType.VaseDestroy;

            if ((tileLayerState & TileLayerState.MoneyBag) != 0)
            {
                yield return FxType.MoneyBagDestroy;
                yield return FxType.MoneyBagGoal;
            }

            if ((tileLayerState & TileLayerState.Penguin) != 0)
                yield return FxType.PenguinDestroy;

            if ((tileLayerState & TileLayerState.Egg) != 0)
            {
                yield return FxType.EggDestroy;
                yield return FxType.EggLayerRemove;
                yield return FxType.BirdAppear;
                yield return FxType.BirdDestroy;
            }
            if ((tileLayerState & TileLayerState.FlowerPot) != 0)
            {
                yield return FxType.FlowerPotLayerRemove;
                yield return FxType.FlowerPotDestroy;
            }

            if ((tileLayerState & TileLayerState.Bird) != 0)
            {
                yield return FxType.BirdAppear;
                yield return FxType.BirdDestroy;
            }

            if ((tileLayerState & TileLayerState.Sheep) != 0)
            {
                yield return FxType.SheepDestroy;
                yield return FxType.BananaAppear;
                yield return FxType.BananaDestroy;
            }

            if ((tileLayerState & TileLayerState.Banana) != 0)
            {
                yield return FxType.BananaDestroy;
                yield return FxType.BananaAppear;
            }

            if ((tileLayerState & (TileLayerState.Monkey | TileLayerState.BigMonkey)) != 0)
            {
                yield return FxType.BananaAppear;
                yield return FxType.BananaDestroy;
            }

            if ((tileLayerState & TileLayerState.Skunk) != 0)
            {
                yield return FxType.SkunkAttack;
            }

            if ((tileLayerState & TileLayerState.Hen) != 0)
            {
                yield return FxType.ChickenAppear;
                yield return FxType.ChickenDestroy;
            }

            if ((tileLayerState & TileLayerState.Chicken) != 0)
            {
                yield return FxType.ChickenAppear;
                yield return FxType.ChickenDestroy;
            }

            if ((tileLayerState & TileLayerState.Hive) != 0)
            {
                yield return FxType.BeeAppear;
                yield return FxType.BeeDestroy;
            }

            if ((tileLayerState & TileLayerState.Bee) != 0)
            {
                yield return FxType.BeeAppear;
                yield return FxType.BeeDestroy;
            }

            if ((tileLayerState & TileLayerState.Mole) != 0)
            {
                yield return FxType.MoleDestroy;
            }

            if ((tileLayerState & TileLayerState.Squid) != 0)
            {
                yield return FxType.SquidDestroy;
                yield return FxType.SquidGoal;
                yield return FxType.SquidHit;
            }

            if ((tileLayerState & TileLayerState.Toad) != 0)
            {
                yield return FxType.ToadGoal;
            }
            
            if ((tileLayerState & TileLayerState.MagicHat) != 0)
            {
                yield return FxType.MagicHatGoal;
            }

            if ((tileLayerState & TileLayerState.Bush) != 0)
            {
                yield return FxType.GrassAnticipation;
            }

            if ((tileLayerState & TileLayerState.Litter) != 0)
            {
                yield return FxType.LitterDestroy;
            }
            
            if ((tileLayerState & TileLayerState.TukTuk) != 0)
            {
                yield return FxType.TukTukEffect;
            }
            
            if ((tileLayerState & TileLayerState.FireWorks) != 0)
            {
                yield return FxType.FireWorksFlight;
                yield return FxType.FireWorksDestroy;
                yield return FxType.FireWorksRemoveLayer;
                yield return FxType.FireWorksTileDestroy;
            }
        }

        public static TileLayerState ToLayerState(this Tile tile)
        {
            var result = TileLayerState.None;

            if (ReferenceEquals(tile, null))
                return result;

            result |= tile.TileToLayerState();

            return result;
        }

        private static TileLayerState TileToLayerState(this Tile tile)
        {
            return tile.Speciality.ToLayerState() | tile.State.ToLayerState();
        }

        public static bool DoesInclude(this TileLayerState subject, TileLayerState obj)
        {
            var and = subject & obj;
            return and != TileLayerState.None && (subject & and) == obj;
        }

        public static TileState ToModState(this TileLayerState layer)
        {
            switch (layer)
            {
                case TileLayerState.Chained:
                    return TileState.ChainMod;
                case TileLayerState.IceCube:
                    return TileState.IceCubeMod;
                case TileLayerState.Sand:
                    return TileState.SandMod;
                case TileLayerState.Pinata:
                    return TileState.PenguinMod;
                case TileLayerState.Animal:
                    return TileState.AnimalMod;
                case TileLayerState.ColorCrate:
                    return TileState.ColorCrateMod;
                case TileLayerState.Watermelon:
                    return TileState.WatermelonMod;
                case TileLayerState.Vase:
                    return TileState.VaseMod;
                case TileLayerState.MoneyBag:
                    return TileState.MoneyBagMod;
                case TileLayerState.Penguin:
                    return TileState.PenguinMod;
                case TileLayerState.Egg:
                    return TileState.EggMod;
                case TileLayerState.Bird:
                    return TileState.BirdMod;
                case TileLayerState.Sheep:
                    return TileState.SheepMod;
                case TileLayerState.Banana:
                    return TileState.BananaMod;
                case TileLayerState.Monkey:
                    return TileState.MonkeyMod;
                case TileLayerState.BigMonkey:
                    return TileState.BigMonkeyMod;
                case TileLayerState.Skunk:
                    return TileState.SkunkMod;
                case TileLayerState.GameEventLabel:
                    return TileState.GameEventLabel;
                case TileLayerState.StealingHatLabel:
                    return TileState.StealingHatLabel;
                case TileLayerState.Hen:
                    return TileState.HenMod;
                case TileLayerState.Chicken:
                    return TileState.ChickenMod;
                case TileLayerState.Hive:
                    return TileState.BeeMod;
                case TileLayerState.Bee:
                    return TileState.BeeMod;
                case TileLayerState.Mole:
                    return TileState.MoleMod;
                case TileLayerState.Squid:
                    return TileState.SquidMod;
                case TileLayerState.Toad:
                    return TileState.ToadMod;
                case TileLayerState.MagicHat:
                    return TileState.MagicHatMod;
                case TileLayerState.Bowling:
                    return TileState.BowlingMod;
                case TileLayerState.Bush:
                    return TileState.BushMod;
                case TileLayerState.Soda:
                    return TileState.SodaMod;
                case TileLayerState.Safe:
                    return TileState.SafeMod;
                case TileLayerState.FlowerPot:
                    return TileState.FlowerPotMod;
                case TileLayerState.IceBar:
                    return TileState.IceBarMod;
                case TileLayerState.DynamiteBox:
                    return TileState.DynamiteBoxMod;
                case TileLayerState.MetalBar:
                    return TileState.MetalBarMod;
                case TileLayerState.Shelf:
                    return TileState.ShelfMod;
                case TileLayerState.JellyFish:
                    return TileState.JellyFishMod;
                case TileLayerState.GoldenScarab:
                    return TileState.GoldenScarabMod;
                case TileLayerState.Gondola:
                    return TileState.GondolaMod;
                case TileLayerState.TukTuk:
                    return TileState.TukTukMod;
                case TileLayerState.FireWorks:
                    return TileState.FireWorksMod;
                case TileLayerState.SlotMachine:
                    return TileState.SlotMachineMod;
            }

            return TileState.None;
        }

        public static GoalType ToGoalType(this TileLayerState layer)
        {
            switch (layer)
            {
                case TileLayerState.Normal:
                    return GoalType.Green | GoalType.Yellow | GoalType.Blue | GoalType.Red | GoalType.Purple |
                           GoalType.Orange;
                case TileLayerState.Sticker:
                    return GoalType.Stickers;
                case TileLayerState.DropItem:
                    return GoalType.DropItems;
                case TileLayerState.Litter:
                    return GoalType.Litters;
                case TileLayerState.IceCube:
                    return GoalType.IceCubes;
                case TileLayerState.Chained:
                    return GoalType.Chains;
                case TileLayerState.Sand:
                    return GoalType.Sand;
                case TileLayerState.Pinata:
                    return GoalType.Pinata;
                case TileLayerState.Frame:
                case TileLayerState.Animal:
                    return GoalType.Animal;
                case TileLayerState.ColorCrate:
                    return GoalType.ColorCrate;
                case TileLayerState.Watermelon:
                    return GoalType.Watermelon;
                case TileLayerState.Vase:
                    return GoalType.Vase;
                case TileLayerState.MoneyBag:
                    return GoalType.MoneyBag;
                case TileLayerState.Penguin:
                    return GoalType.Penguin;
                case TileLayerState.Egg:
                case TileLayerState.Bird:
                    return GoalType.Bird;
                case TileLayerState.Sheep:
                    return GoalType.Sheep;
                case TileLayerState.Banana:
                case TileLayerState.Monkey:
                case TileLayerState.BigMonkey:
                    return GoalType.Banana;
                case TileLayerState.Skunk:
                    return GoalType.Skunk;
                case TileLayerState.Hen:
                case TileLayerState.Chicken:
                    return GoalType.Chicken;
                case TileLayerState.Hive:
                case TileLayerState.Bee:
                    return GoalType.Bee;
                case TileLayerState.Mole:
                    return GoalType.Mole;
                case TileLayerState.Bowling:
                    return GoalType.BowlingPin;
                case TileLayerState.Bush:
                    return GoalType.Bush;
                case TileLayerState.Soda:
                    return GoalType.SodaBottle;
                case TileLayerState.Safe:
                    return GoalType.Safe;
                case TileLayerState.FlowerPot:
                    return GoalType.FlowerPot;
                case TileLayerState.IceBar:
                    return GoalType.IceBar;
                case TileLayerState.DynamiteBox:
                    return GoalType.DynamiteStick;
                case TileLayerState.GiantPinata:
                    return GoalType.GiantPinata;
                case TileLayerState.MetalBar:
                    return GoalType.MetalBar;
                case TileLayerState.Shelf:
                    return GoalType.Shelf;
                case TileLayerState.JellyFish:
                    return GoalType.JellyFish;
                case TileLayerState.GoldenScarab:
                    return GoalType.GoldenScarab;
                case TileLayerState.MagicHat:
                    return GoalType.MagicHat;
                case TileLayerState.Gondola:
                    return GoalType.Gondola;
                case TileLayerState.TukTuk:
                    return GoalType.TukTuk;
                case TileLayerState.FireWorks:
                    return GoalType.FireWorks;
                case TileLayerState.SlotMachine:
                    return GoalType.SlotMachine;
            }

            return GoalType.None;
        }

        public static TileLayerState ToLayerState(this TileSpeciality spec)
        {
            switch (spec)
            {
                case TileSpeciality.None: return TileLayerState.Normal;
                case TileSpeciality.Sticker: return TileLayerState.Sticker;
                case TileSpeciality.RowBreaker: return TileLayerState.HorizontalLb;
                case TileSpeciality.ColumnBreaker: return TileLayerState.VerticalLb;
                case TileSpeciality.Bomb: return TileLayerState.Bomb;
                case TileSpeciality.BlinkingTile: return TileLayerState.Blinking;
                case TileSpeciality.ColorBomb: return TileLayerState.ColorBomb;
                case TileSpeciality.DropItem: return TileLayerState.DropItem;
                case TileSpeciality.Litter: return TileLayerState.Litter;
                case TileSpeciality.Sand: return TileLayerState.Sand;
                case TileSpeciality.Pinata: return TileLayerState.Pinata;
                case TileSpeciality.Frame: return TileLayerState.Frame;
                case TileSpeciality.ColorCrate: return TileLayerState.ColorCrate;
                case TileSpeciality.Watermelon: return TileLayerState.Watermelon;
                case TileSpeciality.MoneyBag: return TileLayerState.MoneyBag;
                case TileSpeciality.Penguin: return TileLayerState.Penguin;
                case TileSpeciality.Egg: return TileLayerState.Egg;
                case TileSpeciality.Bird: return TileLayerState.Bird;
                case TileSpeciality.Sheep: return TileLayerState.Sheep;
                case TileSpeciality.Banana: return TileLayerState.Banana;
                case TileSpeciality.Monkey: return TileLayerState.Monkey;
                case TileSpeciality.Skunk: return TileLayerState.Skunk;
                case TileSpeciality.Hen: return TileLayerState.Hen;
                case TileSpeciality.Chicken: return TileLayerState.Chicken;
                case TileSpeciality.Hive: return TileLayerState.Hive;
                case TileSpeciality.Bee: return TileLayerState.Bee;
                case TileSpeciality.Mole: return TileLayerState.Mole;
                case TileSpeciality.Squid: return TileLayerState.Squid;
                case TileSpeciality.Toad: return TileLayerState.Toad;
                case TileSpeciality.MagicHat: return TileLayerState.MagicHat;
                case TileSpeciality.Propeller: return TileLayerState.Propeller;
                case TileSpeciality.Bowling: return TileLayerState.Bowling;
                case TileSpeciality.Bush: return TileLayerState.Bush;
                case TileSpeciality.Soda: return TileLayerState.Soda;
                case TileSpeciality.Safe: return TileLayerState.Safe;
                case TileSpeciality.FlowerPot: return TileLayerState.FlowerPot;
                case TileSpeciality.IceBar: return TileLayerState.IceBar;
                case TileSpeciality.DynamiteBox: return TileLayerState.DynamiteBox;
                case TileSpeciality.GiantPinata: return TileLayerState.GiantPinata;
                case TileSpeciality.MetalBar: return TileLayerState.MetalBar;
                case TileSpeciality.Shelf: return TileLayerState.Shelf;
                case TileSpeciality.JellyFish: return TileLayerState.JellyFish;
                case TileSpeciality.GoldenScarab: return TileLayerState.GoldenScarab;
                case TileSpeciality.Gondola: return TileLayerState.Gondola;
                case TileSpeciality.TukTuk: return TileLayerState.TukTuk;
                case TileSpeciality.FireWorks: return TileLayerState.FireWorks;
                case TileSpeciality.SlotMachine: return TileLayerState.SlotMachine;
                case TileSpeciality.BigMonkey: return TileLayerState.BigMonkey;
                default:
                    throw new ArgumentOutOfRangeException("TileSpeciality", spec, null);
            }
        }

        public static TileLayerState ToLayerState(this TileState state)
        {
            return ((state & TileState.ChainMod) != 0 ? TileLayerState.Chained : TileLayerState.None)
                   | ((state & TileState.IceCubeMod) != 0 ? TileLayerState.IceCube : TileLayerState.None)
                   | ((state & TileState.ChainMod) != 0 ? TileLayerState.Chained : TileLayerState.None)
                   | ((state & TileState.AnimalMod) != 0 ? TileLayerState.Animal : TileLayerState.None)
                   | ((state & TileState.SandMod) != 0 ? TileLayerState.Sand : TileLayerState.None)
                   | ((state & TileState.ColorCrateMod) != 0 ? TileLayerState.ColorCrate : TileLayerState.None)
                   | ((state & TileState.VaseMod) != 0 ? TileLayerState.Vase : TileLayerState.None)
                   | ((state & TileState.MoneyBagMod) != 0 ? TileLayerState.MoneyBag : TileLayerState.None)
                   | ((state & TileState.PenguinMod) != 0 ? TileLayerState.Penguin : TileLayerState.None)
                   | ((state & TileState.EggMod) != 0 ? TileLayerState.Egg : TileLayerState.None)
                   | ((state & TileState.BirdMod) != 0 ? TileLayerState.Bird : TileLayerState.None)
                   | ((state & TileState.SheepMod) != 0 ? TileLayerState.Sheep : TileLayerState.None)
                   | ((state & TileState.BananaMod) != 0 ? TileLayerState.Banana : TileLayerState.None)
                   | ((state & TileState.MonkeyMod) != 0 ? TileLayerState.Monkey : TileLayerState.None)
                   | ((state & TileState.BigMonkeyMod) != 0 ? TileLayerState.BigMonkey : TileLayerState.None)
                   | ((state & TileState.SkunkMod) != 0 ? TileLayerState.Skunk : TileLayerState.None)
                   | ((state & TileState.GameEventLabel) != 0 ? TileLayerState.GameEventLabel : TileLayerState.None)
                   | ((state & TileState.HenMod) != 0 ? TileLayerState.Hen : TileLayerState.None)
                   | ((state & TileState.ChickenMod) != 0 ? TileLayerState.Chicken : TileLayerState.None)
                   | ((state & TileState.HiveMod) != 0 ? TileLayerState.Hive : TileLayerState.None)
                   | ((state & TileState.BeeMod) != 0 ? TileLayerState.Bee : TileLayerState.None)
                   | ((state & TileState.MoleMod) != 0 ? TileLayerState.Mole : TileLayerState.None)
                   | ((state & TileState.SquidMod) != 0 ? TileLayerState.Squid : TileLayerState.None)
                   | ((state & TileState.StealingHatLabel) != 0 ? TileLayerState.StealingHatLabel : TileLayerState.None)
                   | ((state & TileState.ToadMod) != 0 ? TileLayerState.Toad : TileLayerState.None)
                   | ((state & TileState.BowlingMod) != 0 ? TileLayerState.Bowling : TileLayerState.None)
                   | ((state & TileState.BushMod) != 0 ? TileLayerState.Bush : TileLayerState.None)
                   | ((state & TileState.IceBarMod) != 0 ? TileLayerState.IceBar : TileLayerState.None)
                   | ((state & TileState.SodaMod) != 0 ? TileLayerState.Soda : TileLayerState.None)
                   | ((state & TileState.MagicHatMod) != 0 ? TileLayerState.MagicHat : TileLayerState.None)
                   | ((state & TileState.SafeMod) != 0 ? TileLayerState.Safe : TileLayerState.None)
                   | ((state & TileState.FlowerPotMod) != 0 ? TileLayerState.FlowerPot : TileLayerState.None)
                   | ((state & TileState.DynamiteBoxMod) != 0 ? TileLayerState.DynamiteBox : TileLayerState.None)
                   | ((state & TileState.GiantPinataMod) != 0 ? TileLayerState.GiantPinata : TileLayerState.None)
                   | ((state & TileState.MetalBarMod) != 0 ? TileLayerState.MetalBar : TileLayerState.None)
                   | ((state & TileState.ShelfMod) != 0 ? TileLayerState.Shelf : TileLayerState.None)
                   | ((state & TileState.JellyFishMod) != 0 ? TileLayerState.JellyFish : TileLayerState.None)
                   | ((state & TileState.GoldenScarabMod) != 0 ? TileLayerState.GoldenScarab : TileLayerState.None)
                   | ((state & TileState.GondolaMod) != 0 ? TileLayerState.Gondola : TileLayerState.None)
                   | ((state & TileState.TukTukMod) != 0 ? TileLayerState.TukTuk : TileLayerState.None)
                   | ((state & TileState.FireWorksMod) != 0 ? TileLayerState.FireWorks : TileLayerState.None)
                   | ((state & TileState.SlotMachineMod) != 0 ? TileLayerState.SlotMachine : TileLayerState.None);
        }
    }

    [AttributeUsage(AttributeTargets.Field)]
    public class TileLayerStateNameAttribute : UnityEngine.PropertyAttribute
    {
    }

#if UNITY_EDITOR

    [UnityEditor.CustomPropertyDrawer(typeof(TileLayerStateNameAttribute))]
    public class TileMechanicHelpInfoDrawer : UnityEditor.PropertyDrawer
    {
        private static UnityEngine.GUIStyle labelStyle = UnityEngine.GUI.skin.GetStyle("miniLabel");

        public override float GetPropertyHeight(UnityEditor.SerializedProperty property, UnityEngine.GUIContent label)
        {
            return UnityEditor.EditorGUI.GetPropertyHeight(property, label, includeChildren: true);
        }

        public override void OnGUI(UnityEngine.Rect position, UnityEditor.SerializedProperty property,
            UnityEngine.GUIContent label)
        {
            UnityEditor.EditorGUI.PropertyField(position, property, label);
            TileLayerState layerStateValue = property.intValue.GetTileLayerFromInt();
            const int width = 120;
            var labelPosition =
                new UnityEngine.Rect(new UnityEngine.Vector2(position.xMax - width - 10f, position.yMin),
                    new UnityEngine.Vector2(width, 20f));
            UnityEditor.EditorGUI.LabelField(labelPosition, layerStateValue.ToString(), labelStyle);
        }
    }
#endif
}
