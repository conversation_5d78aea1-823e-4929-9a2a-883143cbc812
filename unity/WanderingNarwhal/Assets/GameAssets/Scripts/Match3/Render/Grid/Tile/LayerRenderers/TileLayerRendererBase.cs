using System;
using System.Collections.Generic;
using DG.Tweening;
using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public abstract class TileLayerRendererBase : BbbMonoBehaviour
    {
        [NonSerialized] public int LayerIndex;
        [NonSerialized] public int AnimationTrigger;

        [SerializeField] public string LayerIndexName;
        [SerializeField] public string AnimationTriggerName;

        [SerializeField] public SkeletonGraphic SpineAnimator;
        [SerializeField] public Animator Animator;
        [SerializeField] public List<FxType> FXTypes;
        [SerializeField] public List<float> FXDestroyDurations;
        [SerializeField] public List<string> SoundIds;

        [SerializeField] public RectTransform Content;
        [SerializeField] public float TileBaseMinOffset;
        [SerializeField] public float TileBaseMaxOffset;
        [SerializeField] public float TileHelpMinOffset;
        [SerializeField] public float TileHelpMaxOffset;
        
        protected static readonly int IntroState = Animator.StringToHash("Intro");
        protected static readonly int IntroByBoltState = Animator.StringToHash("IntroByBoltState");
        protected static readonly int ComboTriggerHash = Animator.StringToHash("ComboTrigger");
        protected static readonly int FormationTriggerHash = Animator.StringToHash("FormationTrigger");
        protected static readonly int FormationByBoltTriggerHash = Animator.StringToHash("FormationByBoltTrigger");
        protected static readonly int BoltComboTriggerHash = Animator.StringToHash("BoltComboTrigger");
        private Sequence _sequence;
        public Vector2 CellSize { get; set; }

        [SerializeField]
        protected TapFeedbackSettings _doTweenTapFeedbackSettings = new()
        {
            IsEnabled = true,
            Duration = 0.3f,
            ScaleMultiplier = 1.2f
        };

        public virtual void Init()
        {
            
        }
        
        public virtual void PlayAppear()
        {
            
        }
        
        public virtual void Show()
        {
            
        }

        public virtual void PlayTapFeedback(ITileLayerView layerView)
        {
            if (!_doTweenTapFeedbackSettings.IsEnabled) return;
            
            _sequence?.Kill(true);
            var tileTransform = transform;
            var cachedScale = tileTransform.localScale;
            
            _sequence = TileAnimator.PlayFeedbackOnTileView(tileTransform,
                _doTweenTapFeedbackSettings.Duration,
                _doTweenTapFeedbackSettings.ScaleMultiplier);

            var canvasList = new List<(Canvas canvas, int beforeLayer)>();

            var spriteRendererList = new List<(SpriteRenderer spriteRenderer, int beforeLayer)>();
            
            var spriteMaskList = new List<(SpriteMask spriteMask, int beforeLayer)>();

            var overlayLayer = SortingLayer.NameToID("Overlay");

            if (layerView == null || _sequence == null) return;
            {
                foreach (var canvas in GetComponentsInChildren<Canvas>())
                {
                    var beforeLayer = canvas.sortingLayerID;
                    canvas.sortingLayerID = overlayLayer;
                    canvasList.Add((canvas, beforeLayer));
                }

                foreach (var spriteRenderer in GetComponentsInChildren<SpriteRenderer>())
                {
                    var beforeLayer = spriteRenderer.sortingLayerID;
                    spriteRenderer.sortingLayerID = overlayLayer;
                    spriteRendererList.Add((spriteRenderer, beforeLayer));
                }
                
                foreach (var spriteMask in GetComponentsInChildren<SpriteMask>())
                {
                    var beforeLayer = spriteMask.frontSortingLayerID;
                    spriteMask.frontSortingLayerID = overlayLayer;
                    spriteMaskList.Add((spriteMask, beforeLayer));
                }

                _sequence.OnComplete(() =>
                {
                    tileTransform.localScale = cachedScale;
                    
                    foreach (var (canvas, beforeLayer) in canvasList)
                        canvas.sortingLayerID = beforeLayer;

                    foreach (var (spriteRenderer, beforeLayer) in spriteRendererList)
                        spriteRenderer.sortingLayerID = beforeLayer;

                    foreach (var (spriteMask, beforeLayer) in spriteMaskList)
                        spriteMask.frontSortingLayerID = beforeLayer;


                    _sequence = null;
                });
            }
        }

        /// <summary>
        /// Preview state of Tile that is used when tile is displayed on
        /// UI panel for mechanic description (Help Panel).
        /// </summary>
        public virtual void PlayPreview()
        {
        }

        /// <summary>
        /// Preview state of Tile that is used when tile is displayed on
        /// start level and level desciption UI panels as Visitor tile.
        /// </summary>
        public virtual void PlayVisitorPreview()
        {
        }

        /// <summary>
        /// Set tile rect transform size to be exact size of the cell.
        /// Works only if CellSize was provided for tile when view is instantiated.
        /// Used only for 2x2 tiles.
        /// </summary>
        public void FitBaseRectToExactCellSize()
        {
            if (CellSize.x > 0)
            {
                var rootRect = GetComponent<RectTransform>();
                var parentScale = rootRect.parent.localScale;
                var parentScaleFactorX = 1f / parentScale.x;
                var parentScaleFactorY = 1f / parentScale.y;
                rootRect.anchorMin = new Vector2(0.5f, 0.5f);
                rootRect.anchorMax = new Vector2(0.5f, 0.5f);
                rootRect.anchoredPosition = new Vector2();
                rootRect.sizeDelta = new Vector2(CellSize.x * parentScaleFactorX, CellSize.y * parentScaleFactorY);
            }
        }
    }

    [Serializable]
    public struct TapFeedbackSettings
    {
        public bool IsEnabled;
        public float Duration;
        public float ScaleMultiplier;
    }
}