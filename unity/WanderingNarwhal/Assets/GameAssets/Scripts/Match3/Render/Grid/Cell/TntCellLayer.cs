using BBB.CellTypes;

namespace BBB.Match3.Renderer
{
    public sealed class TntCellLayer : CellLayerBase
    {
        public int Count { get; private set; }

        public int SizeX { get; private set; }

        public int SizeY { get; private set; }

        public TileKinds Kind { get; private set; }

        public TntTargetType Target { get; private set; }

        public override CellOverlayType OverlayType => CellOverlayType.TntOverlay;

        public override bool IsAnimated => true;

        public override CellLayerState State => CellLayerState.Tnt;

        protected override bool IsCondition(Cell cell)
        {
            return cell.IsAnyOf(CellState.Tnt);
        }

        protected override void Customize(Cell cell)
        {
            base.Customize(cell);
            Count = cell.TntCount;
            Kind = cell.TntTarget == TntTargetType.Simple ? cell.TntKind : TileKinds.Undefined;
            SizeX = cell.SizeX;
            SizeY = cell.SizeY;
            Target = cell.TntTarget;
            Coords = cell.Coords;
        }
    }
}