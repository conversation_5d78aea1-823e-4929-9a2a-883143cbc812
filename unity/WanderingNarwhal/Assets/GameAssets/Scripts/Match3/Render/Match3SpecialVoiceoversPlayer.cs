using System.Collections.Generic;
using BBB.DI;
using BebopBee.Core.Audio;
using FBConfig;

namespace BBB.Match3.Renderer
{
    //don't change the names of enum values, they are connected to remote config uids
    public enum M3SpecialVoiceovers
    {
        None = 0,
        ThingsLookingUp = 1,
        LevelWon = 2,
        LevelWonNearLoss = 3,
        BoardCleanUp = 4,
        BoltBolt = 5,
        PowerUpCombine = 6
    }

    public class Match3SpecialVoiceoversPlayer : IContextInitializable
    {
        private IConfig _config;
        private AwardRenderer _awardRenderer;

        public void InitializeByContext(IContext context)
        {
            if (_awardRenderer == null)
            {
                _awardRenderer = context.Resolve<AwardRenderer>();
            }

            _config ??= context.Resolve<IConfig>();
        }

        public void Play(M3SpecialVoiceovers voiceover, bool skipText, out string textUid)
        {
            textUid = string.Empty;
            var specialVoiceoversConfigs = _config.Get<Match3SpecialVoiceoversConfig>();
            if (!specialVoiceoversConfigs.TryGetValue(voiceover.ToString(), out var config))
                return;
            
            if (config.SoundIdsLength == 0)
            {
                UnityEngine.Debug.LogErrorFormat("Match3SpecialVoiceoversConfig {0} has no voiceovers", config.Uid);
                return;
            }

            var roll = UnityEngine.Random.Range(0f, 1f);

            if (roll > config.ChanceToPlay)
                return;

            var randomIndex = UnityEngine.Random.Range(0, config.SoundIdsLength);
            textUid = config.TextIdsLength == 0 ? null : config.TextIds(randomIndex);
            var soundId = config.SoundIds(randomIndex);
            if (textUid.IsNullOrEmpty() || skipText)
                return;
            
            var victory = voiceover is M3SpecialVoiceovers.LevelWon or M3SpecialVoiceovers.LevelWonNearLoss;
            _awardRenderer.RenderAward(textUid, soundId, victory);
        }
    }
}