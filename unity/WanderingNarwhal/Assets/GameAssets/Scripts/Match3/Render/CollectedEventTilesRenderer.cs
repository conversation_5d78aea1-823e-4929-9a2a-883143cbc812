using BBB.DI;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public sealed class CollectedEventTilesRenderer : RendererBase
    {
        [SerializeField] private float _delayBeforeAppear = 0.1f;
        
        private RendererContainers _rendererContainers;
        private IGridController _gridController;
        private M3Settings _m3Settings;

        public override void InitializeByContext(IContext context)
        {
            base.InitializeByContext(context);

            _gridController = context.Resolve<IGridController>();
            _m3Settings = context.Resolve<M3Settings>();
            _rendererContainers = context.Resolve<RendererContainers>();
        }

        // public void SpawnCollectedAmount(IVisualActionCollectedEventTiles actionCollectedEventTiles)
        // {
        //     if (actionCollectedEventTiles.Amount > 0)
        //     {
        //         if (_delayBeforeAppear <= 0f)
        //         {
        //             DoSpawn(actionCollectedEventTiles);
        //         }
        //         else
        //         {
        //             Rx.Invoke(_delayBeforeAppear, Action);
        //
        //             void Action(long _)
        //             {
        //                 DoSpawn(actionCollectedEventTiles);
        //             }
        //         }
        //     }
        // }
        //
        // private void DoSpawn(IVisualActionCollectedEventTiles actionCollectedEventTiles)
        // {
        //     var amount = actionCollectedEventTiles.Amount;
        //     var tileKind = actionCollectedEventTiles.TileKind;
        //
        //     var amountText = _rendererContainers.SpawnFx<CollectEventTileAmountFleeting>(FxType.CollectEventTile);
        //     var amountGo = amountText.gameObject;
        //     amountGo.transform.localPosition = _gridController.ToLocalPosition(actionCollectedEventTiles.Coords);
        //     amountText.SetAmount(amount);
        //
        //     var kindMaterial = _boardSettings.GetScoreMaterial(tileKind);
        //     if (kindMaterial != null)
        //         amountText.SetMaterial(kindMaterial);
        // }
    }
}