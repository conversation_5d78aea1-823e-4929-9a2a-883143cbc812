using System;
using Assets.UltimateIsometricToolkit.Scripts.External;
using BBB.DI;
using BBB.UI.Core;
using JetBrains.Annotations;
using Spine.Unity;
using UnityEngine;

namespace BBB.GameAssets.Scripts.Match3.Render
{
    public class OverlayEffect : ContextedUiBehaviour
    {
        public event Action AnimationCompleted;

        [SerializeField] private Canvas _canvas;
        [SerializeField] private Animator _animator;
        [SerializeField] private SkeletonGraphic[] _skeletons;
        [SerializeField] private ParticleSystem[] _particleSystems;
        [SerializeField] private LocalizedTextPro _titleText;
        
        public Animator Animator => _animator;

        protected override void InitWithContextInternal(IContext context)
        {
        }

        public void SetTitle(string textUid)
        {
            if (_titleText != null && !textUid.IsNullOrEmpty())
            {
                _titleText.SetTextId(textUid);
            }
        }

        public void Launch()
        {
            LazyInit();

            _canvas.enabled = true;
            _animator.enabled = true;

            if (_skeletons != null)
            {
                foreach (var skeleton in _skeletons)
                {
                    if (skeleton == null) continue;
                    skeleton.Initialize(true);
                }
            }

            _particleSystems.ForEach(ps =>
            {
                ps.gameObject.SetActive(true);
                ps.Play(false);
            });
        }

        public void StopAndClear()
        {
            if (_canvas != null)
            {
                _canvas.enabled = false;
            }

            if (_animator != null)
            {
                _animator.enabled = false;
                _animator.Rebind();
            }

            if (_skeletons != null)
                foreach (var skeleton in _skeletons)
                {
                    if (skeleton == null) continue;
                    skeleton.AnimationState.ClearTracks();
                    skeleton.Skeleton.SetToSetupPose();
                }

            if (_particleSystems != null)
                _particleSystems.ForEach(ps =>
                {
                    if (ps != null)
                    {
                        ps.Stop(false);
                        ps.Clear();
                        ps.gameObject.SetActive(false);
                    }
                });
        }

        [UsedImplicitly]
        private void InvokeAnimationCompleted()
        {
            AnimationCompleted?.Invoke();
        }
    }
}