using System;
using System.Collections.Generic;
using System.Text;
using BBB.Core;
using BBB.DI;
using BBB.UI.Level.ScriptableObjects;
using BebopBee.Core;
using UnityEngine;
using BebopBee.Core.Audio;
using Core.Configs;
using DG.Tweening;
using FBConfig;

namespace BBB.Match3.Renderer
{
    public class AwardRenderer : BbbMonoBehaviour, IContextInitializable
    {
        private static readonly Type[] RequiredConfigs = {
            typeof(Match3AwardsConfig)
        };

        [SerializeField] private float _secondsToHideAward = 5f;
        [SerializeField] private float _secondsToPlaySound = 1f;
        [SerializeField] private List<LocalizedTextPro> _localizedTextPro;

        private Canvas _canvas;
        private Animator _animator;
        private readonly List<Match3AwardsConfig> _awardConfigs = new ();
        private AwardsSettings _settings;
        private GameController _gameController;
        
        private Match3AwardsConfig _lastShownAward;
        
        private Tween _hideTween;
        
        private Tween _playSoundTween;

        public void InitializeByContext(IContext context)
        {
            if (_gameController == null)
            {
                _gameController = context.Resolve<GameController>();
            }

            if (_awardConfigs.Count == 0)
            {
                var config = context.Resolve<IConfig>();
                SetupAwardConfig(config);
                Config.OnConfigUpdated -= SetupAwardConfig;
                Config.OnConfigUpdated += SetupAwardConfig;
            }

            if (_settings == null)
            {
                _settings = Resources.Load<AwardsSettings>("Settings/" + "AwardsSettings");
                _canvas = GetComponent<Canvas>();
                _animator = GetComponent<Animator>();
            }

            _canvas.enabled = false;
            
            _hideTween?.Kill();
            _hideTween = null;
            
            _playSoundTween?.Kill();
            _playSoundTween = null;
            
            _animator.Rebind();
            _animator.Update(0f);
        }

        public void ResetDefaults()
        {
            _lastShownAward = default;
        }

        private void SetupAwardConfig(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            _awardConfigs.Clear();
            var awardConfigsDict = config.Get<Match3AwardsConfig>();
            if (awardConfigsDict != null)
            {
                _awardConfigs.AddRange(awardConfigsDict.Values);
                _awardConfigs.Sort((a, b) => a.MatchesComboCount.CompareTo(b.MatchesComboCount));
            }
            else
            {
                UnityEngine.Debug.LogError("AwardRenderer: AwardConfigDict is null");
            }
        }

        private readonly List<Match3AwardsConfig> _suitableAwardsTempList = new (5);
        private static readonly int ShowTriggerVictory = Animator.StringToHash("ShowTrigger_Victory");

        private void PickAwards(int matchesCount, List<Match3AwardsConfig> outList)
        {
            outList.Clear();

            foreach (var award in _awardConfigs)
            {
                if (award.Equals(_lastShownAward))
                    continue;

                if (matchesCount == award.MatchesComboCount)
                {
                    outList.Add(award);
                }
            }
        }

        public void RenderAward(string textId, string soundId, bool victory)
        {
            ShowAward(textId, victory, soundId);
        }

        public void RenderAwardForCombo(int matchesCount)
        {
            if (_gameController.GameEnded || _canvas.enabled)
                return;

            var workingMatchesCount = matchesCount;

            while (workingMatchesCount > 0)
            {
                PickAwards(workingMatchesCount, _suitableAwardsTempList);

                if (_suitableAwardsTempList.Count > 0)
                    break;

                workingMatchesCount--;
            }

            if (_suitableAwardsTempList.Count == 0)
            {
                UnityEngine.Debug.LogWarningFormat("No suitable award found for matches count = {0}", matchesCount);
                return;
            }

            var chanceSum = 0f;
            foreach (var item in _suitableAwardsTempList)
            {
                chanceSum += item.ChanceToPlay;
            }

            var nullChance = 1f - chanceSum;

            if (nullChance >= 0f)
            {
                _suitableAwardsTempList.Add(default);
            }
            else
            {
                var sb = new StringBuilder();

                foreach (var award in _suitableAwardsTempList)
                {
                    const string space = " ";
                    if (sb.Length > 0)
                    {
                        sb.Append(space);
                    }
                    sb.Append(award.Uid);
                }

                BDebug.LogError(LogCat.Match3, $"Sum of chances of {sb} is > 1");
            }

            var selectedAward = _suitableAwardsTempList.GetRandomItemByWeight(c => !c.IsNull() && !c.ChanceToPlay.IsNull() ? c.ChanceToPlay : nullChance);

            if (!selectedAward.IsNull())
            {
                var settingsInstance = _settings.FindByUid(selectedAward.Uid);

                if (settingsInstance == null)
                {
                    UnityEngine.Debug.LogErrorFormat("Award settings instance not found for {0}", selectedAward.Uid);
                    return;
                }

                ShowAward(settingsInstance.LocalizedTextId, false, settingsInstance.SoundId, settingsInstance.SecondarySoundId);
            }

            _lastShownAward = selectedAward;

            _suitableAwardsTempList.Clear();
        }

        private void ShowAward(string textId, bool victory, params string[] soundIds)
        {
            if (_localizedTextPro == null || _localizedTextPro.Count == 0 || !isActiveAndEnabled) 
                return;
           
            if (victory) //SIMPLIFICATIONS awards: only sound for all except victory awards
            {
                foreach (var text in _localizedTextPro)
                {
                    text.SetTextId(textId);
                }
                _canvas.enabled = true;
                _animator.Rebind();
                _animator.SetTrigger(ShowTriggerVictory);
                
                _hideTween?.Kill();
                _hideTween = Rx.Invoke(_secondsToHideAward, _ =>
                {
                    if (_canvas != null)
                    {
                        _canvas.enabled = false;
                    }
                    
                    _hideTween = null;
                });
            }


            if (soundIds is { Length: > 0 })
            {
                _playSoundTween?.Kill();

                _playSoundTween = Rx.Invoke(_secondsToPlaySound, _ =>
                {
                    foreach (var soundId in soundIds)
                    {
                        AudioProxy.PlaySound(soundId, false, false);
                    }

                    _playSoundTween = null;
                });
            }
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            ResetTweener();
            if (_canvas != null)
            {
                _canvas.enabled = false;
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            ResetTweener();
            Config.OnConfigUpdated -= SetupAwardConfig;
        }

        private void ResetTweener()
        {
            if (_hideTween != null)
            {
                _hideTween.Kill();
                _hideTween = null;
            }

            if (_playSoundTween != null)
            {
                _playSoundTween.Kill();
                _playSoundTween = null;
            }
        }
    }
}