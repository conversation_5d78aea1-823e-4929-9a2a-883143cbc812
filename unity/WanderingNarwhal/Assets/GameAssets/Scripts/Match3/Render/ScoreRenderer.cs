using System.Collections;
using BBB.DI;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using GameAssets.Scripts.Match3.Settings;
using GameAssets.Scripts.Utils;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public sealed class ScoreRenderer : RendererBase
    {
        [SerializeField] private float _maxRandomTimeOffset = 0.5f;
        
        private RendererContainers _rendererContainers;
        private IGridController _gridController;
        private M3Settings _m3Settings;
        private ICoroutineExecutor _coroutineExecutor;

        public override void InitializeByContext(IContext context)
        {
            base.InitializeByContext(context);

            _coroutineExecutor = context.Resolve<ICoroutineExecutor>();
            _gridController = context.Resolve<IGridController>();
            _m3Settings = context.Resolve<M3Settings>();
            _rendererContainers = context.Resolve<RendererContainers>();
        }

        public void SpawnScore(ActionScoreText actionScoreText)
        {
            if (actionScoreText.Score > 0)
            {
                _coroutineExecutor.StartCoroutine(SpawnScoreRoutine(actionScoreText));
            }
        }

        private IEnumerator SpawnScoreRoutine(ActionScoreText actionScoreText)
        {
            var randomWaitTime = Random.Range(0f, _maxRandomTimeOffset);
            yield return WaitCache.Seconds(randomWaitTime);
            
            SpawnScoreNow(actionScoreText);
        }

        private void SpawnScoreNow(ActionScoreText actionScoreText)
        {
            var score = actionScoreText.Score;
            var multiplier = actionScoreText.ComboMultiplier;
            var assignRandomColor = actionScoreText.AssignRandomColor;
            var tileKind = actionScoreText.TileKind;

            var scoreText = _rendererContainers.SpawnFx<ScoreTextFleeting>(FxType.ScoreText);
            var scoreGo = scoreText.gameObject;
            scoreGo.transform.localPosition = _gridController.ToLocalPosition(actionScoreText.Coords);
            scoreText.SetScore(score, multiplier);

            if (assignRandomColor)
            {
                var randomMaterial = _m3Settings.GetRandomScoreMaterial();
                scoreText.SetMaterial(randomMaterial);
            }
            else
            {
                var kindMaterial = _m3Settings.GetScoreMaterial(tileKind);
                if(kindMaterial != null)
                    scoreText.SetMaterial(kindMaterial);
            }
        }
    }
}