using System.Collections.Generic;
using UnityEngine;
using BBB.DI;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Renderer
{
    public class CellView : IView, IRevealableBoardElement
    {
        private readonly GameObject _gameObject;
        private readonly RectTransform _rectTransform;
        private readonly List<CellLayerView> _views = new List<CellLayerView>();
        private readonly List<CellLayerView> _renderedViews = new List<CellLayerView>();
        private readonly ICellController _controller;
        private readonly TilesResources _tilesResources;
        private Dictionary<CellOverlayType, GoPool> _cellOverlayPools;
        
        public GameObject GameObject => _gameObject;

        public CellLayerState State {
            get
            {
                CellLayerState result = CellLayerState.None;
                foreach (var overlay in _renderedViews)
                {
                    result |= overlay.State;
                }
                return result;
            }
        }

        public CellView(IContext context, GameObject gameObject, Vector2 position, Dictionary<CellOverlayType, GoPool> cellOverlayPools)
        {
            _controller = context.Resolve<ICellController>();
            _gameObject = gameObject;
            _tilesResources = context.Resolve<TilesResources>();
            _rectTransform = gameObject.GetComponent<RectTransform>();
            _rectTransform.localPosition = position;
            _rectTransform.sizeDelta = _tilesResources.CellSizeBox;
            _cellOverlayPools = cellOverlayPools;
        }

        public CellLayerView GetView(CellLayerState state)
        {
            foreach (var view in _renderedViews)
            {
                if ((view.State & state) != 0)
                {
                    return view;
                }
            }

            return null;
        }

        public void Reveal(float time)
        {
            foreach (var view in _renderedViews)
                view.Reveal(time);
        }

        public void Hide()
        {
            foreach (var view in _renderedViews)
                view.Hide();
        }
        
        public void HideForTnt()
        {
            foreach (var view in _renderedViews)
                view.HideForTnt();
        }

        public Vector2 GetPosition()
        {
            return _gameObject.transform.position;
        }

        public void Animate(CellAnimation anim)
        {
            foreach(var view in _renderedViews)
                view.Animate(anim);
        }

        public void OnTileMatchedNearCell()
        {
            foreach (var view in _renderedViews)
            {
                view.OnTileDestroyedNear();
            }
        }

        public void OnTileEnteredCellCell()
        {
            foreach (var view in _renderedViews)
            {
                view.OnTileEntered();
            }
        }

        public void OnTileTap()
        {
            foreach (var view in _renderedViews)
            {
                view.OnTileTap();
            }
        }

        public void Update(IContext context, Cell cell)
        {
            _renderedViews.Clear();

            var i = 0;
            var validatedLayers = _controller.ValidateLayers(cell);

            foreach (var cellState in validatedLayers)
            {
                var layer = _controller.GetLayer(cellState);
                CellLayerView cellLayerView = null;

                foreach (var view in _views)
                {
                    if (view.State != cellState) continue;
                    
                    cellLayerView = view;
                    break;
                }

                if (cellLayerView == null)
                {
                    foreach (var view in _views)
                    {
                        if (view.Applied) continue;
                        
                        cellLayerView = view;
                        break;
                    }
                }

                cellLayerView ??= MakeCellImage(layer.OverlayType != CellOverlayType.None);

                if (!cellLayerView.Applied)
                {
                    cellLayerView.Apply(cellState, layer, context);
                }

                cellLayerView.Update(i++, layer);
                _renderedViews.Add(cellLayerView);
            }

            foreach (var view in _views)
            {
                if (!_renderedViews.Contains(view))
                {
                    view.UnApply();
                }
            }
        }
        
        private CellLayerView MakeCellImage(bool overlayImage)
        {
            if (overlayImage)
            {
                var fakeLayerView = new CellLayerView(null, null, _rectTransform, _cellOverlayPools);
                _views.Add(fakeLayerView);
                return fakeLayerView;
            }

            UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{_tilesResources.CellImagePrefab.name}]");
            var spriteRenderer = Object.Instantiate(_tilesResources.CellImagePrefab, _rectTransform, false);
            UnityEngine.Profiling.Profiler.EndSample();
            var rectTransform = (RectTransform)spriteRenderer.transform;
            rectTransform.anchoredPosition = Vector2.zero;
            rectTransform.sizeDelta = _tilesResources.CellSizeBox;
            rectTransform.localScale = _tilesResources.CellSizeBox;
            
            var result = new CellLayerView(spriteRenderer, rectTransform, _rectTransform, _cellOverlayPools);

            _views.Add(result);

            return result;
        }

        public void Release()
        {
            _views.ForEach(_ => _.Release());
        }

    }
}
