using System;

namespace BBB.Match3.Renderer
{
    [Flags]
    public enum CellLayerState : uint
    {
        None          = 0,
        Spawner       = 1, //1,
        Despawner     = 1 << 1, //2,
        BackOne       = 1 << 4, //16,
        BackDouble    = 1 << 5, //32,
        WallLeft      = 1 << 6, //64,
        WallTop       = 1 << 7, //128,
        WallRight     = 1 << 8, //256,
        WallBottom    = 1 << 9, //512,
        Petal         = 1 << 14, //16384
        Water         = 1 << 15, //32768
        FlagEnd       = 1 << 16, //65536
        // States:
        AssistMarker  = 1 << 11, //2048,
        Ivy           = 1 << 12, //4096
        Tnt           = 1 << 13, //8192
    }
}