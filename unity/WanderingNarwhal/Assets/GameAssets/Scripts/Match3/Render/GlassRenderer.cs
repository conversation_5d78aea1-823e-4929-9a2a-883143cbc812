using System.Collections.Generic;
using BBB.DI;
using DG.Tweening;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Match3.Renderer
{
    public class GlassRenderer
    {
        private readonly IGridController _gridController;
        private readonly M3Settings _m3Settings;
        private readonly Transform _cellOverlayContainer;
        private readonly Transform _tilesOverlayContainer;

        private readonly List<Image> _glassImagesList = new();
        private readonly Dictionary<Coords, Image> _glassImagesDict = new();
        private readonly TilesResources _tileResources;
        private GoPool _glassPool;
        private readonly Color _defaultColor = new(1, 1, 1, 0);

        public GlassRenderer(IContext context)
        {
            _tileResources = context.Resolve<TilesResources>();
            _gridController = context.Resolve<IGridController>();

            _m3Settings = context.Resolve<M3Settings>();
            var containers = context.Resolve<RendererContainers>();
            _cellOverlayContainer = containers.FloorsOverlay;
            _tilesOverlayContainer = containers.TilesOverlay;
        }

        public void SetGrid(Grid grid)
        {
            _glassPool ??= new GoPool(_tileResources.GlassImagePrefab, _tilesOverlayContainer, grid.Cells.Count);
            foreach (var cell in grid.Cells)
            {
                var go = _glassPool.Spawn();
                go.SetActive(false);
                var tf = go.GetComponent<RectTransform>();
                tf.localPosition = _gridController.ToLocalPosition(cell.Coords);
                tf.sizeDelta = _tileResources.CellSize;
                var image = go.GetComponent<Image>();
                image.color = _defaultColor;
                _glassImagesList.Add(image);
                _glassImagesDict.Add(cell.Coords, image);
            }
        }

        public void Clear(bool cleanPool = false)
        {
            foreach (var (coords, image) in _glassImagesDict)
            {
                if (image == null) continue;

                image.color = _defaultColor;
                MoveGlassFrontInternal(image, coords);

                if (!cleanPool)
                {
                    _glassPool.Release(image.gameObject);
                }
                else
                {
                    Object.Destroy(image.gameObject);
                }
            }

            _glassImagesList.Clear();
            _glassImagesDict.Clear();

            if (cleanPool)
            {
                _glassPool.Cleanup();
            }
        }

        public void MoveGlassBehind(Coords coords)
        {
            if (_glassImagesDict.TryGetValue(coords, out var foundImage))
            {
                foundImage.transform.SetParent(_cellOverlayContainer, false);
                var localPosition = _gridController.ToLocalPosition(coords);
                foundImage.GetComponent<RectTransform>().localPosition = localPosition;
            }
        }
        
        private void MoveGlassFrontInternal(Image image, Coords coords)
        {
            image.transform.SetParent(_tilesOverlayContainer, false);
            var localPosition = _gridController.ToLocalPosition(coords);
            image.GetComponent<RectTransform>().localPosition = localPosition;
        }

        public void MoveGlassToFront(Coords coords)
        {
            if (_glassImagesDict.TryGetValue(coords, out var foundImage))
            {
                MoveGlassFrontInternal(foundImage, coords);
            }
        }

        public void Dim(bool value, float dimTimeOverride)
        {
            var targetColor = value ? _m3Settings.BoardDimColor : _defaultColor;

            foreach (var image in _glassImagesList)
            {
                if (value)
                    image.gameObject.SetActive(true);

                var workingImage = image;
                var dimTime = dimTimeOverride < 0f ? _m3Settings.DimTime : dimTimeOverride;
                image.DOColor(targetColor, dimTime)
                    .SetEase(Ease.InOutCubic)
                    .OnComplete(() =>
                    {
                        if (!value)
                            workingImage.gameObject.SetActive(false);
                    });
            }
        }
    }
}