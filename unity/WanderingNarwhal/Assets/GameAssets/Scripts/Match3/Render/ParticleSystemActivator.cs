using BBB;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Render
{
    public class ParticleSystemActivator : BbbMonoBehaviour
    {
        [SerializeField] private ParticleSystem _particleSystem;
        [SerializeField] private bool _includeChildren;
        [Tooltip("-1 means infinite duration (don't stop by timer)")]
        [SerializeField] private float _duration = -1f;
        
        protected override void OnEnable()
        {
            base.OnEnable();
            _particleSystem.gameObject.SetActive(true);
            _particleSystem.Play(_includeChildren);
            if (_duration > 0f)
            {
                Invoke(nameof(StopParticleSystem), _duration);
            }
        }
        
        private void StopParticleSystem()
        {
            if (_particleSystem != null)
            {
                _particleSystem.Stop(_includeChildren);
            }
        }
    }
}