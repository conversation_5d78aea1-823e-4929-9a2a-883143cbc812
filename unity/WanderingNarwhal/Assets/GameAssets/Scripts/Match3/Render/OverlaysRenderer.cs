using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.GameAssets.Scripts.Match3.Render;
using BBB.MMVibrations;
using BBB.MMVibrations.Plugins;
using BBB.UI;
using BBB.UI.Level;
using Cysharp.Threading.Tasks;
using UnityEngine;

public class OverlaysRenderer : BbbMonoBehaviour, IContextInitializable, IContextReleasable
{
    [SerializeField] private Transform[] _overlayLayers;
    private IMatch3SharedResourceProvider _match3ResourceProvider;
    private IVibrationsWrapper _vibrations;
    private readonly Dictionary<ContainerType, Transform> _containerToLayerMap = new();
    private readonly Dictionary<string, OverlayEffect> _loadedAssetsDict = new();
    private string _cachedOverlayName;

    private void InitLayerMap()
    {
        if (_containerToLayerMap.Count != 0) return;
        _containerToLayerMap.Add(ContainerType.OverlayFx0, _overlayLayers[0]);
        _containerToLayerMap.Add(ContainerType.OverlayFx1, _overlayLayers[1]);
        _containerToLayerMap.Add(ContainerType.OverlayFx2, _overlayLayers[2]);
    }

    public void InitializeByContext(IContext context)
    {
        _match3ResourceProvider = context.Resolve<IMatch3SharedResourceProvider>();
        _vibrations = context.Resolve<IVibrationsWrapper>();
        InitLayerMap();
        foreach (var effect in _loadedAssetsDict.Values)
        {
            effect.StopAndClear();
        }
    }

    public Transform GetOverlayLayer(ContainerType containerType)
    {
        InitLayerMap();
        if (_containerToLayerMap.TryGetValue(containerType, out var result))
        {
            return result;
        }

        BDebug.LogError(LogCat.Match3, $"Overlay layer not found for {containerType}");
        return transform;
    }

    public void LoadOverlay(string overlayKey)
    {
        if (_loadedAssetsDict.ContainsKey(overlayKey)) return;
        var prefab = _match3ResourceProvider.GetPrefab(overlayKey);
        if (prefab == null) return;
        var overlayEffect = SpawnOverlayEffect(prefab);
        overlayEffect.StopAndClear();
        _loadedAssetsDict.Add(overlayKey, overlayEffect);
    }

    public void LoadAndLaunchOverlay(string overlayName, Action<OverlayEffect> onLaunched)
    {
        BDebug.Log(LogCat.Vibration,
            $"Playing haptic feedback for button {gameObject.name} -- Vibrations available={_vibrations != null}");
        _vibrations?.PlayHaptic(ImpactPreset.MediumImpact);
        if (_loadedAssetsDict.TryGetValue(overlayName, out var effect1))
        {
            effect1.Launch();
            onLaunched.SafeInvoke(effect1);
            return;
        }

        _match3ResourceProvider.CacheAndLoadAsync<GameObject>(this, overlayName).ContinueWith(asset =>
        {
            var effect = SpawnOverlayEffect(asset);
            effect.Launch();
            _loadedAssetsDict.Add(overlayName, effect);
            onLaunched.SafeInvoke(effect);
        });
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();

        ReleaseLoadedResources();
    }

    private void ReleaseLoadedResources()
    {
        foreach (var overlayName in _loadedAssetsDict.Keys)
        {
            _match3ResourceProvider?.ReleaseCached(overlayName);
        }
    }

    private OverlayEffect SpawnOverlayEffect(GameObject prefab)
    {
        UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{prefab.name}]");
        var go = Instantiate(prefab, transform, false);
        UnityEngine.Profiling.Profiler.EndSample();
        var ctrl = go.GetComponent<OverlayEffect>();

        if (ctrl == null)
            throw new InvalidOperationException($"Prefab {prefab.name} does not have OverlayEffect script");

        return ctrl;
    }

    public void ReleaseByContext(IContext context)
    {

    }
}
