using System.Collections.Generic;

namespace BBB
{
    public class Node<TValue>
    {
        public readonly TValue Value;

        public readonly List<Node<TValue>> Childs;

        public Node(TValue value)
        {
            Value = value;
            Childs = new List<Node<TValue>>();
        }

        public Node(TValue value, List<Node<TValue>> childs)
        {
            Childs = childs;
            Value = value;
        }
    }
}