using System.Collections.Generic;

namespace BBB.Match3.Renderer
{
    /// <summary>
    /// System that handles notification of tile views about near matches and some other events.
    /// Every turn after matching destroying tiles via matches it will trigger feedback in neighbour tiles around.
    /// </summary>
    /// <remarks>
    /// Currently used only for Ivy tile, which should react to near matches and when any tile fallen into cell with <PERSON>.
    /// </remarks>
    public class TileFeedbackNotificationHandler
    {
        /// <summary>
        /// Cache of cells for visual feedback;
        /// </summary>
        private readonly HashSet<Coords> _cellsWithJustEnteredTiles = new HashSet<Coords>();

        /// <summary>
        /// Cache of cells for visual feedback of ivy;
        /// </summary>
        private readonly HashSet<Coords> _cellsNearMatchedTiles = new HashSet<Coords>();

        public void ClearCaches()
        {
            _cellsNearMatchedTiles.Clear();
            _cellsWithJustEnteredTiles.Clear();
        }

        public void RegisterTileHasEnteredCell(Coords cellCoord)
        {
            if (!_cellsWithJustEnteredTiles.Contains(cellCoord))
            {
                _cellsWithJustEnteredTiles.Add(cellCoord);
            }
        }

        public void NotifyNearCellsAboutMatch(Grid grid, Coords centerPos)
        {
            int dx = 0;
            int dy = 0;
            for (int i = 0; i < 8; i++)
            {
                // All 8 directions. At distance 1.
                dx = i < 4 ? (i == 0 ? 0 : 1) : (i == 4 ? 0 : -1);
                dy = (i == 2 || i == 6) ? 0 : (i > 2 && i < 6 ? -1 : 1);
                var c = new Coords(centerPos.X + dx, centerPos.Y + dy);

                Cell cell;
                if (!_cellsNearMatchedTiles.Contains(c) && grid.TryGetCell(c, out cell))
                {
                    _cellsNearMatchedTiles.Add(c);
                }
            }

            dx = 2;
            dy = 0;
            // Additional 4 points at distance 2.
            for (int i = 0; i < 4; i++)
            {
                var c = new Coords(centerPos.X + dx, centerPos.Y + dy);
                Cell cell;
                if (!_cellsNearMatchedTiles.Contains(c) && grid.TryGetCell(c, out cell))
                {
                    _cellsNearMatchedTiles.Add(c);
                }

                var tmp = dx;
                dx = dy;
                dy = -tmp;
            }
        }

        public void TriggerCachedNotifications(ICellController cellController)
        {
            cellController.NotifyCellsAboutEnteredTile(_cellsWithJustEnteredTiles);
            cellController.NotifyCellsAboutNearMatchedTiles(_cellsNearMatchedTiles);
        }
    }
}