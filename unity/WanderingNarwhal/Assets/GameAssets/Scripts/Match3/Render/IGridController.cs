using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public interface ICoordsConverter
    {
        Vector2 ToDisplacedLocalPosition(Coords coords);
        Vector2 ToLocalPosition(Coords coords);
        Vector2 ToLocalPosition(Vector2 coords);
        Vector2 ToDisplacedWorldPosition(Vector2 vec2);
        Vector2 ToWorldPosition(Vector2 vec2);
    }
    public interface IGridController : ICoordsConverter
    {
        RectTransform Transform { get; }
        RectTransform RootBoardTransform { get; }
        RectTransform PerimeterTransform { get; }
        RectTransform OverlayTransform { get; }
        RectTransform HintRectTransform { get; }
        RectTransform RainOverlayTransform { get; }
        void DimBoard(bool value, float dimTimeOverride = -1f);
        bool IsSwappableCell(Coords coord);
        bool HasTile(Coords coord);
        Tile GetTile(Coords coord);
        bool HasSwappableTile(Coords coords);
        void DoTweenShakeLevelCamera(int hashCode, float overrideDuration = -1f, ShakeSettingsType shakeSettings = ShakeSettingsType.ClearBoard);
        void DoTweenShakeBoard(int hashCode, float overrideDuration = -1f, ShakeSettingsType shakeSettings = ShakeSettingsType.ClearBoard);
        void SetupGrid(Grid grid);
        void SetupTransformSizes();
        Coords GetOutGridCoords(Vector3 pointer);
        Vector2 GetOutGridVector2(Vector3 pointer);
        Coords GetGridCoords(Vector3 pointer);
        Vector2 GetGridLocalPoint(Vector3 pointer);
        Vector2 GetLowestPosition();
        Vector2 GetCenterPosition();
        Vector2 GetCurrentGridSize();
        void Clear(bool cleanupPools = false);
        void ForceUpdateGridTiles();
        void ForceUpdatePerimeter();
        void RevealGrid(bool instantReveal, int revealMovesChange);
        void AddRevealObserver(IBoardRevealObserver observer);
        void MoveGlassBehind(Coords targetCoords);
        void MoveGlassToFront(Coords targetCoords);
        void OnSkip();
        bool IsWaterState(Coords coords);
        bool IsPartOfShelfGroup(Coords coords, int shelfGroupIdentifier);
    }
}