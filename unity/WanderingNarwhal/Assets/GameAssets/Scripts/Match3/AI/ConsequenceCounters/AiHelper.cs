using System.Collections.Generic;
using BBB.CellTypes;
using GameAssets.Scripts.Match3.Logic;

#if UNITY_EDITOR
namespace BBB.Match3.Systems
{
    public static class AiHelper
    {
        public static IEnumerable<GoalType> GetGoalForConsequence(ConsequenceType consequenceType)
        {
            switch (consequenceType)
            {
                case ConsequenceType.TileKindGoal:
                    yield return GoalType.Green;
                    yield return GoalType.Yellow;
                    yield return GoalType.Red;
                    yield return GoalType.Blue;
                    yield return GoalType.Orange;
                    yield return GoalType.Purple;
                    yield return GoalType.White;
                    break;
                case ConsequenceType.LitterGoal:
                    yield return GoalType.Litters;
                    break;
                case ConsequenceType.StickerGoal:
                    yield return GoalType.Stickers;
                    break;
                case ConsequenceType.BackgroundGoal:
                    yield return GoalType.Backgrounds;
                    break;
                case ConsequenceType.PetalGoal:
                    yield return GoalType.Petal;
                    break;
                case ConsequenceType.DestructibleWall:
                    yield return GoalType.DestructibleWall;
                    break;
                case ConsequenceType.IceCubeGoal:
                    yield return GoalType.IceCubes;
                    break;
                case ConsequenceType.ChainGoal:
                    yield return GoalType.Chains;
                    break;
                case ConsequenceType.SandGoal:
                    yield return GoalType.Sand;
                    break;
                case ConsequenceType.PinataGoal:
                    yield return GoalType.Pinata;
                    break;
                case ConsequenceType.AnimalGoal:
                    yield return GoalType.Animal;
                    break;
                case ConsequenceType.DropItemGoal:
                    yield return GoalType.DropItems;
                    break;
                case ConsequenceType.ColorCrateGoal:
                    yield return GoalType.ColorCrate;
                    break;
                case ConsequenceType.WatermelonGoal:
                    yield return GoalType.Watermelon;
                    break;
                case ConsequenceType.VaseGoal:
                    yield return GoalType.Vase;
                    break;
                case ConsequenceType.MoneyBagGoal:
                    yield return GoalType.MoneyBag;
                    break;
                case ConsequenceType.PenguinGoal:
                    yield return GoalType.Penguin;
                    break;
                case ConsequenceType.EggGoal:
                    yield return GoalType.Egg;
                    break;
                case ConsequenceType.BirdGoal:
                    yield return GoalType.Bird;
                    break;
                case ConsequenceType.BananaGoal:
                    yield return GoalType.Banana;
                    break;
                case ConsequenceType.SheepGoal:
                    yield return GoalType.Sheep;
                    break;
                case ConsequenceType.SkunkGoal:
                    yield return GoalType.Skunk;
                    break;
                case ConsequenceType.ChickenGoal:
                    yield return GoalType.Chicken;
                    break;
                case ConsequenceType.BeeGoal:
                    yield return GoalType.Bee;
                    break;
                case ConsequenceType.MoleGoal:
                    yield return GoalType.Mole;
                    break;
                case ConsequenceType.SquidGoal:
                    yield return GoalType.Squid;
                    break;
                case ConsequenceType.BowlingPin:
                    yield return GoalType.BowlingPin;
                    break;
                case ConsequenceType.BushGoal:
                    yield return GoalType.Bush;
                    break;
                case ConsequenceType.SodaBottle:
                    yield return GoalType.SodaBottle;
                    break;
                case ConsequenceType.SafeGoal:
                    yield return GoalType.Safe;    
                    break;
                case ConsequenceType.FlowerPotGoal:
                    yield return GoalType.FlowerPot;
                    break;
                case ConsequenceType.IceBarGoal:
                    yield return GoalType.IceBar;
                    break;
                case ConsequenceType.DynamiteStick:
                    yield return GoalType.DynamiteStick;
                    break;
                case ConsequenceType.GiantPinataGoal:
                    yield return GoalType.GiantPinata;
                    break;
                case ConsequenceType.MetalBarGoal:
                    yield return GoalType.MetalBar;
                    break;
                case ConsequenceType.Shelf:
                    yield return GoalType.Shelf;
                    break;
                case ConsequenceType.JellyFish:
                    yield return GoalType.JellyFish;
                    break;
                case ConsequenceType.GoldenScarab:
                    yield return GoalType.GoldenScarab;
                    break;
                case ConsequenceType.FireWorks:
                    yield return GoalType.FireWorks;
                    break;
                case ConsequenceType.SlotMachine:
                    yield return GoalType.SlotMachine;
                    break;
            }
        }

        public static IEnumerable<ConsequenceType> GetConsequencesForGoal(GoalType goalType)
        {
            switch (goalType)
            {
                case GoalType.Score:
                    yield return ConsequenceType.ScoreGoal;
                    break;
                case GoalType.Green:
                case GoalType.Yellow:
                case GoalType.Blue:
                case GoalType.Red:
                case GoalType.Purple:
                case GoalType.Orange:
                case GoalType.White:
                    yield return ConsequenceType.TileKindGoal;
                    break;
                case GoalType.Backgrounds:
                    yield return ConsequenceType.BackgroundGoal;
                    break;
                case GoalType.Petal:
                    yield return ConsequenceType.PetalGoal;
                    break;
                
                case GoalType.DestructibleWall:
                    yield return ConsequenceType.DestructibleWall;
                    break;
                case GoalType.DropItems:
                    yield return ConsequenceType.DropItemGoal;
                    break;
                case GoalType.Stickers:
                    yield return ConsequenceType.StickerGoal;
                    break;
                case GoalType.Litters:
                    yield return ConsequenceType.LitterGoal;
                    break;
                case GoalType.IceCubes:
                    yield return ConsequenceType.IceCubeGoal;
                    break;
                case GoalType.Chains:
                    yield return ConsequenceType.ChainGoal;
                    break;
                case GoalType.Sand:
                    yield return ConsequenceType.SandGoal;
                    break;
                case GoalType.Pinata:
                    yield return ConsequenceType.PinataGoal;
                    break;
                case GoalType.Animal:
                    yield return ConsequenceType.AnimalGoal;
                    break;
                case GoalType.ColorCrate:
                    yield return ConsequenceType.ColorCrateGoal;
                    break;
                case GoalType.Watermelon:
                    yield return ConsequenceType.WatermelonGoal;
                    break;
                case GoalType.Vase:
                    yield return ConsequenceType.VaseGoal;
                    break;
                case GoalType.MoneyBag:
                    yield return ConsequenceType.MoneyBagGoal;
                    break;
                case GoalType.Penguin:
                    yield return ConsequenceType.PenguinGoal;
                    break;
                case GoalType.Egg:
                    yield return ConsequenceType.EggGoal;
                    break;
                case GoalType.Bird:
                    yield return ConsequenceType.BirdGoal;
                    break;
                case GoalType.Banana:
                    yield return ConsequenceType.BananaGoal;
                    break;
                case GoalType.Sheep:
                    yield return ConsequenceType.SheepGoal;
                    break;
                case GoalType.Skunk:
                    yield return ConsequenceType.SkunkGoal;
                    break;
                case GoalType.Chicken:
                    yield return ConsequenceType.ChickenGoal;
                    break;
                case GoalType.Bee:
                    yield return ConsequenceType.BeeGoal;
                    break;
                case GoalType.Mole:
                    yield return ConsequenceType.MoleGoal;
                    break;
                case GoalType.Squid:
                    yield return ConsequenceType.SquidGoal;
                    break;
                case GoalType.BowlingPin:
                    yield return ConsequenceType.BowlingPin;
                    break;
                case GoalType.Bush:
                    yield return ConsequenceType.BushGoal;
                    break;
                case GoalType.SodaBottle:
                    yield return ConsequenceType.SodaBottle;
                    break;
                case GoalType.Safe:
                    yield return ConsequenceType.SafeGoal;
                    break;
                case GoalType.FlowerPot:
                    yield return ConsequenceType.FlowerPotGoal;
                    break;
                case GoalType.IceBar:
                    yield return ConsequenceType.IceBarGoal;
                    break;
                case GoalType.DynamiteStick:
                    yield return ConsequenceType.DynamiteStick;
                    break;
                case GoalType.GiantPinata:
                    yield return ConsequenceType.GiantPinataGoal;
                    break;
                case GoalType.MetalBar:
                    yield return ConsequenceType.MetalBarGoal;
                    break;
                case GoalType.Shelf:
                    yield return ConsequenceType.Shelf;
                    break;
                case GoalType.JellyFish:
                    yield return ConsequenceType.JellyFish;
                    break;
                case GoalType.GoldenScarab:
                    yield return ConsequenceType.GoldenScarab;
                    break;
                case GoalType.FireWorks:
                    yield return ConsequenceType.FireWorks;
                    break;
                case GoalType.SlotMachine:
                    yield return ConsequenceType.SlotMachine;
                    break;
            }
        }

        public static bool IsPinata(Tile tile)
        {
            return tile.Speciality == TileSpeciality.Pinata;
        }

        public static bool IsColorCrate(Tile tile)
        {
            return tile.Speciality == TileSpeciality.ColorCrate;
        }

        public static int StickerLayerCount(Tile tile)
        {
            return tile.Speciality == TileSpeciality.Sticker
                ? tile.GetParam(TileParamEnum.AdjacentHp)
                : 0;
        }

        public static int WatermelonLayerCount(Tile tile)
        {
            return tile.Speciality == TileSpeciality.Watermelon
                ? tile.GetParam(TileParamEnum.AdjacentHp)
                : 0;
        }

        public static int LitterCount(Tile tile)
        {
            return tile.Speciality == TileSpeciality.Litter ? 1 : 0;
        }

        public static int FrameCount(Tile tile)
        {
            return tile.Speciality == TileSpeciality.Frame ? tile.GetParam(TileParamEnum.AdjacentHp) : 0;
        }

        public static int IceCubeLayerCount(Tile tile)
        {
            return tile.IsAnyOf(TileState.IceCubeMod)
                ? tile.GetParam(TileParamEnum.IceLayerCount)
                : 0;
        }

        public static int VaseLayerCount(Tile tile)
        {
            return tile.IsAnyOf(TileState.VaseMod)
                ? tile.GetParam(TileParamEnum.VaseLayerCount)
                : 0;
        }

        public static int EggLayerCount(Tile tile)
        {
            return tile.IsAnyOf(TileState.EggMod)
                ? tile.GetParam(TileParamEnum.EggLayerCount)
                : 0;
        }
        
        public static int FlowerPotLayerCount(Tile tile)
        {
            return tile.IsAnyOf(TileState.FlowerPotMod)
                ? tile.GetParam(TileParamEnum.FlowerPotLayerCount)
                : 0;
        }


        public static int ChainLayerCount(Tile tile)
        {
            return tile.IsAnyOf(TileState.ChainMod)
                ? tile.GetParam(TileParamEnum. ChainLayerCount)
                : 0;
        }

        public static int ColorCrateCount(Tile tile)
        {
            return tile.Speciality == TileSpeciality.ColorCrate ? 1 : 0;
        }

        public static int IvyLayerCounter(Cell cell)
        {
            return cell.IsAnyOf(CellState.Ivy) ? cell.IvyCount : 0;
        }

        public static int SandCount(Tile tile)
        {
            return tile.IsAnyOf(TileState.SandMod) ? 1 : 0;
        }
    }
}
#endif
